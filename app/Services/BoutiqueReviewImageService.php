<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class BoutiqueReviewImageService
{
    /**
     * URL de base du projet admin_marchand_lorrelei
     */
    private string $adminBaseUrl;

    public function __construct()
    {
        $this->adminBaseUrl = rtrim(config('app.admin_base_url', 'http://127.0.0.1:8001'), '/');
    }

    /**
     * Upload des images de review boutique vers le projet admin_marchand_lorrelei
     *
     * @param array $images Array d'UploadedFile
     * @param string $marchandId ID du marchand
     * @return array Array contenant les informations des images uploadées
     * @throws \Exception
     */
    public function uploadBoutiqueReviewImages(array $images, string $marchandId): array
    {
        $uploadedImages = [];

        foreach ($images as $image) {
            try {
                $uploadedImage = $this->uploadSingleImage($image, $marchandId);
                $uploadedImages[] = $uploadedImage;
            } catch (\Exception $e) {
                Log::error('Erreur upload image review boutique', [
                    'marchand_id' => $marchandId,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        }

        return $uploadedImages;
    }

    /**
     * Upload une seule image vers admin_marchand_lorrelei
     *
     * @param UploadedFile $image
     * @param string $marchandId
     * @return array
     * @throws \Exception
     */
    private function uploadSingleImage(UploadedFile $image, string $marchandId): array
    {
        // Déterminer le dossier de stockage basé sur l'ID du marchand
        $marchandIdInt = (int)$marchandId;
        $folderPrefix = $marchandIdInt < 1000 ? '0' : substr((string)$marchandIdInt, 0, -3);

        // Préparer les données pour l'upload
        $response = Http::timeout(30)
            ->attach(
                'image',
                file_get_contents($image->getPathname()),
                $image->getClientOriginalName()
            )
            ->post($this->adminBaseUrl . '/api/boutique-reviews/upload-images', [
                'marchand_id' => $marchandId,
                'folder_prefix' => $folderPrefix,
            ]);

        if (!$response->successful()) {
            throw new \Exception('Erreur lors de l\'upload de l\'image: ' . $response->body());
        }

        $responseData = $response->json();

        return [
            'name' => $responseData['filename'],
            'folder' => $folderPrefix,
            'url' => $responseData['url'] ?? null
        ];
    }

    /**
     * Génère l'URL complète d'une image de review boutique
     *
     * @param string $filename
     * @param string $folder
     * @return string
     */
    public function getImageUrl(string $filename, string $folder): string
    {
        return $this->adminBaseUrl . "/images/boutique-reviews/{$folder}/{$filename}";
    }

    /**
     * Supprime une image de review boutique du serveur admin
     *
     * @param string $filename
     * @param string $folder
     * @return bool
     */
    public function deleteBoutiqueReviewImage(string $filename, string $folder): bool
    {
        try {
            $response = Http::timeout(30)
                ->delete($this->adminBaseUrl . '/api/boutique-reviews/delete-image', [
                    'filename' => $filename,
                    'folder' => $folder,
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Erreur suppression image review boutique', [
                'filename' => $filename,
                'folder' => $folder,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
