<?php

namespace App\Services;

use App\Models\CommandePrincipale;
use App\Models\SousCommandeVendeur;
use App\Models\Client;
use App\Models\Marchand;
use App\Models\User;
use App\Models\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Collection;
use Illuminate\Support\Carbon;
use Exception;

/**
 * Service de gestion des notifications
 * Gère l'envoi de notifications multi-canaux (email, SMS, push, etc.)
 */
class NotificationService
{
    /**
     * Types de notifications supportés
     */
    const TYPE_EMAIL = 'email';
    const TYPE_SMS = 'sms';
    const TYPE_PUSH = 'push';
    const TYPE_DATABASE = 'database';

    /**
     * Événements de notification
     */
    const EVENT_ORDER_CREATED = 'order_created';
    const EVENT_ORDER_CONFIRMED = 'order_confirmed';
    const EVENT_ORDER_SHIPPED = 'order_shipped';
    const EVENT_ORDER_DELIVERED = 'order_delivered';
    const EVENT_ORDER_CANCELLED = 'order_cancelled';
    const EVENT_PAYMENT_CONFIRMED = 'payment_confirmed';
    const EVENT_PAYMENT_FAILED = 'payment_failed';
    const EVENT_STOCK_LOW = 'stock_low';
    const EVENT_MERCHANT_NEW_ORDER = 'merchant_new_order';

    /**
     * Envoie une notification de création de commande au client
     *
     * @param CommandePrincipale $commande
     * @return array
     */
    public function notifyOrderCreated(CommandePrincipale $commande): array
    {
        try {
            $client = $commande->client;
            $user = $client->user;

            $data = [
                'commande_numero' => $commande->numero_commande,
                'montant_total' => $commande->montant_total_ttc,
                'nombre_articles' => $commande->nombre_articles,
                'date_commande' => $commande->date_commande->format('d/m/Y H:i'),
                'client_nom' => $user->name,
                'client_email' => $user->email
            ];

            $results = [];

            // Notification email au client
            $results['email_client'] = $this->sendEmailNotification(
                $user->email,
                'Confirmation de votre commande #' . $commande->numero_commande,
                'emails.client.order-confirmation',
                array_merge($data, [
                    'commande_id' => $commande->id
                ])
            );

            // Notification en base de données
            $results['database'] = $this->sendDatabaseNotification(
                $user,
                'Commande créée',
                'Votre commande #' . $commande->numero_commande . ' a été créée avec succès.',
                self::EVENT_ORDER_CREATED,
                array_merge($data, [
                    'action_url' => route('dashboard.order-details', $commande->id),
                    'priority' => 'medium'
                ]),
                $commande->id
            );

            // Notifier les marchands concernés
            $results['merchants'] = $this->notifyMerchantsNewOrder($commande);

            Log::info('Notifications envoyées pour création de commande', [
                'commande_id' => $commande->id,
                'results' => $results
            ]);

            return [
                'success' => true,
                'results' => $results
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors de l\'envoi des notifications de création de commande', [
                'commande_id' => $commande->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Notifie les marchands d'une nouvelle commande
     *
     * @param CommandePrincipale $commande
     * @return array
     */
    public function notifyMerchantsNewOrder(CommandePrincipale $commande): array
    {
        $results = [];

        foreach ($commande->sousCommandes as $sousCommande) {
            try {
                $marchand = $sousCommande->marchand;
                $user = $marchand->user;

                $data = [
                    'sous_commande_numero' => $sousCommande->numero_sous_commande,
                    'commande_principale_numero' => $commande->numero_commande,
                    'montant_sous_commande' => $sousCommande->montant_ttc,
                    'nombre_articles' => $sousCommande->nombre_articles,
                    'marchand_nom' => $marchand->nom_entreprise,
                    'client_nom' => $commande->client->user->name,
                    'date_commande' => $commande->date_commande->format('d/m/Y H:i')
                ];

                // Email au marchand
                $results[$marchand->id]['email'] = $this->sendEmailNotification(
                    $user->email,
                    'Nouvelle commande #' . $sousCommande->numero_sous_commande,
                    'emails.merchant.new-order',
                    array_merge($data, [
                        'commande_principale_id' => $commande->id
                    ])
                );

                // Notification en base
                $results[$marchand->id]['database'] = $this->sendDatabaseNotification(
                    $user,
                    'Nouvelle commande',
                    'Vous avez reçu une nouvelle commande #' . $sousCommande->numero_sous_commande,
                    self::EVENT_MERCHANT_NEW_ORDER,
                    array_merge($data, [
                        'action_url' => route('dashboard.order-details', $commande->id),
                        'priority' => 'high'
                    ]),
                    $commande->id,
                    $sousCommande->id
                );

            } catch (Exception $e) {
                Log::error('Erreur notification marchand', [
                    'marchand_id' => $marchand->id ?? 'unknown',
                    'sous_commande_id' => $sousCommande->id,
                    'error' => $e->getMessage()
                ]);

                $results[$marchand->id ?? 'unknown'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Notifie la confirmation de paiement
     *
     * @param CommandePrincipale $commande
     * @return array
     */
    public function notifyPaymentConfirmed(CommandePrincipale $commande): array
    {
        try {
            $client = $commande->client;
            $user = $client->user;

            $data = [
                'commande_numero' => $commande->numero_commande,
                'montant_total' => $commande->montant_total_ttc,
                'methode_paiement' => $commande->methode_paiement,
                'date_paiement' => $commande->date_paiement->format('d/m/Y H:i'),
                'client_nom' => $user->name
            ];

            $results = [];

            // Email de confirmation de paiement
            $results['email'] = $this->sendEmailNotification(
                $user->email,
                'Paiement confirmé pour votre commande #' . $commande->numero_commande,
                'emails.payment.confirmed',
                $data
            );

            // Notification en base
            $results['database'] = $this->sendDatabaseNotification(
                $user,
                'Paiement confirmé',
                'Le paiement de votre commande #' . $commande->numero_commande . ' a été confirmé.',
                self::EVENT_PAYMENT_CONFIRMED,
                $data
            );

            return [
                'success' => true,
                'results' => $results
            ];

        } catch (Exception $e) {
            Log::error('Erreur notification paiement confirmé', [
                'commande_id' => $commande->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Notifie l'expédition d'une sous-commande
     *
     * @param SousCommandeVendeur $sousCommande
     * @return array
     */
    public function notifyOrderShipped(SousCommandeVendeur $sousCommande): array
    {
        try {
            $commande = $sousCommande->commandePrincipale;
            $client = $commande->client;
            $user = $client->user;

            $data = [
                'sous_commande_numero' => $sousCommande->numero_sous_commande,
                'commande_numero' => $commande->numero_commande,
                'transporteur' => $sousCommande->transporteur,
                'numero_suivi' => $sousCommande->numero_suivi,
                'url_suivi' => $sousCommande->url_suivi,
                'date_expedition' => $sousCommande->date_expedition_reelle->format('d/m/Y H:i'),
                'marchand_nom' => $sousCommande->marchand->nom_entreprise,
                'client_nom' => $user->name
            ];

            $results = [];

            // Email d'expédition
            $results['email'] = $this->sendEmailNotification(
                $user->email,
                'Votre commande #' . $sousCommande->numero_sous_commande . ' a été expédiée',
                'emails.order.shipped',
                $data
            );

            // Notification en base
            $results['database'] = $this->sendDatabaseNotification(
                $user,
                'Commande expédiée',
                'Votre commande #' . $sousCommande->numero_sous_commande . ' a été expédiée par ' . $sousCommande->marchand->nom_entreprise,
                self::EVENT_ORDER_SHIPPED,
                $data
            );

            return [
                'success' => true,
                'results' => $results
            ];

        } catch (Exception $e) {
            Log::error('Erreur notification expédition', [
                'sous_commande_id' => $sousCommande->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Envoie une notification email
     *
     * @param string $email
     * @param string $subject
     * @param string $template
     * @param array $data
     * @return array
     */
    private function sendEmailNotification(string $email, string $subject, string $template, array $data): array
    {
        try {
            // Envoi réel d'emails avec nos templates
            Mail::send($template, $data, function ($message) use ($email, $subject) {
                $message->to($email)
                        ->subject($subject)
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info('Email envoyé avec succès', [
                'to' => $email,
                'subject' => $subject,
                'template' => $template
            ]);

            return [
                'success' => true,
                'type' => self::TYPE_EMAIL,
                'recipient' => $email,
                'template' => $template
            ];

        } catch (Exception $e) {
            Log::error('Erreur envoi email', [
                'email' => $email,
                'subject' => $subject,
                'template' => $template,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'type' => self::TYPE_EMAIL,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Envoie une notification en base de données
     *
     * @param User $user
     * @param string $title
     * @param string $message
     * @param string $type
     * @param array $data
     * @param string|null $relatedOrderId
     * @param string|null $relatedSuborderId
     * @return array
     */
    private function sendDatabaseNotification(
        User $user,
        string $title,
        string $message,
        string $type,
        array $data = [],
        ?string $relatedOrderId = null,
        ?string $relatedSuborderId = null
    ): array {
        try {
            // Créer la notification en base de données
            $notification = Notification::create([
                'type' => $type,
                'notifiable_type' => get_class($user),
                'notifiable_id' => $user->id,
                'data' => array_merge($data, [
                    'title' => $title,
                    'message' => $message,
                    'action_url' => $data['action_url'] ?? null,
                    'icon' => $data['icon'] ?? null,
                ]),
                'related_order_id' => $relatedOrderId,
                'related_suborder_id' => $relatedSuborderId,
                'priority' => $data['priority'] ?? 'medium',
                'channel' => 'database',
                'status' => 'sent',
            ]);

            Log::info('Notification créée en base de données', [
                'notification_id' => $notification->id,
                'user_id' => $user->id,
                'type' => $type,
                'title' => $title
            ]);

            return [
                'success' => true,
                'type' => self::TYPE_DATABASE,
                'notification_id' => $notification->id,
                'user_id' => $user->id
            ];

        } catch (Exception $e) {
            Log::error('Erreur notification base de données', [
                'user_id' => $user->id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'type' => self::TYPE_DATABASE,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Envoie une notification multi-canal
     *
     * @param mixed $notifiable
     * @param string $type
     * @param array $data
     * @param array $channels
     * @return array
     */
    public function sendMultiChannelNotification(
        $notifiable,
        string $type,
        array $data,
        array $channels = ['database', 'email']
    ): array {
        $results = [];

        foreach ($channels as $channel) {
            switch ($channel) {
                case 'database':
                    $results['database'] = $this->sendDatabaseNotification(
                        $notifiable,
                        $data['title'] ?? '',
                        $data['message'] ?? '',
                        $type,
                        $data,
                        $data['related_order_id'] ?? null,
                        $data['related_suborder_id'] ?? null
                    );
                    break;

                case 'email':
                    if (isset($data['email_template'])) {
                        $results['email'] = $this->sendEmailNotification(
                            $notifiable->email,
                            $data['email_subject'] ?? $data['title'] ?? '',
                            $data['email_template'],
                            $data
                        );
                    }
                    break;

                // TODO: Ajouter SMS et push notifications
            }
        }

        return $results;
    }

    /**
     * Récupère les notifications non lues d'un utilisateur
     *
     * @param User $user
     * @param int $limit
     * @return Collection
     */
    public function getUnreadNotifications(User $user, int $limit = 10): Collection
    {
        return Notification::where('notifiable_type', get_class($user))
            ->where('notifiable_id', $user->id)
            ->unread()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Compte les notifications non lues d'un utilisateur
     *
     * @param User $user
     * @return int
     */
    public function getUnreadCount(User $user): int
    {
        return Notification::where('notifiable_type', get_class($user))
            ->where('notifiable_id', $user->id)
            ->unread()
            ->count();
    }

    /**
     * Marque des notifications comme lues
     *
     * @param User $user
     * @param array $notificationIds
     * @return int
     */
    public function markAsRead(User $user, array $notificationIds): int
    {
        return Notification::where('notifiable_type', get_class($user))
            ->where('notifiable_id', $user->id)
            ->whereIn('id', $notificationIds)
            ->whereNull('read_at')
            ->update([
                'read_at' => Carbon::now(),
                'status' => 'read'
            ]);
    }

    /**
     * Marque toutes les notifications comme lues
     *
     * @param User $user
     * @return int
     */
    public function markAllAsRead(User $user): int
    {
        return Notification::where('notifiable_type', get_class($user))
            ->where('notifiable_id', $user->id)
            ->whereNull('read_at')
            ->update([
                'read_at' => Carbon::now(),
                'status' => 'read'
            ]);
    }

    /**
     * Nettoie les anciennes notifications
     *
     * @param int $daysOld
     * @return int
     */
    public function cleanupOldNotifications(int $daysOld = 90): int
    {
        return Notification::where('created_at', '<', Carbon::now()->subDays($daysOld))
            ->delete();
    }
}
