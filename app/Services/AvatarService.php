<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Support\Facades\Log;
class AvatarService
{
    private ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Upload et traite un avatar
     *
     * @param UploadedFile $file
     * @param string $type 'user' ou 'marchand'
     * @param int $entityId ID de l'utilisateur ou du marchand
     * @return string|null Le chemin de l'avatar ou null en cas d'erreur
     */
    public function uploadAvatar(UploadedFile $file, string $type, int $entityId): ?string
    {
        try {
            // Validation du fichier
            if (!$this->validateFile($file)) {
                return null;
            }

            // Génération du nom de fichier unique
            $filename = $this->generateFilename($type, $entityId, $file->getClientOriginalExtension());

            // Traitement de l'image
            $processedImage = $this->processImage($file);

            // Stockage du fichier
            $path = $this->storeImage($processedImage, $filename, $type);

            return $path;
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'upload d\'avatar: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Supprime un ancien avatar
     *
     * @param string|null $avatarPath
     * @return bool
     */
    public function deleteAvatar(?string $avatarPath): bool
    {
        if (!$avatarPath || !Storage::disk('public')->exists($avatarPath)) {
            return true;
        }

        try {
            return Storage::disk('public')->delete($avatarPath);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la suppression d\'avatar: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Génère l'URL complète de l'avatar
     *
     * @param string|null $avatarPath
     * @return string|null
     */
    public function getAvatarUrl(?string $avatarPath): ?string
    {
        if (!$avatarPath) {
            return null;
        }

        return Storage::disk('public')->url($avatarPath);
    }

    /**
     * Génère les initiales pour le fallback
     *
     * @param string $name
     * @return string
     */
    public function generateInitials(string $name): string
    {
        $words = explode(' ', trim($name));
        $initials = '';

        foreach (array_slice($words, 0, 2) as $word) {
            $initials .= strtoupper(substr($word, 0, 1));
        }

        return $initials ?: 'U';
    }

    /**
     * Valide le fichier uploadé
     *
     * @param UploadedFile $file
     * @return bool
     */
    private function validateFile(UploadedFile $file): bool
    {
        // Vérification de la taille (max 2MB)
        if ($file->getSize() > 2 * 1024 * 1024) {
            return false;
        }

        // Vérification du type MIME
        $allowedMimes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return false;
        }

        return true;
    }

    /**
     * Génère un nom de fichier unique
     *
     * @param string $type
     * @param int $entityId
     * @param string $extension
     * @return string
     */
    private function generateFilename(string $type, int $entityId, string $extension): string
    {
        $timestamp = now()->format('YmdHis');
        $random = Str::random(8);

        return "{$type}_{$entityId}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Traite l'image (redimensionnement et optimisation)
     *
     * @param UploadedFile $file
     * @return \Intervention\Image\Interfaces\ImageInterface
     */
    private function processImage(UploadedFile $file)
    {
        $image = $this->imageManager->read($file->getPathname());

        // Redimensionnement en carré de 300x300px
        $image->cover(300, 300);

        // Optimisation de la qualité
        return $image;
    }

    /**
     * Stocke l'image traitée
     *
     * @param \Intervention\Image\Interfaces\ImageInterface $image
     * @param string $filename
     * @param string $type
     * @return string
     */
    private function storeImage($image, string $filename, string $type): string
    {
        $directory = "avatars/{$type}";
        $path = "{$directory}/{$filename}";

        // Création du répertoire s'il n'existe pas
        Storage::disk('public')->makeDirectory($directory);

        // Sauvegarde de l'image
        Storage::disk('public')->put($path, $image->encode());

        return $path;
    }

    /**
     * Nettoie les avatars orphelins
     *
     * @return int Nombre de fichiers supprimés
     */
    public function cleanupOrphanedAvatars(): int
    {
        $deleted = 0;

        try {
            // Récupération de tous les avatars stockés
            $userAvatars = Storage::disk('public')->files('avatars/user');
            $marchandAvatars = Storage::disk('public')->files('avatars/marchand');

            // Vérification des avatars utilisateurs
            foreach ($userAvatars as $avatar) {
                if (!$this->isAvatarInUse($avatar, 'user')) {
                    Storage::disk('public')->delete($avatar);
                    $deleted++;
                }
            }

            // Vérification des avatars marchands
            foreach ($marchandAvatars as $avatar) {
                if (!$this->isAvatarInUse($avatar, 'marchand')) {
                    Storage::disk('public')->delete($avatar);
                    $deleted++;
                }
            }
        } catch (\Exception $e) {
            \Log::error('Erreur lors du nettoyage des avatars: ' . $e->getMessage());
        }

        return $deleted;
    }

    /**
     * Vérifie si un avatar est utilisé
     *
     * @param string $avatarPath
     * @param string $type
     * @return bool
     */
    private function isAvatarInUse(string $avatarPath, string $type): bool
    {
        if ($type === 'user') {
            return \App\Models\User::where('avatar', $avatarPath)->exists();
        } elseif ($type === 'marchand') {
            return \App\Models\Marchand::where('logo', $avatarPath)->exists();
        }

        return false;
    }
}
