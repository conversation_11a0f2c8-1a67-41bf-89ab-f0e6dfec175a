<?php

namespace App\Services;

use App\Models\CommandePrincipale;
use App\Models\SousCommandeVendeur;
use App\Models\ArticleCommande;
use App\Models\Client;
use App\Models\Produit;
use App\Models\Adresse;
use App\Services\StockManagementService;
use App\Services\NotificationService;
use App\Services\PaymentSplittingService;
use App\Services\CommissionCalculationService;
use App\Services\EscrowService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Service de gestion du processus de checkout
 * Orchestration de la création des commandes multi-marchands AVANT le paiement
 */
class CheckoutService
{
    /**
     * Service de gestion des stocks
     */
    protected StockManagementService $stockService;

    /**
     * Service de notifications
     */
    protected NotificationService $notificationService;

    /**
     * Service de fractionnement des paiements
     */
    protected PaymentSplittingService $paymentSplittingService;

    /**
     * Service de calcul des commissions
     */
    protected CommissionCalculationService $commissionService;

    /**
     * Service d'escrow
     */
    protected EscrowService $escrowService;

    /**
     * Constructeur
     */
    public function __construct(
        StockManagementService $stockService,
        NotificationService $notificationService,
        PaymentSplittingService $paymentSplittingService,
        CommissionCalculationService $commissionService,
        EscrowService $escrowService
    ) {
        $this->stockService = $stockService;
        $this->notificationService = $notificationService;
        $this->paymentSplittingService = $paymentSplittingService;
        $this->commissionService = $commissionService;
        $this->escrowService = $escrowService;
    }
    /**
     * Crée une commande principale et ses sous-commandes à partir du panier
     *
     * @param array $cartData Données du panier
     * @param int $clientId ID du client
     * @param int $adresseLivraisonId ID de l'adresse de livraison
     * @param int|null $adresseFacturationId ID de l'adresse de facturation (optionnel)
     * @param array $metadata Métadonnées supplémentaires
     * @return array Résultat avec commande_principale_id et sous_commandes
     * @throws Exception
     */
    public function createOrderFromCart(
        array $cartData,
        int $clientId,
        int $adresseLivraisonId,
        ?int $adresseFacturationId = null,
        array $metadata = []
    ): array {
        return DB::transaction(function () use (
            $cartData,
            $clientId,
            $adresseLivraisonId,
            $adresseFacturationId,
            $metadata
        ) {
            // 1. Valider les données du panier
            $this->validateCartData($cartData);

            // 2. Vérifier la disponibilité des stocks
            // Adapter le format pour StockManagementService
            $stockCheckData = array_map(function ($item) {
                return [
                    'id' => $item['product_id'],
                    'quantity' => $item['quantity']
                ];
            }, $cartData['items']);

            $stockCheck = $this->stockService->checkStockAvailability($stockCheckData);
            if (!$stockCheck['available']) {
                throw new Exception('Stock insuffisant pour certains produits: ' . json_encode($stockCheck['unavailable_items']));
            }

            // 3. Vérifier que le client existe
            $client = Client::findOrFail($clientId);

            // 4. Vérifier que les adresses existent et appartiennent au client
            $this->validateAddresses($adresseLivraisonId, $adresseFacturationId, $client->user_id);

            // 5. Décomposer le panier par marchand
            $cartByMerchant = $this->decomposeCartByMerchant($cartData);

            // 6. Calculer les totaux globaux
            $totals = $this->calculateGlobalTotals($cartByMerchant);

            // 7. Créer la commande principale
            $commandePrincipale = $this->createMainOrder(
                $client,
                $adresseLivraisonId,
                $adresseFacturationId,
                $totals,
                $metadata
            );

            // 8. Créer les sous-commandes par marchand
            $sousCommandes = [];
            foreach ($cartByMerchant as $marchandId => $merchantCart) {
                $sousCommande = $this->createMerchantSubOrder(
                    $commandePrincipale,
                    $marchandId,
                    $merchantCart
                );
                $sousCommandes[] = $sousCommande;
            }

            // 9. Mettre à jour les compteurs de la commande principale
            $this->updateMainOrderCounters($commandePrincipale, $sousCommandes);

            // 10. Réserver les stocks
            try {
                $stockReservation = $this->stockService->reserveStock($sousCommandes);
                Log::info('Stocks réservés avec succès', $stockReservation);
            } catch (Exception $e) {
                Log::error('Erreur lors de la réservation des stocks', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }

            // 11. Envoyer les notifications
            try {
                $notificationResult = $this->notificationService->notifyOrderCreated($commandePrincipale);
                Log::info('Notifications envoyées pour création de commande', $notificationResult);
            } catch (Exception $e) {
                Log::error('Erreur lors de l\'envoi des notifications', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'error' => $e->getMessage()
                ]);
                // On continue même si les notifications échouent
            }

            Log::info('Commande créée avec succès', [
                'commande_principale_id' => $commandePrincipale->id,
                'client_id' => $clientId,
                'nombre_sous_commandes' => count($sousCommandes),
                'montant_total' => $totals['montant_total_ttc']
            ]);

            return [
                'success' => true,
                'commande_principale' => $commandePrincipale,
                'sous_commandes' => $sousCommandes,
                'totals' => $totals
            ];
        });
    }

    /**
     * Valide les données du panier
     */
    private function validateCartData(array $cartData): void
    {
        if (empty($cartData['items'])) {
            throw new Exception('Le panier est vide');
        }

        foreach ($cartData['items'] as $item) {
            if (!isset($item['product_id'], $item['quantity'], $item['price'])) {
                throw new Exception('Données du panier invalides');
            }

            if ($item['quantity'] <= 0) {
                throw new Exception('Quantité invalide pour le produit ' . $item['product_id']);
            }

            // Vérifier que le produit existe et valider le prix
            $produit = Produit::find($item['product_id']);
            if (!$produit) {
                throw new Exception('Produit non trouvé : ' . $item['product_id']);
            }

            // Vérifier le stock
            if ($produit->stock < $item['quantity']) {
                throw new Exception('Stock insuffisant pour le produit : ' . $produit->nom);
            }

            // Vérifier que le prix du panier est valide (prix original ou prix effectif)
            $prixEffectif = $produit->effective_price;
            $prixOriginal = $produit->prix;

            // Accepter soit le prix effectif, soit le prix original (le système utilisera toujours le prix effectif)
            $prixValide = (abs($item['price'] - $prixEffectif) <= 0.01) || (abs($item['price'] - $prixOriginal) <= 0.01);

            if (!$prixValide) {
                throw new Exception("Prix invalide pour le produit {$produit->nom}. Prix attendu: {$prixEffectif} FCFA (effectif) ou {$prixOriginal} FCFA (original), prix reçu: {$item['price']} FCFA");
            }

            Log::info('Validation prix produit', [
                'produit_id' => $produit->id,
                'produit_nom' => $produit->nom,
                'prix_recu' => $item['price'],
                'prix_effectif' => $prixEffectif,
                'prix_original' => $prixOriginal,
                'prix_utilise' => $prixEffectif
            ]);
        }
    }

    /**
     * Valide les adresses
     */
    private function validateAddresses(int $adresseLivraisonId, ?int $adresseFacturationId, int $userId): void
    {
        $adresseLivraison = Adresse::where('id', $adresseLivraisonId)
            ->where('user_id', $userId)
            ->first();

        if (!$adresseLivraison) {
            throw new Exception('Adresse de livraison invalide');
        }

        if ($adresseFacturationId) {
            $adresseFacturation = Adresse::where('id', $adresseFacturationId)
                ->where('user_id', $userId)
                ->first();

            if (!$adresseFacturation) {
                throw new Exception('Adresse de facturation invalide');
            }
        }
    }

    /**
     * Décompose le panier par marchand
     */
    private function decomposeCartByMerchant(array $cartData): array
    {
        $cartByMerchant = [];

        foreach ($cartData['items'] as $item) {
            $produit = Produit::with('marchand')->findOrFail($item['product_id']);
            $marchandId = $produit->marchand_id;

            if (!isset($cartByMerchant[$marchandId])) {
                $cartByMerchant[$marchandId] = [
                    'marchand' => $produit->marchand,
                    'items' => [],
                    'frais_livraison' => $item['delivery_fees'] ?? 0,
                    'zone_livraison_id' => $item['zone_livraison_id'] ?? null
                ];
            }

            // Utiliser le prix effectif du produit (avec discount si applicable)
            $prixEffectif = $produit->effective_price;

            $cartByMerchant[$marchandId]['items'][] = [
                'produit' => $produit,
                'quantity' => $item['quantity'],
                'prix_unitaire' => $prixEffectif,
                'prix_original' => $produit->prix,
                'prix_discount' => $produit->discount_price,
                'is_on_discount' => $produit->is_on_discount,
                'discount_percentage' => $produit->discount_percentage,
                'variantes' => $item['variants'] ?? [],
                'delivery_info' => $item['delivery_info'] ?? null
            ];
        }

        return $cartByMerchant;
    }

    /**
     * Calcule les totaux globaux avec commissions réelles
     */
    private function calculateGlobalTotals(array $cartByMerchant): array
    {
        $montantTotalHT = 0;
        $montantTotalTTC = 0;
        $montantTaxes = 0;
        $montantCommissions = 0;
        $nombreArticles = 0;

        foreach ($cartByMerchant as $marchandId => $merchantCart) {
            $montantMarchandHT = 0;
            $montantMarchandTTC = 0;

            foreach ($merchantCart['items'] as $item) {
                $quantite = $item['quantity'];
                $prixUnitaire = $item['prix_unitaire'];
                $montantItem = $prixUnitaire * $quantite;

                // Pour l'instant, on considère que les prix incluent déjà la TVA
                $montantMarchandTTC += $montantItem;
                $montantMarchandHT += $montantItem; // À ajuster selon la logique TVA

                $nombreArticles += $quantite;
            }

            // Ajouter les frais de livraison
            $fraisLivraison = $merchantCart['frais_livraison'];
            $montantMarchandTTC += $fraisLivraison;
            $montantMarchandHT += $fraisLivraison;

            // Calculer la commission pour ce marchand en utilisant le vrai système
            $commissionMarchand = $this->calculateMerchantCommission($marchandId, $montantMarchandTTC);
            $montantCommissions += $commissionMarchand;

            $montantTotalHT += $montantMarchandHT;
            $montantTotalTTC += $montantMarchandTTC;
        }

        return [
            'montant_total_ht' => round($montantTotalHT, 2),
            'montant_total_ttc' => round($montantTotalTTC, 2),
            'montant_taxes' => round($montantTaxes, 2),
            'montant_commission_plateforme' => round($montantCommissions, 2),
            'nombre_articles_total' => $nombreArticles,
            'nombre_marchands' => count($cartByMerchant)
        ];
    }

    /**
     * Calcule la commission pour un marchand spécifique
     */
    private function calculateMerchantCommission(int $marchandId, float $montant): float
    {
        try {
            // Récupérer le marchand avec son abonnement
            $marchand = \App\Models\Marchand::with('abonnementActuel')->find($marchandId);

            if (!$marchand || !$marchand->abonnementActuel || !$marchand->abonnementActuel->estActif()) {
                // Utiliser l'abonnement gratuit par défaut (5-10%)
                $seuil = 50000; // 50,000 FCFA
                $tauxCommission = $montant <= $seuil ? 10.0 : 5.0; // Taux max pour petits montants
                return ($montant * $tauxCommission) / 100;
            }

            // Utiliser la méthode du modèle MarchandAbonnement
            return $marchand->abonnementActuel->calculerCommission($montant);

        } catch (Exception $e) {
            Log::warning('Erreur lors du calcul de commission pour marchand', [
                'marchand_id' => $marchandId,
                'montant' => $montant,
                'error' => $e->getMessage()
            ]);

            // Fallback : utiliser 5% par défaut
            return ($montant * 5.0) / 100;
        }
    }

    /**
     * Crée la commande principale
     */
    private function createMainOrder(
        Client $client,
        int $adresseLivraisonId,
        ?int $adresseFacturationId,
        array $totals,
        array $metadata
    ): CommandePrincipale {
        return CommandePrincipale::create([
            'client_id' => $client->id,
            'numero_commande' => CommandePrincipale::genererNumeroCommande(),
            'montant_total_ht' => $totals['montant_total_ht'],
            'montant_total_ttc' => $totals['montant_total_ttc'],
            'montant_commission_plateforme' => $totals['montant_commission_plateforme'],
            'montant_taxes' => $totals['montant_taxes'],
            'statut_global' => 'EnAttente',
            'statut_paiement' => 'EnAttente',
            'adresse_livraison_id' => $adresseLivraisonId,
            'adresse_facturation_id' => $adresseFacturationId,
            'date_commande' => now(),
            'nombre_articles_total' => $totals['nombre_articles_total'],
            'nombre_marchands' => $totals['nombre_marchands'],
            'devise' => 'FCFA',
            'metadata' => array_merge($metadata, [
                'created_from' => 'checkout_service',
                'user_agent' => request()->userAgent(),
                'ip_address' => request()->ip()
            ])
        ]);
    }

    /**
     * Crée une sous-commande pour un marchand
     */
    private function createMerchantSubOrder(
        CommandePrincipale $commandePrincipale,
        int $marchandId,
        array $merchantCart
    ): SousCommandeVendeur {
        // Calculer les totaux pour ce marchand
        $montantHT = 0;
        $montantTTC = 0;
        $nombreArticles = 0;

        foreach ($merchantCart['items'] as $item) {
            $montantItem = $item['prix_unitaire'] * $item['quantity'];
            $montantHT += $montantItem;
            $montantTTC += $montantItem;
            $nombreArticles += $item['quantity'];
        }

        // Ajouter les frais de livraison
        $fraisLivraison = $merchantCart['frais_livraison'];
        $montantTTC += $fraisLivraison;

        // Calculer la commission avec le vrai système
        $montantCommission = $this->calculateMerchantCommission($marchandId, $montantTTC);
        $tauxCommission = $montantTTC > 0 ? ($montantCommission / $montantTTC) * 100 : 0;
        $montantVersementMarchand = $montantTTC - $montantCommission;

        // Créer la sous-commande
        $sousCommande = SousCommandeVendeur::create([
            'commande_principale_id' => $commandePrincipale->id,
            'marchand_id' => $marchandId,
            'numero_sous_commande' => SousCommandeVendeur::genererNumeroSousCommande(
                $commandePrincipale->id,
                $marchandId
            ),
            'montant_ht' => round($montantHT, 2),
            'montant_ttc' => round($montantTTC, 2),
            'montant_commission' => round($montantCommission, 2),
            'montant_versement_marchand' => round($montantVersementMarchand, 2),
            'taux_commission' => $tauxCommission,
            'statut' => 'EnAttente',
            'frais_livraison' => $fraisLivraison,
            'nombre_articles' => $nombreArticles,
            'zone_livraison_id' => $merchantCart['zone_livraison_id'],
            'date_creation' => now()
        ]);

        // Créer les articles de commande
        foreach ($merchantCart['items'] as $item) {
            ArticleCommande::create([
                'commande_id' => null, // Pas de commande legacy
                'sous_commande_id' => $sousCommande->id,
                'produit_id' => $item['produit']->id,
                'quantite' => $item['quantity'],
                'prixUnitaire' => $item['prix_unitaire'], // Prix effectif (avec discount)
                'prix_unitaire_ht' => $item['prix_unitaire'],
                'prix_unitaire_ttc' => $item['prix_unitaire'],
                'montant_total_ht' => $item['prix_unitaire'] * $item['quantity'],
                'montant_total_ttc' => $item['prix_unitaire'] * $item['quantity'],
                'nom_produit_commande' => $item['produit']->nom,
                'description_produit_commande' => $item['produit']->description,
                'statut_article' => 'en_attente',
                'variantes_produit' => array_merge($item['variantes'], [
                    'prix_original' => $item['prix_original'],
                    'prix_discount' => $item['prix_discount'] ?? null,
                    'is_on_discount' => $item['is_on_discount'],
                    'discount_percentage' => $item['discount_percentage'] ?? 0,
                    'savings_amount' => $item['is_on_discount'] ? ($item['prix_original'] - $item['prix_unitaire']) * $item['quantity'] : 0
                ])
            ]);
        }

        return $sousCommande;
    }

    /**
     * Met à jour les compteurs de la commande principale
     */
    private function updateMainOrderCounters(CommandePrincipale $commandePrincipale, array $sousCommandes): void
    {
        $commandePrincipale->update([
            'nombre_sous_commandes' => count($sousCommandes)
        ]);
    }

    /**
     * Confirme le paiement et met à jour les statuts
     */
    public function confirmPayment(int $commandePrincipaleId, array $paymentData): void
    {
        DB::transaction(function () use ($commandePrincipaleId, $paymentData) {
            $commandePrincipale = CommandePrincipale::with('sousCommandes')->findOrFail($commandePrincipaleId);

            $commandePrincipale->update([
                'statut_global' => 'PayementConfirme',
                'statut_paiement' => 'Complété',
                'methode_paiement' => $paymentData['method'] ?? null,
                'reference_paiement_externe' => $paymentData['transaction_id'] ?? null,
                'date_paiement' => now(),
                'date_confirmation' => now()
            ]);

            // Mettre à jour les sous-commandes
            foreach ($commandePrincipale->sousCommandes as $sousCommande) {
                $sousCommande->update([
                    'statut' => 'Confirmé',
                    'date_confirmation_marchand' => now()
                ]);
            }

            // Placer les fonds en escrow au lieu de fractionnement immédiat
            try {
                $escrowResult = $this->escrowService->holdFundsInEscrow(
                    $commandePrincipale,
                    $paymentData
                );

                Log::info('Fonds placés en escrow', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'escrow_transaction_id' => $escrowResult['escrow_transaction']->id,
                    'montant_held' => $escrowResult['montant_held'],
                    'payment_method' => $paymentData['method']
                ]);
            } catch (Exception $e) {
                Log::error('Erreur lors de la mise en escrow', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'error' => $e->getMessage()
                ]);
                // On continue même si l'escrow échoue pour ne pas bloquer la confirmation
                // Dans ce cas, on peut faire un fractionnement direct en fallback
                try {
                    $splittingResult = $this->paymentSplittingService->processPaymentSplitting(
                        $commandePrincipale,
                        $paymentData
                    );

                    Log::warning('Fallback vers fractionnement direct (escrow échoué)', [
                        'commande_principale_id' => $commandePrincipale->id,
                        'versements_crees' => count($splittingResult['data']['versements_crees'])
                    ]);
                } catch (Exception $fallbackError) {
                    Log::error('Échec du fallback fractionnement', [
                        'commande_principale_id' => $commandePrincipale->id,
                        'error' => $fallbackError->getMessage()
                    ]);
                }
            }

            // Envoyer les notifications de confirmation de paiement
            try {
                $notificationResult = $this->notificationService->notifyPaymentConfirmed($commandePrincipale);
                Log::info('Notifications de paiement confirmé envoyées', $notificationResult);
            } catch (Exception $e) {
                Log::error('Erreur lors de l\'envoi des notifications de paiement', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'error' => $e->getMessage()
                ]);
            }

            Log::info('Paiement confirmé', [
                'commande_principale_id' => $commandePrincipaleId,
                'transaction_id' => $paymentData['transaction_id'] ?? null
            ]);
        });
    }

    /**
     * Annule une commande et libère les stocks
     *
     * @param int $commandePrincipaleId
     * @param string $reason
     * @return array
     * @throws Exception
     */
    public function cancelOrder(int $commandePrincipaleId, string $reason = 'Échec du paiement'): array
    {
        return DB::transaction(function () use ($commandePrincipaleId, $reason) {
            $commandePrincipale = CommandePrincipale::findOrFail($commandePrincipaleId);

            // Vérifier si la commande peut être annulée
            if (!in_array($commandePrincipale->statut_global, ['EnAttente', 'PayementConfirme'])) {
                throw new Exception('Cette commande ne peut plus être annulée (statut: ' . $commandePrincipale->statut_global . ')');
            }

            // Libérer les stocks
            try {
                $stockRelease = $this->stockService->releaseStock($commandePrincipale);
                Log::info('Stocks libérés lors de l\'annulation', $stockRelease);
            } catch (Exception $e) {
                Log::error('Erreur lors de la libération des stocks', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'error' => $e->getMessage()
                ]);
                // On continue même si la libération échoue pour éviter de bloquer l'annulation
            }

            $commandePrincipale->update([
                'statut_global' => 'Annulé',
                'statut_paiement' => 'Échoué',
                'date_annulation' => now(),
                'raison_annulation' => $reason
            ]);

            // Annuler les sous-commandes
            foreach ($commandePrincipale->sousCommandes as $sousCommande) {
                $sousCommande->changerStatut('Annulé', 'Commande annulée: ' . $reason);
            }

            Log::info('Commande annulée avec succès', [
                'commande_principale_id' => $commandePrincipaleId,
                'reason' => $reason
            ]);

            return [
                'success' => true,
                'commande_principale' => $commandePrincipale,
                'message' => 'Commande annulée avec succès'
            ];
        });
    }
}
