<?php

namespace App\Services;

use App\Models\CommandePrincipale;
use App\Models\SousCommandeVendeur;
use App\Models\Versement;
use App\Models\Marchand;
use App\Services\CommissionCalculationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Exception;

/**
 * Service de fractionnement des paiements
 * Gère la répartition des paiements entre marchands après déduction des commissions
 */
class PaymentSplittingService
{
    protected CommissionCalculationService $commissionService;

    public function __construct(CommissionCalculationService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    /**
     * Traite le fractionnement d'un paiement pour une commande principale
     *
     * @param CommandePrincipale $commandePrincipale
     * @param array $paymentData Données du paiement (transaction_id, method, etc.)
     * @return array
     */
    public function processPaymentSplitting(CommandePrincipale $commandePrincipale, array $paymentData): array
    {
        return DB::transaction(function () use ($commandePrincipale, $paymentData) {
            try {
                // 1. Récupérer toutes les sous-commandes
                $sousCommandes = $commandePrincipale->sousCommandes()->with(['marchand'])->get();

                if ($sousCommandes->isEmpty()) {
                    throw new Exception('Aucune sous-commande trouvée pour cette commande principale');
                }

                // 2. Calculer les commissions pour chaque sous-commande
                $commissionsResult = $this->commissionService->calculateCommissionsForOrder($sousCommandes);

                if (!$commissionsResult['success']) {
                    throw new Exception('Erreur lors du calcul des commissions: ' . implode(', ', $commissionsResult['resume']['erreurs']));
                }

                // 3. Créer les versements en attente pour chaque marchand
                $versements = [];
                foreach ($commissionsResult['commissions_par_marchand'] as $commissionData) {
                    $versement = $this->createPendingPayout($commissionData, $paymentData, $commandePrincipale);
                    $versements[] = $versement;
                }

                // 4. Enregistrer les informations de fractionnement
                $this->recordPaymentSplitting($commandePrincipale, $commissionsResult, $paymentData);

                Log::info('Fractionnement de paiement traité avec succès', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'nombre_marchands' => count($versements),
                    'total_commissions' => $commissionsResult['resume']['total_commissions'],
                    'total_versements' => $commissionsResult['resume']['total_montant_net_marchands']
                ]);

                return [
                    'success' => true,
                    'data' => [
                        'commande_principale_id' => $commandePrincipale->id,
                        'versements_crees' => $versements,
                        'commissions_calculees' => $commissionsResult['commissions_par_marchand'],
                        'resume' => $commissionsResult['resume']
                    ]
                ];

            } catch (Exception $e) {
                Log::error('Erreur lors du fractionnement de paiement', [
                    'commande_principale_id' => $commandePrincipale->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }
        });
    }

    /**
     * Crée un versement en attente pour un marchand
     *
     * @param array $commissionData
     * @param array $paymentData
     * @param CommandePrincipale $commandePrincipale
     * @return array
     */
    private function createPendingPayout(array $commissionData, array $paymentData, CommandePrincipale $commandePrincipale): array
    {
        // Générer une référence unique pour le versement
        $reference = 'VER-' . $commandePrincipale->numero_commande . '-' . $commissionData['marchand_id'] . '-' . time();

        // Déterminer le type de versement selon les préférences du marchand
        $marchand = Marchand::find($commissionData['marchand_id']);
        $timingVersement = $marchand->timing_versements ?? 'hebdomadaire';

        $versementData = [
            'marchand_id' => $commissionData['marchand_id'],
            'reference_versement' => $reference,
            'montant_brut' => $commissionData['montant_net_marchand'],
            'frais_transaction' => 0, // Sera calculé lors du traitement
            'montant_net' => $commissionData['montant_net_marchand'], // Sera ajusté après calcul des frais
            'devise' => 'FCFA',
            'statut' => 'EnAttente',
            'methode_versement' => $marchand->methode_versement_preferee ?? 'virement_bancaire',
            'date_demande' => now(),
            'type_versement' => $timingVersement,
            'frequence' => $this->getFrequenceFromTiming($timingVersement),
            'nombre_commandes' => 1,
            'montant_commissions_deduites' => $commissionData['commission_montant'],
            'metadata' => [
                'commande_principale_id' => $commandePrincipale->id,
                'sous_commande_id' => $commissionData['sous_commande_id'],
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'payment_method' => $paymentData['method'] ?? 'paypal',
                'commission_details' => $commissionData['details'],
                'created_by_payment_splitting' => true
            ]
        ];

        // Créer le versement dans la base de données (admin_marchand_lorrelei)
        // Note: Ceci nécessitera une connexion à la base admin_marchand_lorrelei
        // Pour l'instant, on retourne les données pour traitement ultérieur

        return [
            'marchand_id' => $commissionData['marchand_id'],
            'marchand_nom' => $commissionData['marchand_nom'],
            'reference_versement' => $reference,
            'montant_net' => $commissionData['montant_net_marchand'],
            'commission_deduite' => $commissionData['commission_montant'],
            'timing_versement' => $timingVersement,
            'statut' => 'EnAttente',
            'versement_data' => $versementData
        ];
    }

    /**
     * Enregistre les informations de fractionnement dans la commande principale
     *
     * @param CommandePrincipale $commandePrincipale
     * @param array $commissionsResult
     * @param array $paymentData
     */
    private function recordPaymentSplitting(CommandePrincipale $commandePrincipale, array $commissionsResult, array $paymentData): void
    {
        // Mettre à jour les métadonnées de la commande principale
        $metadata = $commandePrincipale->metadata ?? [];
        $metadata['payment_splitting'] = [
            'processed_at' => now()->toISOString(),
            'transaction_id' => $paymentData['transaction_id'] ?? null,
            'payment_method' => $paymentData['method'] ?? 'paypal',
            'total_commissions' => $commissionsResult['resume']['total_commissions'],
            'total_versements' => $commissionsResult['resume']['total_montant_net_marchands'],
            'nombre_marchands' => $commissionsResult['resume']['nombre_marchands'],
            'commissions_par_marchand' => $commissionsResult['commissions_par_marchand']
        ];

        $commandePrincipale->update(['metadata' => $metadata]);
    }

    /**
     * Traite les versements éligibles selon leur timing
     *
     * @param string $timing ('immediat_apres_livraison', 'hebdomadaire', 'mensuel')
     * @return array
     */
    public function processScheduledPayouts(string $timing): array
    {
        try {
            // Récupérer les sous-commandes éligibles pour versement
            $sousCommandesEligibles = $this->getEligibleSubOrdersForPayout($timing);

            if ($sousCommandesEligibles->isEmpty()) {
                return [
                    'success' => true,
                    'message' => 'Aucune sous-commande éligible pour versement',
                    'timing' => $timing,
                    'versements_traites' => 0
                ];
            }

            $versementsTraites = [];
            $errors = [];

            // Grouper par marchand
            $sousCommandesParMarchand = $sousCommandesEligibles->groupBy('marchand_id');

            foreach ($sousCommandesParMarchand as $marchandId => $sousCommandes) {
                try {
                    $versement = $this->createGroupedPayout($marchandId, $sousCommandes, $timing);
                    $versementsTraites[] = $versement;
                } catch (Exception $e) {
                    $errors[] = [
                        'marchand_id' => $marchandId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return [
                'success' => empty($errors),
                'timing' => $timing,
                'versements_traites' => count($versementsTraites),
                'versements' => $versementsTraites,
                'erreurs' => $errors
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors du traitement des versements programmés', [
                'timing' => $timing,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Erreur lors du traitement: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Récupère les sous-commandes éligibles pour versement selon le timing
     *
     * @param string $timing
     * @return Collection
     */
    private function getEligibleSubOrdersForPayout(string $timing): Collection
    {
        $query = SousCommandeVendeur::with(['marchand', 'commandePrincipale'])
            ->where('statut', 'livre');

        // Filtrer selon le timing du marchand
        $query->whereHas('marchand', function ($q) use ($timing) {
            $q->where('timing_versements', $timing);
        });

        // Appliquer le délai de sécurité de 48h
        $delaiSecurite = now()->subHours(48);
        $query->where('date_livraison', '<=', $delaiSecurite)
            ->orWhere(function ($q) use ($delaiSecurite) {
                $q->whereNull('date_livraison')
                  ->where('updated_at', '<=', $delaiSecurite);
            });

        // Exclure celles qui ont déjà un versement traité
        $query->whereDoesntHave('versements', function ($q) {
            $q->whereIn('statut', ['Complété', 'EnCours']);
        });

        return $query->get();
    }

    /**
     * Crée un versement groupé pour un marchand
     *
     * @param int $marchandId
     * @param Collection $sousCommandes
     * @param string $timing
     * @return array
     */
    private function createGroupedPayout(int $marchandId, Collection $sousCommandes, string $timing): array
    {
        // Calculer les commissions pour toutes les sous-commandes
        $commissionsResult = $this->commissionService->calculateCommissionsForOrder($sousCommandes);

        if (!$commissionsResult['success']) {
            throw new Exception('Erreur lors du calcul des commissions groupées');
        }

        $marchand = $sousCommandes->first()->marchand;
        $reference = 'VER-GROUP-' . $marchandId . '-' . now()->format('YmdHis');

        $versementData = [
            'marchand_id' => $marchandId,
            'reference_versement' => $reference,
            'montant_brut' => $commissionsResult['resume']['total_montant_net_marchands'],
            'montant_commissions_deduites' => $commissionsResult['resume']['total_commissions'],
            'nombre_commandes' => $sousCommandes->count(),
            'type_versement' => $timing,
            'periode_debut' => $sousCommandes->min('created_at'),
            'periode_fin' => $sousCommandes->max('created_at'),
            'statut' => 'EnAttente',
            'metadata' => [
                'sous_commandes_ids' => $sousCommandes->pluck('id')->toArray(),
                'commissions_details' => $commissionsResult['commissions_par_marchand'],
                'created_by_scheduled_payout' => true
            ]
        ];

        return $versementData;
    }

    /**
     * Convertit le timing en fréquence
     *
     * @param string $timing
     * @return string
     */
    private function getFrequenceFromTiming(string $timing): string
    {
        return match($timing) {
            'immediat_apres_livraison' => 'immediat',
            'hebdomadaire' => 'hebdomadaire',
            'mensuel' => 'mensuel',
            default => 'hebdomadaire'
        };
    }
}
