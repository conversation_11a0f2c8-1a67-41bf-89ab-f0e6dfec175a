<?php

namespace App\Services;

use App\Models\MerchantValidation;
use App\Models\Marchand;
use App\Models\User;
use App\Events\MerchantSubmissionReceived;
use App\Events\MerchantApproved;
use App\Events\MerchantRejected;
use App\Events\MerchantValidationStarted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class MerchantValidationService
{
    /**
     * Approuve un marchand et lui donne accès au dashboard
     */
    public function approuverMarchand(int $validationId, int $validatorId): array
    {
        try {
            DB::beginTransaction();

            $validation = MerchantValidation::findOrFail($validationId);
            
            if ($validation->status !== 'EN_ATTENTE_VALIDATION') {
                throw new Exception('Ce marchand ne peut pas être approuvé dans son état actuel');
            }

            // Mettre à jour le statut de validation
            $validation->update([
                'status' => MerchantValidation::STATUS_VALIDE,
                'validated_by' => $validatorId,
                'validated_at' => now(),
                'rejection_reason' => null,
                'rejected_at' => null,
            ]);

            // Créer ou mettre à jour le profil marchand
            $marchand = $this->creerOuMettreAJourMarchand($validation);

            // Mettre à jour le rôle de l'utilisateur
            $validation->user->update([
                'role' => 'Marchand',
                'is_active' => true,
            ]);

            // Assigner automatiquement le rôle owner au marchand
            $this->assignerRoleOwner($validation->user, $marchand);

            // Déclencher l'événement d'approbation
            event(new MerchantApproved($validation, $marchand));

            DB::commit();

            Log::info('Marchand approuvé avec succès', [
                'validation_id' => $validationId,
                'user_id' => $validation->user_id,
                'validator_id' => $validatorId,
                'marchand_id' => $marchand->id ?? null,
            ]);

            return [
                'success' => true,
                'message' => 'Marchand approuvé avec succès',
                'validation' => $validation,
                'marchand' => $marchand,
            ];

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Erreur lors de l\'approbation du marchand', [
                'validation_id' => $validationId,
                'validator_id' => $validatorId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Erreur lors de l\'approbation: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Rejette un marchand avec une raison
     */
    public function rejeterMarchand(int $validationId, int $validatorId, string $raison): array
    {
        try {
            DB::beginTransaction();

            $validation = MerchantValidation::findOrFail($validationId);
            
            if ($validation->status !== 'EN_ATTENTE_VALIDATION') {
                throw new Exception('Ce marchand ne peut pas être rejeté dans son état actuel');
            }

            // Mettre à jour le statut de validation
            $validation->update([
                'status' => MerchantValidation::STATUS_REJETE,
                'validated_by' => $validatorId,
                'rejected_at' => now(),
                'rejection_reason' => $raison,
                'validated_at' => null,
            ]);

            // Déclencher l'événement de rejet
            event(new MerchantRejected($validation, $raison));

            DB::commit();

            Log::info('Marchand rejeté', [
                'validation_id' => $validationId,
                'user_id' => $validation->user_id,
                'validator_id' => $validatorId,
                'reason' => $raison,
            ]);

            return [
                'success' => true,
                'message' => 'Marchand rejeté avec succès',
                'validation' => $validation,
            ];

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Erreur lors du rejet du marchand', [
                'validation_id' => $validationId,
                'validator_id' => $validatorId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Erreur lors du rejet: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Notifie les administrateurs d'une nouvelle soumission
     */
    public function notifierNouvelleSubmission(MerchantValidation $validation): void
    {
        try {
            // Déclencher l'événement de nouvelle soumission
            event(new MerchantSubmissionReceived($validation));

            Log::info('Notification de nouvelle soumission envoyée', [
                'validation_id' => $validation->id,
                'user_id' => $validation->user_id,
            ]);

        } catch (Exception $e) {
            Log::error('Erreur lors de l\'envoi de notification de soumission', [
                'validation_id' => $validation->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Démarre le processus de validation
     */
    public function demarrerValidation(int $validationId, int $validatorId): array
    {
        try {
            $validation = MerchantValidation::findOrFail($validationId);
            
            if ($validation->status !== 'EN_ATTENTE_VALIDATION') {
                throw new Exception('Cette validation ne peut pas être démarrée');
            }

            // Déclencher l'événement de début de validation
            event(new MerchantValidationStarted($validation, $validatorId));

            Log::info('Validation démarrée', [
                'validation_id' => $validationId,
                'validator_id' => $validatorId,
            ]);

            return [
                'success' => true,
                'message' => 'Validation démarrée',
                'validation' => $validation,
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors du démarrage de validation', [
                'validation_id' => $validationId,
                'validator_id' => $validatorId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Erreur lors du démarrage: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Crée ou met à jour le profil marchand basé sur les données de validation
     */
    private function creerOuMettreAJourMarchand(MerchantValidation $validation): ?Marchand
    {
        try {
            $personalInfo = $validation->personal_info ?? [];
            $billingInfo = $validation->billing_info ?? [];
            $storeInfo = $validation->store_info ?? [];
            $businessInfo = $validation->business_info ?? [];

            $marchandData = [
                'user_id' => $validation->user_id,
                'nomEntreprise' => $businessInfo['nomEntreprise'] ?? $storeInfo['nom_boutique'] ?? 'Boutique',
                'pays_business' => $businessInfo['pays_business'] ?? 'Non spécifié',
                'ville_business' => $businessInfo['ville_business'] ?? 'Non spécifié',
                'type_business' => $businessInfo['type_business'] ?? 'individuel',
                'description_business' => $businessInfo['description_business'] ?? $storeInfo['description_business'] ?? null,
                'telephone_principal' => $businessInfo['telephone_principal'] ?? $personalInfo['telephone'] ?? null,
                'email_business' => $businessInfo['email_business'] ?? null,
                'site_web' => $businessInfo['site_web'] ?? $storeInfo['site_web'] ?? null,
                'chiffre_affaires_estime' => $businessInfo['chiffre_affaires_estime'] ?? null,
                'nombre_employes' => $businessInfo['nombre_employes'] ?? null,
                'categories_produits' => $storeInfo['categories_produits'] ?? [],
                'methode_paiement_preferee' => $billingInfo['methode_paiement_preferee'] ?? 'rib',
                'nom_titulaire_compte' => $billingInfo['nom_titulaire_compte'] ?? null,
                'statut_validation' => 'valide',
                'etape_inscription' => 'termine',
                'date_validation' => now(),
                'accepte_conditions' => true,
                'accepte_newsletter' => true,
                'langue_preferee' => 'fr',
                'source_inscription' => 'seller_platform',
            ];

            // Chiffrer l'IBAN si fourni
            if (!empty($billingInfo['iban'])) {
                $marchandData['iban_crypte'] = encrypt($billingInfo['iban']);
            }

            $marchand = Marchand::updateOrCreate(
                ['user_id' => $validation->user_id],
                $marchandData
            );

            return $marchand;

        } catch (Exception $e) {
            Log::error('Erreur lors de la création/mise à jour du marchand', [
                'validation_id' => $validation->id,
                'user_id' => $validation->user_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Assigne automatiquement le rôle owner au marchand validé
     */
    private function assignerRoleOwner(\App\Models\User $user, \App\Models\Marchand $marchand): void
    {
        try {
            // Récupérer le rôle owner
            $ownerRole = \App\Models\MarchandRole::where('slug', 'owner')->first();

            if (!$ownerRole) {
                Log::warning('Rôle owner non trouvé pour l\'assignation automatique', [
                    'user_id' => $user->id,
                    'marchand_id' => $marchand->id,
                ]);
                return;
            }

            // Vérifier si l'utilisateur a déjà un MarchandUser pour ce marchand
            $existingMarchandUser = \App\Models\MarchandUser::where('user_id', $user->id)
                ->where('marchand_id', $marchand->id)
                ->first();

            if ($existingMarchandUser) {
                // Mettre à jour le rôle existant
                $existingMarchandUser->update([
                    'role_id' => $ownerRole->id,
                    'access_level' => 'owner',
                    'is_active' => true,
                ]);

                Log::info('Rôle owner mis à jour pour marchand validé', [
                    'user_id' => $user->id,
                    'marchand_id' => $marchand->id,
                    'marchand_user_id' => $existingMarchandUser->id,
                ]);
            } else {
                // Créer un nouveau MarchandUser avec le rôle owner
                $marchandUser = \App\Models\MarchandUser::create([
                    'user_id' => $user->id,
                    'marchand_id' => $marchand->id,
                    'role_id' => $ownerRole->id,
                    'access_level' => 'owner',
                    'is_active' => true,
                    'permissions' => [], // Le rôle owner a déjà toutes les permissions
                    'invited_by' => 1, // Système
                ]);

                Log::info('Rôle owner assigné automatiquement au marchand validé', [
                    'user_id' => $user->id,
                    'marchand_id' => $marchand->id,
                    'marchand_user_id' => $marchandUser->id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'assignation du rôle owner', [
                'user_id' => $user->id,
                'marchand_id' => $marchand->id,
                'error' => $e->getMessage(),
            ]);

            // Ne pas faire échouer la validation pour cette erreur
            // Le rôle pourra être assigné manuellement plus tard
        }
    }

    /**
     * Obtient les statistiques de validation
     */
    public function getStatistiquesValidation(): array
    {
        return [
            'en_attente' => MerchantValidation::where('status', 'EN_ATTENTE_VALIDATION')->count(),
            'approuves_ce_mois' => MerchantValidation::where('status', 'VALIDE')
                ->whereMonth('validated_at', now()->month)
                ->whereYear('validated_at', now()->year)
                ->count(),
            'rejetes_ce_mois' => MerchantValidation::where('status', 'REJETE')
                ->whereMonth('rejected_at', now()->month)
                ->whereYear('rejected_at', now()->year)
                ->count(),
            'total_soumissions' => MerchantValidation::whereIn('status', [
                'EN_ATTENTE_VALIDATION',
                'VALIDE',
                'REJETE'
            ])->count(),
        ];
    }
}
