<?php

namespace App\Policies;

use App\Models\Commande;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CommandePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['Admin', 'Marchand']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Commande $commande): bool
    {
        // Admin peut tout voir
        if ($user->role === 'Admin') {
            return true;
        }

        // Marchand ne peut voir que ses propres commandes
        if ($user->role === 'Marchand') {
            $marchandId = $user->marchands->first()->id ?? null;
            return $commande->marchand_id === $marchandId;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Seul l'admin peut créer des commandes manuellement
        return $user->role === 'Admin';
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Commande $commande): bool
    {
        // Admin peut tout modifier
        if ($user->role === 'Admin') {
            return true;
        }

        // Marchand ne peut modifier que ses propres commandes
        if ($user->role === 'Marchand') {
            $marchandId = $user->marchands->first()->id ?? null;
            return $commande->marchand_id === $marchandId;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Commande $commande): bool
    {
        // Seul l'admin peut supprimer des commandes
        return $user->role === 'Admin';
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Commande $commande): bool
    {
        return $user->role === 'Admin';
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Commande $commande): bool
    {
        return $user->role === 'Admin';
    }
}
