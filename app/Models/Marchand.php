<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Marchand extends Model
{
    use HasFactory;

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::creating(function (Marchand $marchand) {
            if (empty($marchand->slug)) {
                $marchand->slug = $marchand->generateUniqueSlug($marchand->nomEntreprise);
            }
        });

        static::updating(function (Marchand $marchand) {
            if ($marchand->isDirty('nomEntreprise') && empty($marchand->slug)) {
                $marchand->slug = $marchand->generateUniqueSlug($marchand->nomEntreprise);
            }
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'nomEntreprise',
        'logo',
        'slug',
        'adresse_id',
        'idFiscal',
        'banqueNom',
        'banqueNumeroCompte',
        // Nouvelles colonnes pour la plateforme seller
        'pays_business',
        'ville_business',
        'type_business',
        'statut_validation',
        'etape_inscription',
        'documents_soumis',
        'documents_requis',
        'date_soumission_documents',
        'date_validation',
        'commentaires_validation',
        'validateur_id',
        'telephone_principal',
        'telephone_secondaire',
        'email_business',
        'site_web',
        'methode_paiement_preferee',
        'iban_crypte',
        'nom_titulaire_compte',
        'numero_orange_money',
        'numero_mtn_money',
        'description_business',
        'categories_produits',
        'chiffre_affaires_estime',
        'nombre_employes',
        'accepte_conditions',
        'accepte_newsletter',
        'langue_preferee',
        'notifications_preferences',
        'source_inscription',
        'code_parrainage',
        'parrain_id',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'logo_url',
        'logo_data',
        'boutique_average_rating',
        'boutique_reviews_count',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function adresse(): BelongsTo
    {
        return $this->belongsTo(Adresse::class);
    }

    public function produits(): HasMany
    {
        return $this->hasMany(Produit::class);
    }

    /**
     * Get the boutique reviews for the marchand.
     */
    public function boutiqueReviews(): HasMany
    {
        return $this->hasMany(BoutiqueReview::class);
    }

    /**
     * Get the approved boutique reviews for the marchand.
     */
    public function approvedBoutiqueReviews(): HasMany
    {
        return $this->boutiqueReviews()->approved();
    }

    public function commandes(): HasMany
    {
        return $this->hasMany(Commande::class);
    }

    public function paiements(): HasMany
    {
        return $this->hasMany(Paiement::class);
    }

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'documents_soumis' => 'array',
        'documents_requis' => 'array',
        'categories_produits' => 'array',
        'notifications_preferences' => 'array',
        'date_soumission_documents' => 'datetime',
        'date_validation' => 'datetime',
        'chiffre_affaires_estime' => 'decimal:2',
        'accepte_conditions' => 'boolean',
        'accepte_newsletter' => 'boolean',
    ];

    /**
     * Relations pour la plateforme seller
     */

    /**
     * Validateur qui a validé le marchand
     */
    public function validateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validateur_id');
    }

    /**
     * Marchand parrain
     */
    public function parrain(): BelongsTo
    {
        return $this->belongsTo(Marchand::class, 'parrain_id');
    }

    /**
     * Marchands parrainés
     */
    public function filleuls(): HasMany
    {
        return $this->hasMany(Marchand::class, 'parrain_id');
    }

    /**
     * Abonnement actuel du marchand
     */
    public function abonnementActuel()
    {
        return $this->hasOne(MarchandAbonnement::class)->where('statut', 'actif')->latest();
    }

    /**
     * Tous les abonnements du marchand
     */
    public function abonnements(): HasMany
    {
        return $this->hasMany(MarchandAbonnement::class);
    }

    /**
     * Historique des abonnements
     */
    public function historiqueAbonnements(): HasMany
    {
        return $this->hasMany(MarchandAbonnementHistorique::class);
    }

    /**
     * Documents du marchand
     */
    public function documents(): HasMany
    {
        return $this->hasMany(MarchandDocument::class);
    }

    /**
     * Documents validés
     */
    public function documentsValides(): HasMany
    {
        return $this->hasMany(MarchandDocument::class)->where('statut_validation', 'valide');
    }

    /**
     * Documents en attente
     */
    public function documentsEnAttente(): HasMany
    {
        return $this->hasMany(MarchandDocument::class)->where('statut_validation', 'en_attente');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si le marchand est validé
     */
    public function estValide(): bool
    {
        return $this->statut_validation === 'valide';
    }

    /**
     * Vérifie si le marchand a un abonnement actif
     */
    public function aAbonnementActif(): bool
    {
        return $this->abonnementActuel()->exists();
    }

    /**
     * Obtient le type d'abonnement actuel
     */
    public function getTypeAbonnementActuel(): string
    {
        $abonnement = $this->abonnementActuel()->first();
        return $abonnement ? $abonnement->type_abonnement : 'gratuit';
    }

    /**
     * Vérifie si tous les documents obligatoires sont soumis
     */
    public function documentsObligatoiresComplets(): bool
    {
        $documentsRequis = $this->getDocumentsRequis();
        $documentsValides = $this->documentsValides()->pluck('type_document')->toArray();

        foreach ($documentsRequis as $typeDocument) {
            if (!in_array($typeDocument, $documentsValides)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Obtient la liste des documents requis selon le type de business
     */
    public function getDocumentsRequis(): array
    {
        $documentsBase = ['cni_recto', 'cni_verso', 'photo_avec_cni'];

        switch ($this->type_business) {
            case 'individuel':
                return array_merge($documentsBase, ['justificatif_domicile']);

            case 'entreprise':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'rib_bancaire',
                    'justificatif_domicile'
                ]);

            case 'cooperative':
                return array_merge($documentsBase, [
                    'recepisse_declaration',
                    'statuts_entreprise',
                    'rib_bancaire'
                ]);

            case 'grande_entreprise':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'bilan_comptable',
                    'rib_bancaire',
                    'declaration_fiscale'
                ]);

            default:
                return $documentsBase;
        }
    }

    /**
     * Génère un slug unique basé sur le nom de l'entreprise
     */
    public function generateUniqueSlug(string $nomEntreprise): string
    {
        $baseSlug = Str::slug($nomEntreprise);
        $slug = $baseSlug;
        $counter = 1;

        // Vérifier l'unicité du slug
        while (static::where('slug', $slug)->where('id', '!=', $this->id ?? 0)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the logo URL attribute.
     *
     * @return string|null
     */
    public function getLogoUrlAttribute(): ?string
    {
        if (!$this->logo) {
            return null;
        }

        // Si le logo contient déjà une URL complète, la retourner
        if (str_starts_with($this->logo, 'http')) {
            return $this->logo;
        }

        // Construire l'URL vers admin_marchand_lorrelei
        $adminBaseUrl = config('app.admin_base_url', 'http://127.0.0.1:8001');
        return rtrim($adminBaseUrl, '/') . '/storage/logos/' . $this->logo;
    }

    /**
     * Get the logo URL or generate initials.
     *
     * @return array
     */
    public function getLogoDataAttribute(): array
    {
        $logoUrl = $this->logo_url;

        if ($logoUrl) {
            return [
                'type' => 'image',
                'url' => $logoUrl,
                'initials' => $this->generateInitials()
            ];
        }

        return [
            'type' => 'initials',
            'url' => null,
            'initials' => $this->generateInitials()
        ];
    }

    /**
     * Generate marchand initials from company name.
     *
     * @return string
     */
    public function generateInitials(): string
    {
        $name = $this->nomEntreprise;
        $words = explode(' ', trim($name));
        $initials = '';

        foreach (array_slice($words, 0, 2) as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }

        return $initials ?: 'M';
    }

    /**
     * Get the average rating for the boutique.
     *
     * @return float
     */
    public function getBoutiqueAverageRatingAttribute(): float
    {
        return $this->approvedBoutiqueReviews()->avg('rating') ?? 0;
    }

    /**
     * Get the number of boutique reviews.
     *
     * @return int
     */
    public function getBoutiqueReviewsCountAttribute(): int
    {
        return $this->approvedBoutiqueReviews()->count();
    }

    /**
     * Get boutique rating statistics.
     *
     * @return array
     */
    public function getBoutiqueRatingStatsAttribute(): array
    {
        $reviews = $this->approvedBoutiqueReviews();

        return [
            'average' => round($reviews->avg('rating') ?? 0, 1),
            'total' => $reviews->count(),
            'distribution' => [
                5 => $reviews->where('rating', 5)->count(),
                4 => $reviews->where('rating', 4)->count(),
                3 => $reviews->where('rating', 3)->count(),
                2 => $reviews->where('rating', 2)->count(),
                1 => $reviews->where('rating', 1)->count(),
            ]
        ];
    }

    /**
     * Obtient l'URL de la boutique du marchand
     */
    public function getBoutiqueUrl(): string
    {
        return '/boutique/' . ($this->slug ?: $this->generateUniqueSlug($this->nomEntreprise));
    }

    /**
     * Obtient le slug du marchand (génère un si nécessaire)
     */
    public function getSlugAttribute($value): string
    {
        if (empty($value)) {
            $slug = $this->generateUniqueSlug($this->nomEntreprise);
            // Sauvegarder le slug généré
            $this->updateQuietly(['slug' => $slug]);
            return $slug;
        }
        return $value;
    }
}
