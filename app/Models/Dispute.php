<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Dispute extends Model
{
    use HasFactory, HasUuids;



    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'commande_principale_id',
        'sous_commande_id',
        'client_id',
        'marchand_id',
        'escrow_transaction_id',
        'numero_litige',
        'type_litige',
        'statut',
        'priorite',
        'sujet',
        'description',
        'solution_souhaitee',
        'montant_conteste',
        'montant_rembourse',
        'montant_compensation',
        'date_ouverture',
        'date_premiere_reponse',
        'date_resolution',
        'date_fermeture',
        'date_limite_reponse',
        'assigne_a',
        'resolu_par',
        'resolution_details',
        'notes_internes',
        'satisfaction_client',
        'commentaire_satisfaction',
        'pieces_jointes',
        'historique_statuts',
        'metadata',
        'urgent',
        'escalade_automatique',
        'notification_client',
        'notification_marchand',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_ouverture' => 'datetime',
        'date_premiere_reponse' => 'datetime',
        'date_resolution' => 'datetime',
        'date_fermeture' => 'datetime',
        'date_limite_reponse' => 'datetime',
        'montant_conteste' => 'decimal:2',
        'montant_rembourse' => 'decimal:2',
        'montant_compensation' => 'decimal:2',
        'pieces_jointes' => 'array',
        'historique_statuts' => 'array',
        'metadata' => 'array',
        'urgent' => 'boolean',
        'escalade_automatique' => 'boolean',
        'notification_client' => 'boolean',
        'notification_marchand' => 'boolean',
        'satisfaction_client' => 'integer',
    ];

    /**
     * Relation avec la commande principale
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class, 'commande_principale_id');
    }

    /**
     * Relation avec la sous-commande
     */
    public function sousCommande(): BelongsTo
    {
        return $this->belongsTo(SousCommandeVendeur::class, 'sous_commande_id');
    }

    /**
     * Relation avec le client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Relation avec le marchand (via admin_marchand_lorrelei)
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Marchand::class, 'marchand_id');
    }

    /**
     * Relation avec l'utilisateur assigné
     */
    public function assigneA(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigne_a');
    }

    /**
     * Relation avec l'utilisateur qui a résolu
     */
    public function resoluPar(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolu_par');
    }

    /**
     * Relation avec les messages du litige
     */
    public function messages(): HasMany
    {
        return $this->hasMany(DisputeMessage::class);
    }

    /**
     * Obtient le statut formaté
     */
    public function getStatutFormateAttribute(): string
    {
        return match($this->statut) {
            'ouvert' => 'Ouvert',
            'en_cours' => 'En cours',
            'attente_client' => 'Attente client',
            'attente_marchand' => 'Attente marchand',
            'escalade' => 'Escaladé',
            'resolu' => 'Résolu',
            'ferme' => 'Fermé',
            'annule' => 'Annulé',
            default => ucfirst($this->statut)
        };
    }

    /**
     * Obtient la couleur du statut
     */
    public function getStatutColorAttribute(): string
    {
        return match($this->statut) {
            'ouvert' => 'warning',
            'en_cours' => 'primary',
            'attente_client' => 'info',
            'attente_marchand' => 'info',
            'escalade' => 'danger',
            'resolu' => 'success',
            'ferme' => 'gray',
            'annule' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Obtient la priorité formatée
     */
    public function getPrioriteFormateAttribute(): string
    {
        return match($this->priorite) {
            'basse' => 'Basse',
            'normale' => 'Normale',
            'haute' => 'Haute',
            'critique' => 'Critique',
            default => ucfirst($this->priorite)
        };
    }

    /**
     * Obtient la couleur de la priorité
     */
    public function getPrioriteColorAttribute(): string
    {
        return match($this->priorite) {
            'basse' => 'gray',
            'normale' => 'primary',
            'haute' => 'warning',
            'critique' => 'danger',
            default => 'gray'
        };
    }

    /**
     * Obtient le type de litige formaté
     */
    public function getTypeLitigeFormateAttribute(): string
    {
        return match($this->type_litige) {
            'produit_non_conforme' => 'Produit non conforme',
            'produit_defectueux' => 'Produit défectueux',
            'livraison_retard' => 'Retard de livraison',
            'livraison_non_recue' => 'Livraison non reçue',
            'remboursement' => 'Demande de remboursement',
            'service_client' => 'Service client',
            'facturation' => 'Problème de facturation',
            'autre' => 'Autre',
            default => ucfirst($this->type_litige)
        };
    }

    /**
     * Vérifie si le litige est en retard
     */
    public function getEnRetardAttribute(): bool
    {
        return $this->date_limite_reponse && 
               $this->date_limite_reponse->isPast() && 
               !in_array($this->statut, ['resolu', 'ferme', 'annule']);
    }

    /**
     * Obtient le délai restant
     */
    public function getDelaiRestantAttribute(): ?string
    {
        if (!$this->date_limite_reponse || in_array($this->statut, ['resolu', 'ferme', 'annule'])) {
            return null;
        }

        $diff = now()->diffInHours($this->date_limite_reponse, false);
        
        if ($diff < 0) {
            return 'En retard de ' . abs($diff) . 'h';
        } elseif ($diff < 24) {
            return $diff . 'h restantes';
        } else {
            return ceil($diff / 24) . 'j restants';
        }
    }

    /**
     * Scope pour les litiges urgents
     */
    public function scopeUrgents($query)
    {
        return $query->where(function($q) {
            $q->where('urgent', true)
              ->orWhere('priorite', 'critique')
              ->orWhere('date_limite_reponse', '<=', now()->addHours(24));
        });
    }

    /**
     * Scope pour les litiges en attente de réponse admin
     */
    public function scopeEnAttenteAdmin($query)
    {
        return $query->whereIn('statut', ['ouvert', 'attente_client', 'escalade']);
    }

    /**
     * Scope pour les litiges assignés à un admin
     */
    public function scopeAssignesA($query, $adminId)
    {
        return $query->where('assigne_a', $adminId);
    }
}
