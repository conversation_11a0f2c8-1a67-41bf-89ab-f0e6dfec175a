<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductVariant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'produit_id',
        'sku',
        'prix_supplement',
        'stock',
        'images',
        'attributs',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'prix_supplement' => 'decimal:2',
        'stock' => 'integer',
        'images' => 'array',
        'attributs' => 'array',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['image_urls', 'processed_attributs'];

    /**
     * Get the product that owns the variant.
     */
    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Avant la création d'une variante, générer un SKU unique si aucun n'est fourni
        static::creating(function ($variant) {
            if (empty($variant->sku)) {
                $variant->sku = $variant->generateUniqueSku();
            }
        });
    }

    /**
     * Génère un SKU unique pour la variante.
     *
     * @return string
     */
    public function generateUniqueSku(): string
    {
        // Récupérer le produit parent
        $produit = $this->produit;

        if (!$produit) {
            // Si le produit parent n'est pas disponible, utiliser l'ID de produit fourni
            $produitId = $this->produit_id;
            $produit = Produit::find($produitId);
        }

        if (!$produit) {
            // Si toujours pas de produit, générer un SKU aléatoire
            return 'VAR-' . strtoupper(substr(uniqid(), -8));
        }

        // Utiliser le nom du produit pour générer un préfixe
        $prefix = substr(preg_replace('/[^A-Z0-9]/i', '', strtoupper($produit->nom)), 0, 3);

        // Compter le nombre de variantes existantes pour ce produit
        $variantCount = $produit->variants()->count() + 1;

        // Générer le SKU de base
        $sku = $prefix . '-' . $produit->id . '-' . $variantCount;

        // Vérifier si le SKU existe déjà
        $count = 1;
        $originalSku = $sku;

        while (static::where('sku', $sku)->exists()) {
            $sku = $originalSku . '-' . $count;
            $count++;
        }

        return $sku;
    }

    /**
     * Get the image URLs for the variant.
     */
    public function getImageUrlsAttribute(): array
    {
        if (empty($this->images)) {
            return [];
        }

        // Si images est une chaîne JSON, la décoder d'abord
        $images = $this->images;
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        return array_map(function ($image) {
            // Pour les variantes, utiliser un chemin spécifique avec l'ID du produit et de la variante
            $produitId = $this->produit_id;
            $variantId = $this->id;
            $folderPrefix = $produitId < 1000 ? '0' : substr((string)$produitId, 0, -3);
            $imagePath = "products/{$folderPrefix}/variants/{$variantId}/{$image}";

            return \App\Helpers\ImageUrlHelper::getImageUrl($imagePath, '');
        }, $images);
    }

    /**
     * Get the processed attributes with image paths corrected.
     */
    public function getProcessedAttributsAttribute(): array
    {
        if (empty($this->attributs)) {
            return [];
        }

        // Si attributs est une chaîne JSON, la décoder d'abord
        $attributs = $this->attributs;
        if (is_string($attributs)) {
            $attributs = json_decode($attributs, true) ?? [];
        }

        // Traiter chaque attribut
        return array_map(function ($attribut) {
            // Si c'est un attribut de type couleur avec une image
            if (isset($attribut['type']) && $attribut['type'] === 'couleur' &&
                isset($attribut['with_image']) && $attribut['with_image'] &&
                isset($attribut['color_image']) && !empty($attribut['color_image'])) {

                // Ajouter '/images/' au chemin de l'image si ce n'est pas déjà fait
                if (strpos($attribut['color_image'], 'http') !== 0 && strpos($attribut['color_image'], '/images/') !== 0) {
                    $attribut['color_image'] = '/images/' . $attribut['color_image'];
                }
            }

            return $attribut;
        }, $attributs);
    }
}
