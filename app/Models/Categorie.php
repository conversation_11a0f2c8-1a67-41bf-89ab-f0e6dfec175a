<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Categorie extends Model
{
    use HasFactory, HasTranslations;

    /**
     * Les attributs qui sont traduisibles.
     *
     * @var array
     */
    public $translatable = ['nom', 'description'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'nom',
        'slug',
        'categorie_parent_id',
        'niveau',
        'category_path',
        'description',
        'image_url',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['full_image_url', 'thumbnail_urls'];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'categorie_parent_id' => 'integer',
            'niveau' => 'integer',
        ];
    }

    /**
     * Generate a slug from the name.
     *
     * Note: Cette méthode est appelée avant que HasTranslations ne traite l'attribut,
     * donc nous devons vérifier si $value est un tableau (pour les traductions).
     */
    public function setNomAttribute($value)
    {
        // Si $value est un tableau (traductions), nous utilisons la version française pour le slug
        if (is_array($value) && isset($value['fr'])) {
            $nameForSlug = $value['fr'];
        } else {
            $nameForSlug = $value;
        }

        // Laisser HasTranslations gérer l'attribut nom
        $this->attributes['nom'] = is_array($value) ? json_encode($value) : $value;

        // Générer un slug si le modèle est nouveau ou si le slug est vide
        if (!$this->exists || empty($this->slug)) {
            $this->attributes['slug'] = $this->generateUniqueSlug($nameForSlug);
        }
    }

    /**
     * Generate a unique slug.
     *
     * @param string $name
     * @return string
     */
    protected function generateUniqueSlug($name)
    {
        if (empty($name)) {
            $name = 'categorie-' . uniqid();
        }

        $slug = \Illuminate\Support\Str::slug($name);
        $originalSlug = $slug;
        $count = 1;

        // Vérifier si le slug existe déjà
        while (static::where('slug', $slug)->exists()) {
            $slug = "{$originalSlug}-{$count}";
            $count++;
        }

        return $slug;
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        // Utiliser l'ID pour les routes Filament, le slug pour les routes frontend
        if (request()->is('admin/*') || request()->is('marchand/*')) {
            return 'id';
        }

        return 'slug';
    }

    /**
     * Get the full URL for the category image.
     *
     * @return string|null
     */
    public function getFullImageUrlAttribute(): ?string
    {
        if (empty($this->image_url)) {
            return null;
        }

        // Vérifier si l'image est une URL complète (commence par https)
        if (str_starts_with($this->image_url, 'https')) {
            return $this->image_url;
        }

        // Vérifier si l'image est déjà un chemin complet
        if (strpos($this->image_url, '/') !== false) {
            return url('/images/' . $this->image_url);
        }

        // Déterminer le dossier basé sur l'ID de la catégorie
        $folderPrefix = $this->id < 1000 ? '0' : substr((string)$this->id, 0, -3);
        return url("/images/categories/{$folderPrefix}/{$this->image_url}");
    }

    /**
     * Get the thumbnail URLs for the category image.
     *
     * @return array
     */
    public function getThumbnailUrlsAttribute(): array
    {
        if (empty($this->image_url)) {
            return [
                'small' => null,
                'medium' => null,
                'large' => null
            ];
        }

        // Construire le chemin de l'image pour le helper
        $imagePath = $this->image_url;

        // Si l'image n'est pas déjà un chemin complet, le construire
        if (strpos($this->image_url, '/') === false) {
            $folderPrefix = $this->id < 1000 ? '0' : substr((string)$this->id, 0, -3);
            $imagePath = "categories/{$folderPrefix}/{$this->image_url}";
        }

        return [
            'small' => \App\Helpers\ThumbnailHelper::getThumbnailUrl($imagePath, 'small'),
            'medium' => \App\Helpers\ThumbnailHelper::getThumbnailUrl($imagePath, 'medium'),
            'large' => \App\Helpers\ThumbnailHelper::getThumbnailUrl($imagePath, 'large')
        ];
    }

    // Cette relation est redondante avec categorieParent, nous pouvons la supprimer

    public function categorieParent(): BelongsTo
    {
        return $this->belongsTo(Categorie::class, 'categorie_parent_id');
    }

    public function sousCategories(): HasMany
    {
        return $this->hasMany(Categorie::class, 'categorie_parent_id');
    }

    public function produits(): HasMany
    {
        return $this->hasMany(Produit::class);
    }

    /**
     * Relation avec les guides de tailles.
     *
     * @return BelongsToMany
     */
    public function sizeGuides(): BelongsToMany
    {
        return $this->belongsToMany(SizeGuide::class, 'categorie_size_guide', 'categorie_id', 'size_guide_id')
            ->withTimestamps();
    }

    /**
     * Obtient le nom traduit de la catégorie.
     *
     * @return string
     */
    public function getTranslatedName(): string
    {
        $locale = app()->getLocale();

        try {
            return $this->getTranslation('nom', $locale);
        } catch (\Exception) {
            // Si la traduction n'est pas disponible, retourner la valeur brute
            return $this->nom;
        }
    }

    /**
     * Événements du modèle pour gérer automatiquement niveau et category_path
     */
    protected static function boot()
    {
        parent::boot();

        // Avant la sauvegarde, calculer le niveau et le chemin de la catégorie
        static::saving(function (Categorie $categorie) {
            // Si c'est une catégorie principale (sans parent)
            if (empty($categorie->categorie_parent_id)) {
                $categorie->niveau = 1;
                // Le chemin sera défini après la sauvegarde car nous avons besoin de l'ID
            } else {
                // Récupérer la catégorie parente
                $parent = Categorie::find($categorie->categorie_parent_id);
                if ($parent) {
                    // Le niveau est celui du parent + 1
                    $categorie->niveau = $parent->niveau + 1;
                } else {
                    // Si le parent n'existe pas (ce qui ne devrait pas arriver), définir le niveau à 1
                    $categorie->niveau = 1;
                }
            }
        });

        // Après la sauvegarde, mettre à jour le chemin de la catégorie
        static::saved(function (Categorie $categorie) {
            $path = '';

            // Si c'est une catégorie principale
            if (empty($categorie->categorie_parent_id)) {
                $path = (string) $categorie->id;
            } else {
                // Récupérer la catégorie parente
                $parent = Categorie::find($categorie->categorie_parent_id);
                if ($parent) {
                    $path = $parent->category_path . '/' . $categorie->id;
                } else {
                    // Si le parent n'existe pas, utiliser uniquement l'ID de la catégorie
                    $path = (string) $categorie->id;
                }
            }

            // Mettre à jour le chemin uniquement s'il a changé
            if ($categorie->category_path !== $path) {
                // Désactiver les événements pour éviter une boucle infinie
                Categorie::withoutEvents(function () use ($categorie, $path) {
                    $categorie->update(['category_path' => $path]);
                });

                // Mettre à jour les sous-catégories
                $categorie->updateChildrenPaths();
            }
        });
    }

    /**
     * Met à jour récursivement les chemins des sous-catégories
     */
    public function updateChildrenPaths()
    {
        // Récupérer toutes les sous-catégories directes
        $children = $this->sousCategories;

        foreach ($children as $child) {
            // Mettre à jour le chemin de la sous-catégorie
            $newPath = $this->category_path . '/' . $child->id;

            // Désactiver les événements pour éviter une boucle infinie
            Categorie::withoutEvents(function () use ($child, $newPath) {
                $child->update(['category_path' => $newPath]);
            });

            // Mettre à jour récursivement les sous-catégories de cette sous-catégorie
            $child->updateChildrenPaths();
        }
    }

    /**
     * Récupère toutes les sous-catégories à tous les niveaux
     */
    public function getAllChildren()
    {
        // Si le chemin de catégorie est défini
        if ($this->category_path) {
            // Rechercher toutes les catégories dont le chemin commence par le chemin actuel
            return Categorie::where('category_path', 'like', $this->category_path . '/%')->get();
        }

        return collect();
    }
}
