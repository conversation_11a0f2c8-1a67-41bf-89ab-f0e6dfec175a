<?php

namespace App\Models;

use App\Enums\AdminPermission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AdminRole extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'permissions',
        'is_system_role',
        'priority',
        'is_active',
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_system_role' => 'boolean',
        'is_active' => 'boolean',
        'priority' => 'integer',
    ];

    /**
     * Relation avec les utilisateurs admin
     */
    public function adminUsers(): HasMany
    {
        return $this->hasMany(AdminUser::class, 'role_id');
    }

    /**
     * Vérifier si le rôle a une permission spécifique
     */
    public function hasPermission(AdminPermission|string $permission): bool
    {
        $permissionValue = $permission instanceof AdminPermission ? $permission->value : $permission;
        return in_array($permissionValue, $this->permissions ?? []);
    }

    /**
     * Ajouter une permission au rôle
     */
    public function addPermission(AdminPermission|string $permission): void
    {
        $permissionValue = $permission instanceof AdminPermission ? $permission->value : $permission;
        $permissions = $this->permissions ?? [];
        
        if (!in_array($permissionValue, $permissions)) {
            $permissions[] = $permissionValue;
            $this->update(['permissions' => $permissions]);
        }
    }

    /**
     * Retirer une permission du rôle
     */
    public function removePermission(AdminPermission|string $permission): void
    {
        $permissionValue = $permission instanceof AdminPermission ? $permission->value : $permission;
        $permissions = $this->permissions ?? [];
        
        $permissions = array_filter($permissions, fn($p) => $p !== $permissionValue);
        $this->update(['permissions' => array_values($permissions)]);
    }

    /**
     * Obtenir les permissions formatées pour l'affichage
     */
    public function getFormattedPermissionsAttribute(): array
    {
        return collect($this->permissions ?? [])
            ->map(function ($permission) {
                $enum = AdminPermission::tryFrom($permission);
                return [
                    'value' => $permission,
                    'label' => $enum?->label() ?? $permission,
                    'category' => $enum?->category() ?? 'Autre',
                ];
            })
            ->groupBy('category')
            ->toArray();
    }

    /**
     * Scope pour les rôles actifs
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope pour les rôles système
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system_role', true);
    }

    /**
     * Scope pour les rôles non-système
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system_role', false);
    }

    /**
     * Scope ordonné par priorité
     */
    public function scopeOrdered($query)
    {
        return $query->orderByDesc('priority')->orderBy('name');
    }

    /**
     * Vérifier si le rôle peut être supprimé
     */
    public function canBeDeleted(): bool
    {
        return !$this->is_system_role && $this->adminUsers()->count() === 0;
    }

    /**
     * Obtenir le nombre d'utilisateurs assignés à ce rôle
     */
    public function getUsersCountAttribute(): int
    {
        return $this->adminUsers()->count();
    }
}
