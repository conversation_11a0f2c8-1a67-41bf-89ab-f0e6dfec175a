<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Models\Client;
use App\Models\User;
use App\Models\Marchand;

class DisputeMessage extends Model
{
    use HasFactory, HasUuids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'dispute_messages';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'dispute_id',
        'auteur_type',
        'auteur_id',
        'auteur_nom',
        'message',
        'type_message',
        'pieces_jointes',
        'interne',
        'lu_par_client',
        'lu_par_admin',
        'lu_par_marchand',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'pieces_jointes' => 'array',
        'interne' => 'boolean',
        'lu_par_client' => 'boolean',
        'lu_par_admin' => 'boolean',
        'lu_par_marchand' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'type_message' => 'message',
        'interne' => false,
        'lu_par_client' => false,
        'lu_par_admin' => false,
        'lu_par_marchand' => false,
    ];

    /**
     * Relation avec le litige
     */
    public function dispute(): BelongsTo
    {
        return $this->belongsTo(Dispute::class);
    }

    /**
     * Relation avec le client auteur
     */
    public function auteurClient(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'auteur_id')->where('auteur_type', 'client');
    }

    /**
     * Relation avec l'admin auteur
     */
    public function auteurAdmin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'auteur_id')->where('auteur_type', 'admin');
    }

    /**
     * Relation avec le marchand auteur
     */
    public function auteurMarchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class, 'auteur_id')->where('auteur_type', 'marchand');
    }

    /**
     * Obtient l'auteur selon le type
     */
    public function getAuteurAttribute()
    {
        return match($this->auteur_type) {
            'client' => $this->auteurClient,
            'admin' => $this->auteurAdmin,
            'marchand' => $this->auteurMarchand,
            'system' => null,
            default => null
        };
    }

    /**
     * Marque le message comme lu par un type d'utilisateur
     */
    public function marquerCommeLu(string $typeUtilisateur): void
    {
        $champ = match($typeUtilisateur) {
            'client' => 'lu_par_client',
            'admin' => 'lu_par_admin',
            'marchand' => 'lu_par_marchand',
            default => null
        };

        if ($champ) {
            $this->update([$champ => true]);
        }
    }

    /**
     * Vérifie si le message est lu par un type d'utilisateur
     */
    public function estLuPar(string $typeUtilisateur): bool
    {
        return match($typeUtilisateur) {
            'client' => $this->lu_par_client,
            'admin' => $this->lu_par_admin,
            'marchand' => $this->lu_par_marchand,
            default => false
        };
    }

    /**
     * Crée un message système automatique
     */
    public static function creerMessageSysteme(string $disputeId, string $message, array $metadata = []): self
    {
        return static::create([
            'dispute_id' => $disputeId,
            'auteur_type' => 'system',
            'auteur_nom' => 'Système',
            'message' => $message,
            'type_message' => 'system',
            'metadata' => $metadata,
            'lu_par_admin' => true, // Les messages système sont automatiquement marqués comme lus par admin
        ]);
    }

    /**
     * Crée un message de changement de statut
     */
    public static function creerMessageChangementStatut(
        string $disputeId,
        string $ancienStatut,
        string $nouveauStatut,
        ?int $userId = null,
        ?string $commentaire = null
    ): self {
        $message = "Statut changé de '{$ancienStatut}' vers '{$nouveauStatut}'";
        if ($commentaire) {
            $message .= "\nCommentaire: {$commentaire}";
        }

        return static::create([
            'dispute_id' => $disputeId,
            'auteur_type' => 'system',
            'auteur_id' => $userId,
            'auteur_nom' => $userId ? (User::find($userId)?->name ?? 'Admin') : 'Système',
            'message' => $message,
            'type_message' => 'changement_statut',
            'metadata' => [
                'ancien_statut' => $ancienStatut,
                'nouveau_statut' => $nouveauStatut,
                'commentaire' => $commentaire
            ],
            'lu_par_admin' => true,
        ]);
    }

    /**
     * Obtient le type de message formaté
     */
    public function getTypeMessageFormateAttribute(): string
    {
        return match($this->type_message) {
            'message' => 'Message',
            'changement_statut' => 'Changement de statut',
            'piece_jointe' => 'Pièce jointe',
            'solution_proposee' => 'Solution proposée',
            'escalade' => 'Escalade',
            'resolution' => 'Résolution',
            'system' => 'Système',
            default => 'Message'
        };
    }

    /**
     * Obtient l'icône selon le type de message
     */
    public function getIconeAttribute(): string
    {
        return match($this->type_message) {
            'message' => 'chat',
            'changement_statut' => 'refresh',
            'piece_jointe' => 'paperclip',
            'solution_proposee' => 'lightbulb',
            'escalade' => 'arrow-up',
            'resolution' => 'check-circle',
            'system' => 'settings',
            default => 'chat'
        };
    }

    /**
     * Scope pour les messages non internes
     */
    public function scopePublics($query)
    {
        return $query->where('interne', false);
    }

    /**
     * Scope pour les messages internes
     */
    public function scopeInternes($query)
    {
        return $query->where('interne', true);
    }

    /**
     * Scope pour les messages non lus par un type d'utilisateur
     */
    public function scopeNonLusPar($query, string $typeUtilisateur)
    {
        $champ = match($typeUtilisateur) {
            'client' => 'lu_par_client',
            'admin' => 'lu_par_admin',
            'marchand' => 'lu_par_marchand',
            default => null
        };

        if ($champ) {
            return $query->where($champ, false);
        }

        return $query;
    }
}
