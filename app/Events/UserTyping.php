<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserTyping implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $channelType;
    public string $channelId;
    public int $userId;
    public string $userName;
    public string $userType;
    public bool $isTyping;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $channelType,
        string $channelId,
        int $userId,
        string $userName,
        string $userType,
        bool $isTyping = true
    ) {
        $this->channelType = $channelType;
        $this->channelId = $channelId;
        $this->userId = $userId;
        $this->userName = $userName;
        $this->userType = $userType;
        $this->isTyping = $isTyping;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel($this->channelType . '.' . $this->channelId),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'user.typing';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'user_id' => $this->userId,
            'user_name' => $this->userName,
            'user_type' => $this->userType,
            'is_typing' => $this->isTyping,
            'channel_type' => $this->channelType,
            'channel_id' => $this->channelId,
            'timestamp' => now()->toISOString(),
        ];
    }
}
