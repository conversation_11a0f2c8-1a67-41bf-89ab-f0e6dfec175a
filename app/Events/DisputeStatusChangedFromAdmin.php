<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DisputeStatusChangedFromAdmin implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public array $disputeData;

    /**
     * Create a new event instance.
     */
    public function __construct(array $disputeData)
    {
        $this->disputeData = $disputeData;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('dispute.' . ($this->disputeData['id'] ?? 'unknown')),
            new PrivateChannel('user.' . ($this->disputeData['client_id'] ?? 'unknown')),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'dispute.status.changed.from.admin';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'dispute' => $this->disputeData,
            'timestamp' => now()->toISOString(),
        ];
    }
}
