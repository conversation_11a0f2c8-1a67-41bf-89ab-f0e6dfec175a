<?php

namespace App\Events;

use App\Models\DisputeMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DisputeMessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public DisputeMessage $message;

    /**
     * Create a new event instance.
     */
    public function __construct(DisputeMessage $message)
    {
        $this->message = $message;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('dispute.' . $this->message->dispute_id),
            new PrivateChannel('user.' . $this->message->dispute->client_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'dispute.message.sent';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        // Charger les relations nécessaires
        $this->message->load('dispute');
        
        // Récupérer les informations d'avatar
        $user = null;
        if ($this->message->auteur_type === 'client' && $this->message->auteur_id) {
            $client = \App\Models\Client::find($this->message->auteur_id);
            $user = $client?->user;
        } elseif ($this->message->auteur_type === 'admin' && $this->message->auteur_id) {
            $user = \App\Models\User::find($this->message->auteur_id);
        }

        return [
            'message' => [
                'id' => $this->message->id,
                'dispute_id' => $this->message->dispute_id,
                'auteur_type' => $this->message->auteur_type,
                'auteur_id' => $this->message->auteur_id,
                'auteur_nom' => $this->message->auteur_nom,
                'message' => $this->message->message,
                'type_message' => $this->message->type_message,
                'pieces_jointes' => $this->message->pieces_jointes ?? [],
                'created_at' => $this->message->created_at->toISOString(),
                'avatar_url' => $user?->avatar_url,
                'initials' => $user?->generateInitials() ?? strtoupper(substr($this->message->auteur_nom ?? 'U', 0, 2)),
            ],
            'dispute' => [
                'id' => $this->message->dispute->id,
                'client_id' => $this->message->dispute->client_id,
            ],
            'timestamp' => now()->toISOString(),
        ];
    }
}
