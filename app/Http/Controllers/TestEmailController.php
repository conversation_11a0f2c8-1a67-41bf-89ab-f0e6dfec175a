<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Notifications\VerifyEmailNotification;
use Illuminate\Support\Facades\Auth;

class TestEmailController extends Controller
{
    public function testEmail()
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json(['error' => 'Utilisateur non connecté'], 401);
            }

            Log::info('Test email - Début', [
                'user_id' => $user->id,
                'email' => $user->email,
                'mail_config' => [
                    'mailer' => config('mail.default'),
                    'host' => config('mail.mailers.smtp.host'),
                    'port' => config('mail.mailers.smtp.port'),
                    'encryption' => config('mail.mailers.smtp.encryption'),
                    'username' => config('mail.mailers.smtp.username'),
                ]
            ]);

            // Test 1: Email simple
            Mail::raw('Test email depuis Lorrelei', function ($message) use ($user) {
                $message->to($user->email)
                        ->subject('Test Email - Lorrelei')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info('Test email - Email simple envoyé');

            // Test 2: Notification de vérification
            $user->sendEmailVerificationNotification();

            Log::info('Test email - Notification de vérification envoyée');

            return response()->json([
                'success' => true,
                'message' => 'Emails de test envoyés',
                'user_email' => $user->email,
                'config' => [
                    'mailer' => config('mail.default'),
                    'host' => config('mail.mailers.smtp.host'),
                    'from' => config('mail.from.address'),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Test email - Erreur', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], 500);
        }
    }
}
