<?php

namespace App\Http\Controllers;

use App\Models\SouscriptionPlan;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WelcomeController extends Controller
{
    public function index()
    {
        $plan_test = SouscriptionPlan::all()->map(function ($plan){
            return [
                'features_fr' => $plan->getTranslation('features', 'fr'),
                'features_en' => $plan->getTranslation('features', 'en'),
            ];
        });
        $plans = SouscriptionPlan::query()
            ->where('status', 'active')
            ->orderBy('current_price')
            ->get()
            ->map(function ($plan) {
                // Récupérer les fonctionnalités et les transformer en tableau simple
                $features_fr = collect($plan->getTranslation('features', 'fr'))
                    ->map(function ($feature) {
                        return is_array($feature) && isset($feature['feature']) 
                            ? $feature['feature'] 
                            : $feature;
                    })
                    ->values()
                    ->all();
                $features_en = collect($plan->getTranslation('features', 'en'))
                    ->map(function ($feature) {
                        return is_array($feature) && isset($feature['feature']) 
                            ? $feature['feature'] 
                            : $feature;
                    })
                    ->values()
                    ->all();

                return [
                    'name' => [
                        'fr' => $plan->getTranslation('nom', 'fr'),
                        'en' => $plan->getTranslation('nom', 'en'),
                    ],
                    'price' => number_format($plan->current_price, 0, ',', ' ') . ' FCFA',
                    'period' => '/' . ($plan->periode === 'mois' ? 'mois' : 'an'),
                    'commission' => $plan->commission_range,
                    'features_fr' => $features_fr,
                    'features_en' => $features_en,
                    'popular' => $plan->popular,
                ];
            });

        return Inertia::render('welcome', [
            'pricingPlans' => $plans
        ]);
    }
} 