<?php

namespace App\Http\Controllers;

use App\Models\Marchand;
use App\Models\MarchandAbonnement;
use App\Models\MarchandDocument;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;

class SellerRegistrationController extends Controller
{
    /**
     * Affiche la page de bienvenue pour les nouveaux marchands
     */
    public function welcome()
    {
        $user = Auth::user();
        $marchand = $user->marchand;

        // Si le marchand existe et est validé, rediriger vers la page de connexion au dashboard
        if ($marchand && $marchand->statut_validation === 'valide') {
            return Inertia::render('Seller/LoginToDashboard', [
                'user' => $user,
                'marchand' => $marchand,
            ]);
        }

        return Inertia::render('SellerRegistration/Welcome', [
            'user' => $user,
        ]);
    }

    /**
     * Étape 1: Affiche le formulaire d'informations personnelles
     */
    public function information()
    {
        $user = Auth::user();

        // Vérifier si l'utilisateur a déjà un marchand
        $marchand = Marchand::where('user_id', $user->id)->first();

        return Inertia::render('SellerRegistration/SellerInformation', [
            'user' => $user,
            'marchand' => $marchand,
            'countries' => $this->getCountries(),
        ]);
    }

    /**
     * Étape 1: Sauvegarde les informations personnelles
     */
    public function storeInformation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'prenom' => 'required|string|max:255',
            'nom' => 'required|string|max:255',
            'pays_citoyennete' => 'required|string|max:255',
            'pays_naissance' => 'required|string|max:255',
            'date_naissance' => 'required|date|before:today',
            'type_piece_identite' => 'required|in:passport,national_id,driving_license',
            'numero_piece_identite' => 'nullable|string|max:255',
            'pays_emission_piece' => 'nullable|string|max:255',
            'adresse_ligne1' => 'nullable|string|max:255',
            'adresse_ligne2' => 'nullable|string|max:255',
            'ville' => 'nullable|string|max:255',
            'region' => 'nullable|string|max:255',
            'code_postal' => 'nullable|string|max:20',
            'pays_adresse' => 'nullable|string|max:255',
            'telephone_verification' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $data = $validator->validated();

            // Récupérer le marchand existant
            $marchand = Marchand::where('user_id', $user->id)->first();

            // Préparer les données de mise à jour
            $updateData = array_merge($data, [
                'nom_complet' => $data['prenom'] . ' ' . $data['nom']
            ]);

            // Si le marchand n'existe pas ou n'est pas validé, ajouter les champs d'inscription
            if (!$marchand || $marchand->statut_validation !== 'valide') {
                $updateData = array_merge($updateData, [
                    'etape_inscription' => 'billing',
                    'statut_validation' => 'en_attente',
                    'source_inscription' => 'seller_platform',
                    'langue_preferee' => 'fr'
                ]);
            }

            // Créer ou mettre à jour le marchand
            $marchand = Marchand::updateOrCreate(
                ['user_id' => $user->id],
                $updateData
            );

            DB::commit();

            // Si le marchand est validé, rediriger vers le tableau de bord
            if ($marchand->statut_validation === 'valide') {
                return redirect()->route('filament.marchand.pages.dashboard')
                    ->with('success', 'Vos informations ont été mises à jour avec succès');
            }

            // Sinon, continuer le processus d'inscription
            return redirect()->route('seller.billing');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Une erreur est survenue lors de la sauvegarde'])->withInput();
        }
    }

    /**
     * Étape 2: Affiche le formulaire de facturation
     */
    public function billing()
    {
        $user = Auth::user();

        // Vérifier si l'utilisateur a déjà un marchand
        $marchand = Marchand::where('user_id', $user->id)->first();

        return Inertia::render('SellerRegistration/Billing', [
            'user' => $user,
            'marchand' => $marchand,
            'countries' => $this->getCountries(),
        ]);
    }

    /**
     * Étape 2: Sauvegarde les informations de facturation
     */
    public function storeBilling(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'numero_carte' => 'required|string|max:19',
            'nom_porteur_carte' => 'required|string|max:255',
            'mois_expiration' => 'required|string|size:2',
            'annee_expiration' => 'required|string|size:4',
            'adresse_facturation_ligne1' => 'required|string|max:255',
            'adresse_facturation_ligne2' => 'nullable|string|max:255',
            'ville_facturation' => 'required|string|max:255',
            'region_facturation' => 'required|string|max:255',
            'code_postal_facturation' => 'required|string|max:20',
            'pays_facturation' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $data = $validator->validated();

            // Mettre à jour le marchand
            $marchand = Marchand::where('user_id', $user->id)->firstOrFail();
            $marchand->update(array_merge($data, [
                'etape_inscription' => 'store',
            ]));

            DB::commit();

            return redirect()->route('seller.store')->with('success', 'Informations de facturation sauvegardées avec succès');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Une erreur est survenue lors de la sauvegarde'])->withInput();
        }
    }

    /**
     * Étape 3: Affiche le formulaire d'informations boutique
     */
    public function store()
    {
        $user = Auth::user();

        // Vérifier si l'utilisateur a déjà un marchand
        $marchand = Marchand::where('user_id', $user->id)->first();

        return Inertia::render('SellerRegistration/StoreInformation', [
            'user' => $user,
            'marchand' => $marchand,
        ]);
    }

    /**
     * Étape 3: Sauvegarde les informations boutique
     */
    public function storeStore(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nom_boutique' => 'required|string|max:255',
            'description_boutique' => 'nullable|string|max:1000',
            'a_codes_upc' => 'required|in:yes,no',
            'possede_marque' => 'required|in:yes,no,some',
            'type_vendeur' => 'required|in:individual,business',
            'categories_produits' => 'nullable|string|max:1000',
            'volume_ventes_mensuel' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $data = $validator->validated();

            // Mettre à jour le marchand
            $marchand = Marchand::where('user_id', $user->id)->firstOrFail();
            $marchand->update(array_merge($data, [
                'etape_inscription' => 'documents',
                'documents_requis' => $this->getDocumentsRequis($data['type_vendeur']),
            ]));

            DB::commit();

            return redirect()->route('seller.documents')->with('success', 'Informations boutique sauvegardées avec succès');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Une erreur est survenue lors de la sauvegarde'])->withInput();
        }
    }

    /**
     * Affiche le formulaire d'informations business (ancienne méthode - à supprimer)
     */
    public function businessInfo()
    {
        $user = Auth::user();

        // Vérifier si l'utilisateur a déjà un marchand
        $marchand = Marchand::where('user_id', $user->id)->first();

        return Inertia::render('SellerRegistration/BusinessInfo', [
            'user' => $user,
            'marchand' => $marchand,
            'countries' => $this->getCountries(),
        ]);
    }

    /**
     * Sauvegarde les informations business
     */
    public function storeBusinessInfo(Request $request)
    {
        // Log des données reçues
        Log::info('Données reçues dans storeBusinessInfo', [
            'all_data' => $request->all(),
            'user_id' => Auth::id()
        ]);

        $validator = Validator::make($request->all(), [
            'pays_business' => 'required|string|max:255',
            'ville_business' => 'required|string|max:255',
            'type_business' => 'required|in:individuel,entreprise,cooperative,grande_entreprise',
            'nomEntreprise' => 'required|string|max:255',
            'description_business' => 'nullable|string|max:1000',
            'telephone_principal' => 'required|string|max:20',
            'email_business' => 'nullable|email|max:255',
            'site_web' => 'nullable|url|max:255',
            'chiffre_affaires_estime' => 'nullable|numeric|min:0',
            'nombre_employes' => 'nullable|integer|min:0',
            'categories_produits' => 'nullable|array',
            'accepte_conditions' => 'required|boolean|accepted',
        ]);

        if ($validator->fails()) {
            Log::warning('Validation échouée dans storeBusinessInfo', [
                'errors' => $validator->errors()->toArray(),
                'user_id' => Auth::id()
            ]);
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $data = $validator->validated();

            Log::info('Données validées dans storeBusinessInfo', [
                'validated_data' => $data,
                'user_id' => $user->id
            ]);

            // Créer ou mettre à jour le marchand
            $marchand = Marchand::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'nomEntreprise' => $data['nomEntreprise'],
                    'pays_business' => $data['pays_business'],
                    'ville_business' => $data['ville_business'],
                    'type_business' => $data['type_business'],
                    'description_business' => $data['description_business'] ?? null,
                    'telephone_principal' => $data['telephone_principal'],
                    'email_business' => $data['email_business'] ?? null,
                    'site_web' => $data['site_web'] ?? null,
                    'chiffre_affaires_estime' => $data['chiffre_affaires_estime'] ?? null,
                    'nombre_employes' => $data['nombre_employes'] ?? null,
                    'categories_produits' => $data['categories_produits'] ?? [],
                    'accepte_conditions' => $data['accepte_conditions'],
                    'etape_inscription' => 'documents',
                    'statut_validation' => 'en_attente',
                    'documents_requis' => $this->getDocumentsRequis($data['type_business']),
                    'source_inscription' => 'seller_platform',
                    'accepte_newsletter' => true,
                    'langue_preferee' => 'fr',
                ]
            );

            Log::info('Marchand créé/mis à jour avec succès', [
                'marchand_id' => $marchand->id,
                'user_id' => $user->id
            ]);

            DB::commit();

            return redirect()->route('seller.documents')
                ->with('success', 'Informations business sauvegardées avec succès');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur dans storeBusinessInfo', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id()
            ]);
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la sauvegarde: ' . $e->getMessage()
            ])->withInput();
        }
    }

    /**
     * Affiche la page d'upload des documents
     */
    public function documents()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)->firstOrFail();

        $documentsRequis = $marchand->getDocumentsRequis();
        $documentsExistants = $marchand->documents()->get()->keyBy('type_document');

        return Inertia::render('SellerRegistration/Documents', [
            'marchand' => $marchand,
            'documentsRequis' => $documentsRequis,
            'documentsExistants' => $documentsExistants,
            'typesDocuments' => MarchandDocument::getTypesDocuments(),
            'extensionsAutorisees' => MarchandDocument::getExtensionsAutorisees(),
            'tailleMaximale' => MarchandDocument::getTailleMaximale(),
        ]);
    }

    /**
     * Upload un document
     */
    public function uploadDocument(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type_document' => 'required|string',
            'fichier' => 'required|file|max:' . (MarchandDocument::getTailleMaximale() / 1024) . '|mimes:' . implode(',', MarchandDocument::getExtensionsAutorisees()),
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $marchand = Marchand::where('user_id', $user->id)->firstOrFail();

            $file = $request->file('fichier');
            $typeDocument = $request->type_document;

            // Générer un nom unique pour le fichier
            $nomStockage = uniqid() . '_' . time() . '.' . $file->getClientOriginalExtension();
            $cheminFichier = "documents/marchands/{$marchand->id}/{$nomStockage}";

            // Stocker le fichier
            $path = Storage::putFileAs("documents/marchands/{$marchand->id}", $file, $nomStockage);

            // Calculer le hash du fichier
            $contenuFichier = $file->getContent();
            $hashFichier = hash('sha256', $contenuFichier);

            // Supprimer l'ancien document du même type s'il existe
            $ancienDocument = $marchand->documents()->where('type_document', $typeDocument)->first();
            if ($ancienDocument) {
                Storage::delete($ancienDocument->chemin_fichier);
                $ancienDocument->delete();
            }

            // Créer l'enregistrement du document
            $document = MarchandDocument::create([
                'marchand_id' => $marchand->id,
                'type_document' => $typeDocument,
                'nom_original' => $file->getClientOriginalName(),
                'nom_stockage' => $nomStockage,
                'chemin_fichier' => $cheminFichier,
                'extension' => $file->getClientOriginalExtension(),
                'taille_fichier' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'hash_fichier' => $hashFichier,
                'statut_validation' => 'en_attente',
                'date_upload' => now(),
                'est_obligatoire' => in_array($typeDocument, $marchand->getDocumentsRequis()),
                'est_confidentiel' => true,
                'version' => 1,
                'adresse_ip_upload' => $request->ip(),
                'user_agent_upload' => $request->userAgent(),
            ]);

            // Mettre à jour le statut du marchand si tous les documents sont uploadés
            $this->verifierCompletionDocuments($marchand);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Document uploadé avec succès',
                'document' => $document,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Erreur lors de l\'upload du document'], 500);
        }
    }

    /**
     * Finalise l'inscription
     */
    public function finalize()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)->firstOrFail();

        // Vérifier que tous les documents obligatoires sont uploadés
        if (!$marchand->documentsObligatoiresComplets()) {
            return back()->withErrors(['error' => 'Tous les documents obligatoires doivent être uploadés']);
        }

        try {
            DB::beginTransaction();

            // Mettre à jour le statut
            $marchand->update([
                'etape_inscription' => 'validation',
                'date_soumission_documents' => now(),
                'documents_soumis' => $marchand->documents()->pluck('type_document')->toArray(),
            ]);

            // Créer un abonnement gratuit par défaut
            MarchandAbonnement::creerAbonnement($marchand->id, 'gratuit');

            DB::commit();

            return Inertia::render('SellerRegistration/Success', [
                'marchand' => $marchand,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Erreur lors de la finalisation']);
        }
    }

    /**
     * Méthodes utilitaires privées
     */

    private function getCountries(): array
    {
        return [
            'CM' => 'Cameroun',
            'CI' => 'Côte d\'Ivoire',
            'SN' => 'Sénégal',
            'ML' => 'Mali',
            'BF' => 'Burkina Faso',
            'NE' => 'Niger',
            'TD' => 'Tchad',
            'GA' => 'Gabon',
            'CG' => 'Congo',
            'CF' => 'République Centrafricaine',
        ];
    }

    private function getDocumentsRequis(string $typeVendeur): array
    {
        $documentsBase = ['CNI', 'photo_avec_cni'];

        switch ($typeVendeur) {
            case 'individual':
                return array_merge($documentsBase, ['telephone_verification']);

            case 'business':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'rib_bancaire'
                ]);

            // Anciens types pour compatibilité
            case 'individuel':
                return array_merge($documentsBase, ['telephone_verification']);

            case 'entreprise':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'rib_bancaire'
                ]);

            case 'cooperative':
                return array_merge($documentsBase, [
                    'recepisse_cooperative',
                    'statuts_cooperative',
                    'rib_bancaire'
                ]);

            case 'grande_entreprise':
                return array_merge($documentsBase, [
                    'bilan_comptable',
                    'registre_commerce',
                    'rib_bancaire',
                    'attestation_fiscale'
                ]);

            default:
                return $documentsBase;
        }
    }

    private function verifierCompletionDocuments(Marchand $marchand): void
    {
        $documentsRequis = $marchand->getDocumentsRequis();
        $documentsUploades = $marchand->documents()->pluck('type_document')->toArray();

        $tousDocumentsUploades = empty(array_diff($documentsRequis, $documentsUploades));

        if ($tousDocumentsUploades && $marchand->etape_inscription === 'documents') {
            $marchand->update(['etape_inscription' => 'pret_validation']);
        }
    }
}
