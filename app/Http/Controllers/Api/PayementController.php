<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PayPalService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class PayementController extends Controller
{
    private $paypalService;

    public function __construct(PayPalService $paypalService)
    {
        $this->paypalService = $paypalService;
    }

    public function createOrder(Request $request)
    {
        $request->validate([
            'montant' => 'required|numeric|min:0.01',
            'methode' => 'required|string|in:card,paypal,orange,mtn',
            'currency' => 'required|string|in:EUR,USD,XOF',
            'order_id' => 'required|string',
            'items' => 'required|array',
            'items.*.name' => 'required|string',
            'items.*.price' => 'required|numeric',
            'items.*.quantity' => 'required|integer|min:1',
            // Nouveau champ pour lier à une commande existante
            'commande_principale_id' => 'nullable|integer|exists:commandes_principales,id',
        ]);

        // Log de la tentative de création de commande
        Log::info('Tentative de création de commande de paiement', [
            'method' => $request->methode,
            'amount' => $request->montant,
            'currency' => $request->currency,
            'order_id' => $request->order_id,
            'commande_principale_id' => $request->commande_principale_id,
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip()
        ]);

        // Traitement selon la méthode de paiement
        switch ($request->methode) {
            case 'paypal':
                return $this->createPayPalOrder($request);

            case 'orange':
                return $this->createOrangeMoneyOrder($request);

            case 'mtn':
                return $this->createMTNMoneyOrder($request);

            case 'card':
                return $this->createCardOrder($request);

            default:
                Log::error('Méthode de paiement non supportée', [
                    'method' => $request->methode,
                    'request' => $request->all()
                ]);

                return redirect()->route('payment.cancel', [
                    'method' => $request->methode,
                    'error' => 'Méthode de paiement non supportée: ' . $request->methode,
                    'commande_id' => $request->commande_principale_id
                ]);
        }
    }

    /**
     * Crée une commande PayPal
     */
    private function createPayPalOrder(Request $request)
    {
        try {
            // Convertir FCFA en EUR si nécessaire
            $amount = $request->montant;
            $currency = $request->currency;
            $items = $request->items;

            if ($currency === 'XOF') {
                $originalAmount = $amount;
                $amount = round($amount * 0.0015, 2); // Taux approximatif XOF -> EUR, arrondi à 2 décimales
                $currency = 'EUR';

                // Convertir aussi les prix des articles avec arrondi
                $items = array_map(function ($item) {
                    $item['price'] = round($item['price'] * 0.0015, 2);
                    return $item;
                }, $items);

                Log::info('Conversion devise XOF → EUR', [
                    'montant_original_xof' => $originalAmount,
                    'montant_converti_eur' => $amount,
                    'taux_conversion' => 0.0015,
                    'items_count' => count($items)
                ]);
            }

            // S'assurer que le montant est correctement formaté pour PayPal
            $formattedAmount = $this->paypalService->formatAmount($amount);

            // Valider que tous les prix des items sont correctement formatés
            $items = array_map(function ($item) {
                $item['price'] = $this->paypalService->formatAmount($item['price']);
                return $item;
            }, $items);

            $orderData = [
                'order_id' => $request->order_id,
                'total' => $formattedAmount,
                'montant' => $formattedAmount,
                'currency' => $currency,
                'items' => $items,
                'tax' => $this->paypalService->formatAmount($request->tax ?? 0),
                'shipping' => $this->paypalService->formatAmount($request->shipping ?? 0),
                'description' => $request->description ?? 'Commande Loʁelei Marketplace',
                'return_url' => route('payment.success', [
                    'method' => 'paypal',
                    'commande_principale_id' => $request->commande_principale_id
                ]),
                'cancel_url' => route('payment.cancel', [
                    'method' => 'paypal',
                    'commande_principale_id' => $request->commande_principale_id
                ]),
            ];

            Log::info('Données PayPal formatées', [
                'total_formatted' => $formattedAmount,
                'currency' => $currency,
                'items_count' => count($items),
                'order_id' => $request->order_id
            ]);

            $result = $this->paypalService->createPayment($orderData);

            Log::info('Commande PayPal créée', [
                'success' => $result['success'],
                'order_id' => $request->order_id,
                'amount' => $amount,
                'currency' => $currency
            ]);

            if ($result['success']) {
                return Inertia::location($result['approval_url']);
            } else {
                Log::error('Échec création commande PayPal', [
                    'error' => $result['error'] ?? 'Erreur inconnue',
                    'order_data' => $orderData,
                    'order' => $result['order']
                ]);

                return redirect()->route('payment.cancel', [
                    'method' => 'paypal',
                    'error' => $result['error'] ?? 'Erreur lors de la création de la commande PayPal',
                    'commande_id' => $request->commande_principale_id
                ]);
            }
        } catch (\Throwable $th) {
            Log::error('Exception lors de la création PayPal', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
                'request' => $request->all()
            ]);

            return redirect()->route('payment.cancel', [
                'method' => 'paypal',
                'error' => 'Erreur technique PayPal: ' . $th->getMessage(),
                'commande_id' => $request->commande_principale_id
            ]);
        }
    }

    /**
     * Crée une commande Orange Money (à implémenter)
     */
    private function createOrangeMoneyOrder(Request $request)
    {
        Log::info('Tentative Orange Money (non implémenté)', [
            'request' => $request->all()
        ]);

        return redirect()->route('payment.cancel', [
            'method' => 'orange',
            'error' => 'Orange Money n\'est pas encore disponible',
            'commande_id' => $request->commande_principale_id
        ]);
    }

    /**
     * Crée une commande MTN Money (à implémenter)
     */
    private function createMTNMoneyOrder(Request $request)
    {
        Log::info('Tentative MTN Money (non implémenté)', [
            'request' => $request->all()
        ]);

        return redirect()->route('payment.cancel', [
            'method' => 'mtn',
            'error' => 'MTN Mobile Money n\'est pas encore disponible',
            'commande_id' => $request->commande_principale_id
        ]);
    }

    /**
     * Crée une commande par carte (à implémenter)
     */
    private function createCardOrder(Request $request)
    {
        Log::info('Tentative paiement carte (non implémenté)', [
            'request' => $request->all()
        ]);

        return redirect()->route('payment.cancel', [
            'method' => 'card',
            'error' => 'Le paiement par carte n\'est pas encore disponible',
            'commande_id' => $request->commande_principale_id
        ]);
    }

    public function executePayment(Request $request): JsonResponse
    {
        $request->validate([
            'payment_id' => 'required|string',
            'payer_id' => 'required|string',
        ]);

        $result = $this->paypalService->executePayment(
            $request->payment_id,
            $request->payer_id
        );

        if ($result['success']) {
            // Sauvegarder les détails de la transaction dans votre base de données
            // Exemple : Sauvegarder dans un modèle Payment
            // \App\Models\Payment::create([
            //     'user_id' => auth()->id(),
            //     'payment_id' => $request->payment_id,
            //     'transaction_id' => $result['transaction_id'],
            //     'amount' => $result['payment']->getTransactions()[0]->getAmount()->getTotal(),
            //     'currency' => $result['payment']->getTransactions()[0]->getAmount()->getCurrency(),
            //     'status' => $result['state'],
            //     'method' => 'paypal',
            // ]);

            return response()->json([
                'success' => true,
                'transaction_id' => $result['transaction_id'],
                'status' => $result['state'],
                'message' => 'Paiement exécuté avec succès'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 500);
        }
    }

    public function getPaymentDetails(Request $request, string $paymentId): JsonResponse
    {
        $result = $this->paypalService->getPaymentDetails($paymentId);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'payment' => [
                    'id' => $paymentId,
                    'state' => $result['state'],
                    'total' => $result['total'],
                    'currency' => $result['currency']
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 500);
        }
    }

    public function refundPayment(Request $request): JsonResponse
    {
        $request->validate([
            'sale_id' => 'required|string',
            'amount' => 'nullable|numeric|min:0.01',
        ]);

        $result = $this->paypalService->refundPayment(
            $request->sale_id,
            $request->amount
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'refund_id' => $result['refund_id'],
                'state' => $result['state'],
                'message' => 'Remboursement effectué avec succès'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 500);
        }
    }

    public function success(Request $request)
    {
        // Déterminer la méthode de paiement
        $paymentMethod = $request->get('method', 'paypal'); // Par défaut PayPal

        Log::info('Tentative de confirmation de paiement', [
            'method' => $paymentMethod,
            'request_data' => $request->all(),
            'commande_principale_id' => $request->get('commande_principale_id'),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip()
        ]);

        try {
            $result = null;
            $paymentData = [];

            // Traitement selon la méthode de paiement
            switch ($paymentMethod) {
                case 'paypal':
                    $result = $this->paypalService->capturePayment($request);

                    if ($result['success']) {
                        $paymentData = [
                            'method' => 'paypal',
                            'transaction_id' => $result['capture']['id'] ?? 'paypal_' . time(),
                            'status' => 'completed',
                            'amount' => $result['capture']['amount']['value'] ?? null,
                            'currency' => $result['capture']['amount']['currency_code'] ?? 'EUR',
                            'payer_email' => $result['payer']['email_address'] ?? null,
                            'payer_id' => $result['payer']['payer_id'] ?? null,
                            'data' => $result
                        ];

                        Log::info('Paiement PayPal capturé avec succès', [
                            'transaction_id' => $paymentData['transaction_id'],
                            'amount' => $paymentData['amount'],
                            'currency' => $paymentData['currency'],
                            'commande_principale_id' => $request->get('commande_principale_id')
                        ]);
                    }
                    break;

                case 'orange':
                case 'mtn':
                    // TODO: Implémenter les services Mobile Money
                    $result = ['success' => false, 'error' => 'Mobile Money non encore implémenté'];
                    break;

                case 'card':
                    // TODO: Implémenter Stripe ou autre processeur de cartes
                    $result = ['success' => false, 'error' => 'Paiement par carte non encore implémenté'];
                    break;

                default:
                    $result = ['success' => false, 'error' => 'Méthode de paiement non supportée: ' . $paymentMethod];
            }

            if ($result && $result['success']) {
                // Si une commande principale est liée, confirmer le paiement
                if ($request->has('commande_principale_id')) {
                    $checkoutService = app(\App\Services\CheckoutService::class);
                    $checkoutService->confirmPayment(
                        $request->get('commande_principale_id'),
                        $paymentData
                    );

                    Log::info('Commande confirmée avec succès', [
                        'commande_principale_id' => $request->get('commande_principale_id'),
                        'payment_method' => $paymentMethod,
                        'transaction_id' => $paymentData['transaction_id'] ?? null
                    ]);
                }

                // Récupérer les détails de la commande pour la page de succès
                $commandeDetails = $this->getCommandeDetailsForSuccess($request->get('commande_principale_id'));

                return Inertia::render('ecommerce/payment-success', [
                    'payment_result' => $result,
                    'payment_method' => $paymentMethod,
                    'payment_data' => $paymentData,
                    'commande_id' => $request->get('commande_principale_id'),
                    'commande_details' => $commandeDetails
                ]);
            } else {
                // En cas d'échec, annuler la commande si elle existe
                $errorMessage = $result['error'] ?? 'Erreur inconnue';

                Log::error('Échec du paiement', [
                    'method' => $paymentMethod,
                    'error' => $errorMessage,
                    'commande_principale_id' => $request->get('commande_principale_id'),
                    'request_data' => $request->all()
                ]);

                if ($request->has('commande_principale_id')) {
                    $checkoutService = app(\App\Services\CheckoutService::class);
                    $checkoutService->cancelOrder(
                        $request->get('commande_principale_id'),
                        'Échec du paiement ' . ucfirst($paymentMethod) . ': ' . $errorMessage
                    );
                }

                return redirect()->route('payment.cancel', [
                    'method' => $paymentMethod,
                    'error' => $errorMessage,
                    'commande_id' => $request->get('commande_principale_id')
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Erreur technique lors de la confirmation de paiement', [
                'method' => $paymentMethod,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
                'commande_principale_id' => $request->get('commande_principale_id')
            ]);

            // Annuler la commande en cas d'erreur
            if ($request->has('commande_principale_id')) {
                try {
                    $checkoutService = app(\App\Services\CheckoutService::class);
                    $checkoutService->cancelOrder(
                        $request->get('commande_principale_id'),
                        'Erreur technique lors de la confirmation ' . ucfirst($paymentMethod) . ': ' . $e->getMessage()
                    );
                } catch (\Exception $cancelError) {
                    Log::error('Erreur lors de l\'annulation de commande après échec technique', [
                        'original_error' => $e->getMessage(),
                        'cancel_error' => $cancelError->getMessage(),
                        'commande_principale_id' => $request->get('commande_principale_id')
                    ]);
                }
            }

            return redirect()->route('payment.cancel', [
                'method' => $paymentMethod,
                'error' => 'Erreur technique: ' . $e->getMessage(),
                'commande_id' => $request->get('commande_principale_id')
            ]);
        }
    }

    public function cancel(Request $request)
    {
        // Récupérer les détails de l'annulation
        $paymentMethod = $request->get('method', 'unknown');
        $errorMessage = $request->get('error', 'Paiement annulé par l\'utilisateur');
        $commandeId = $request->get('commande_id');

        // Détails spécifiques selon la méthode de paiement
        $cancelDetails = $this->getCancelDetails($request, $paymentMethod);

        Log::warning('Paiement annulé', [
            'method' => $paymentMethod,
            'error' => $errorMessage,
            'commande_principale_id' => $commandeId,
            'cancel_details' => $cancelDetails,
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip(),
            'referer' => $request->header('referer'),
            'session_id' => $request->session()->getId()
        ]);

        // Si une commande est liée, l'annuler si pas déjà fait
        if ($commandeId) {
            try {
                $checkoutService = app(\App\Services\CheckoutService::class);
                $checkoutService->cancelOrder(
                    $commandeId,
                    'Paiement ' . ucfirst($paymentMethod) . ' annulé: ' . $errorMessage
                );

                Log::info('Commande annulée suite à l\'échec de paiement', [
                    'commande_principale_id' => $commandeId,
                    'payment_method' => $paymentMethod,
                    'reason' => $errorMessage
                ]);
            } catch (\Exception $e) {
                Log::error('Erreur lors de l\'annulation de commande depuis cancel', [
                    'commande_principale_id' => $commandeId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Récupérer les détails de la commande pour la page d'annulation
        $commandeDetails = $this->getCommandeDetailsForSuccess($commandeId);

        return Inertia::render('ecommerce/payment-cancel', [
            'payment_method' => $paymentMethod,
            'error_message' => $errorMessage,
            'commande_id' => $commandeId,
            'commande_details' => $commandeDetails,
            'cancel_details' => $cancelDetails,
            'can_retry' => $this->canRetryPayment($paymentMethod, $errorMessage)
        ]);
    }

    /**
     * Récupère les détails spécifiques de l'annulation selon la méthode
     */
    private function getCancelDetails(Request $request, string $paymentMethod): array
    {
        $details = [
            'timestamp' => now()->toISOString(),
            'method' => $paymentMethod,
            'user_cancelled' => false,
            'technical_error' => false,
            'provider_error' => false
        ];

        switch ($paymentMethod) {
            case 'paypal':
                // Analyser les paramètres PayPal
                if ($request->has('token')) {
                    $details['paypal_token'] = $request->get('token');
                    $details['user_cancelled'] = true; // L'utilisateur a annulé sur PayPal
                    $details['cancel_reason'] = 'Utilisateur a annulé le paiement sur PayPal';
                } else {
                    $details['technical_error'] = true;
                    $details['cancel_reason'] = 'Erreur technique PayPal';
                }
                break;

            case 'orange':
            case 'mtn':
                $details['provider_error'] = true;
                $details['cancel_reason'] = 'Service Mobile Money non disponible';
                break;

            case 'card':
                $details['provider_error'] = true;
                $details['cancel_reason'] = 'Service de paiement par carte non disponible';
                break;

            default:
                $details['technical_error'] = true;
                $details['cancel_reason'] = 'Méthode de paiement non supportée';
        }

        return $details;
    }

    /**
     * Détermine si le paiement peut être retenté
     */
    private function canRetryPayment(string $paymentMethod, string $errorMessage): bool
    {
        // Ne pas permettre de retry si c'est une erreur de méthode non supportée
        if (str_contains($errorMessage, 'non encore implémenté') ||
            str_contains($errorMessage, 'non supportée')) {
            return false;
        }

        // Permettre retry pour PayPal (erreurs temporaires possibles)
        if ($paymentMethod === 'paypal') {
            return true;
        }

        // Par défaut, permettre le retry
        return true;
    }

    /**
     * Récupère les détails de la commande pour la page de succès
     */
    private function getCommandeDetailsForSuccess(?int $commandePrincipaleId): ?array
    {
        if (!$commandePrincipaleId) {
            return null;
        }

        try {
            $commande = \App\Models\CommandePrincipale::with(['sousCommandes.marchand'])
                ->find($commandePrincipaleId);

            if (!$commande) {
                return null;
            }

            return [
                'id' => $commande->id,
                'numero_commande' => $commande->numero_commande,
                'montant_total_ttc' => $commande->montant_total_ttc,
                'devise' => $commande->devise ?? 'FCFA',
                'statut_global' => $commande->statut_global,
                'date_commande' => $commande->date_commande?->format('d/m/Y H:i'),
                'nombre_marchands' => $commande->nombre_marchands,
                'nombre_articles_total' => $commande->nombre_articles_total,
                'sous_commandes' => $commande->sousCommandes->map(function ($sousCommande) {
                    return [
                        'id' => $sousCommande->id,
                        'numero_sous_commande' => $sousCommande->numero_sous_commande,
                        'marchand_nom' => $sousCommande->marchand->nom_boutique ?? $sousCommande->marchand->nom,
                        'montant_ttc' => $sousCommande->montant_ttc,
                        'statut' => $sousCommande->statut
                    ];
                })->toArray()
            ];
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des détails de commande pour succès', [
                'commande_principale_id' => $commandePrincipaleId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}
