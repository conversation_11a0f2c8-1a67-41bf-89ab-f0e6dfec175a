<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Marchand;
use App\Models\Produit;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class MarchandController extends Controller
{
    /**
     * Affiche les informations d'un marchand spécifique
     */
    public function show(string $id): JsonResponse
    {
        try {
            $marchand = Marchand::with(['user'])
                ->where('id', $id)
                ->where('statut_validation', 'valide')
                ->first();

            if (!$marchand) {
                return response()->json([
                    'success' => false,
                    'message' => 'Marchand non trouvé ou non validé'
                ], 404);
            }

            // Calculer les statistiques du marchand
            $stats = $this->calculateMarchandStats($marchand);

            // Préparer les données avec URLs complètes
            $marchandData = $marchand->toArray();

            // Ajouter l'URL complète du logo si elle existe
            if ($marchand->logo) {
                $marchandData['logo_url'] = Storage::disk('public')->url($marchand->logo);
            } else {
                $marchandData['logo_url'] = null;
            }

            return response()->json([
                'success' => true,
                'data' => array_merge($marchandData, $stats)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération du marchand'
            ], 500);
        }
    }

    /**
     * Récupère un marchand par son slug
     */
    public function getBySlug(string $slug): JsonResponse
    {
        try {
            // Chercher directement par slug
            $marchand = Marchand::with(['user'])
                ->where('statut_validation', 'valide')
                ->where('slug', $slug)
                ->first();

            if (!$marchand) {
                return response()->json([
                    'success' => false,
                    'message' => 'Boutique non trouvée'
                ], 404);
            }

            // Calculer les statistiques du marchand
            $stats = $this->calculateMarchandStats($marchand);

            // Préparer les données avec URLs complètes
            $marchandData = $marchand->toArray();

            // Ajouter l'URL complète du logo si elle existe
            if ($marchand->logo) {
                $marchandData['logo_url'] = Storage::disk('public')->url($marchand->logo);
            } else {
                $marchandData['logo_url'] = null;
            }

            return response()->json([
                'success' => true,
                'data' => array_merge($marchandData, $stats)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération de la boutique: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupère les statistiques d'un marchand
     */
    public function getStats(string $id): JsonResponse
    {
        try {
            $marchand = Marchand::where('id', $id)
                ->where('statut_validation', 'valide')
                ->first();

            if (!$marchand) {
                return response()->json([
                    'success' => false,
                    'message' => 'Marchand non trouvé'
                ], 404);
            }

            $stats = $this->calculateMarchandStats($marchand);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques'
            ], 500);
        }
    }

    /**
     * Récupère les produits d'un marchand avec pagination
     */
    public function getProducts(string $id, Request $request): JsonResponse
    {
        try {
            $marchand = Marchand::where('id', $id)
                ->where('statut_validation', 'valide')
                ->first();

            if (!$marchand) {
                return response()->json([
                    'success' => false,
                    'message' => 'Marchand non trouvé'
                ], 404);
            }

            $page = (int) $request->get('page', 1);
            $limit = min((int) $request->get('limit', 12), 50); // Max 50 produits par page
            $categoryId = $request->get('category_id');
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $search = $request->get('search');

            $query = Produit::where('marchand_id', $id);

            // Filtrer par catégorie si spécifiée
            if ($categoryId) {
                $query->where('categorie_id', $categoryId);
            }

            // Recherche si spécifiée
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('nom', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                });
            }

            // Tri
            $allowedSorts = ['created_at', 'nom', 'prix', 'average_rating'];
            if (in_array($sortBy, $allowedSorts)) {
                $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
            }

            // Pagination
            $totalItems = $query->count();
            $totalPages = ceil($totalItems / $limit);
            $offset = ($page - 1) * $limit;

            $produits = $query->skip($offset)->take($limit)->get();

            return response()->json([
                'success' => true,
                'data' => $produits,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_items' => $totalItems,
                    'per_page' => $limit,
                    'has_more' => $page < $totalPages
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des produits'
            ], 500);
        }
    }

    /**
     * Calcule les statistiques d'un marchand
     */
    private function calculateMarchandStats(Marchand $marchand): array
    {
        try {
            // Nombre total de produits
            $totalProducts = Produit::where('marchand_id', $marchand->id)
                ->count();

            // Note moyenne et nombre d'avis (calculés via les accessors du modèle)
            $produits = Produit::where('marchand_id', $marchand->id)->get();
            $averageRating = 0;
            $totalReviews = 0;

            if ($produits->count() > 0) {
                $ratings = [];
                foreach ($produits as $produit) {
                    $rating = $produit->average_rating;
                    $reviews = $produit->reviews_count;

                    if ($rating > 0) {
                        $ratings[] = $rating;
                    }
                    $totalReviews += $reviews;
                }

                if (count($ratings) > 0) {
                    $averageRating = round(array_sum($ratings) / count($ratings), 1);
                }
            }

            // Nombre total de ventes (approximatif basé sur les commandes)
            $totalSales = 0;
            try {
                if (DB::getSchemaBuilder()->hasTable('sous_commande_vendeurs')) {
                    $totalSales = DB::table('sous_commande_vendeurs')
                        ->where('marchand_id', $marchand->id)
                        ->where('statut', 'livre')
                        ->count();
                }
            } catch (\Exception $e) {
                // Ignorer les erreurs de table manquante
                $totalSales = 0;
            }

            return [
                'total_products' => $totalProducts,
                'average_rating' => $averageRating,
                'total_reviews' => $totalReviews,
                'total_sales' => $totalSales,
                'joined_date' => $marchand->created_at->toISOString(),
                'slug' => $marchand->slug ?: $marchand->generateUniqueSlug($marchand->nomEntreprise)
            ];
        } catch (\Exception $e) {
            // En cas d'erreur, retourner des valeurs par défaut
            return [
                'total_products' => 0,
                'average_rating' => 0,
                'total_reviews' => 0,
                'total_sales' => 0,
                'joined_date' => $marchand->created_at->toISOString(),
                'slug' => $marchand->slug ?: 'marchand-' . $marchand->id
            ];
        }
    }


}
