<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MarchandZoneLivraison;
use App\Models\Produit;
use App\Models\ProduitZoneLivraison;
use App\Models\ZoneLivraison;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ProduitZoneLivraisonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = ProduitZoneLivraison::query();

        // Filtrer par produit
        if ($request->has('produit_id')) {
            $query->where('produit_id', $request->produit_id);
        }

        // Filtrer par marchand-zone
        if ($request->has('marchand_zone_livraison_id')) {
            $query->where('marchand_zone_livraison_id', $request->marchand_zone_livraison_id);
        }

        // Filtrer par marchand (via la relation marchand_zone_livraison)
        if ($request->has('marchand_id')) {
            $query->whereHas('marchandZoneLivraison', function ($q) use ($request) {
                $q->where('marchand_id', $request->marchand_id);
            });
        }

        // Si l'utilisateur est un marchand, filtrer par son ID
        if (Auth::user() && Auth::user()->role === 'Marchand') {
            $query->whereHas('marchandZoneLivraison', function ($q) {
                $q->where('marchand_id', Auth::id());
            });
        }

        // Filtrer par actif
        if ($request->has('actif')) {
            $query->where('actif', $request->actif);
        }

        // Inclure les relations
        if ($request->has('with')) {
            $relations = explode(',', $request->with);
            $allowedRelations = ['produit', 'marchandZoneLivraison', 'marchandZoneLivraison.zoneLivraison', 'marchandZoneLivraison.marchand'];
            $validRelations = array_intersect($relations, $allowedRelations);

            if (!empty($validRelations)) {
                $query->with($validRelations);
            }
        }

        // Pagination
        $perPage = $request->input('per_page', 15);
        $produitZones = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $produitZones,
            'message' => 'Associations produit-zone récupérées avec succès'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'produit_id' => 'required|exists:produits,id',
            'marchand_zone_livraison_id' => 'required|exists:marchand_zones_livraison,id',
            'frais_livraison_specifique' => 'nullable|numeric|min:0',
            'actif' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        // Vérifier si le produit existe
        $produit = Produit::find($request->produit_id);
        if (!$produit) {
            return response()->json([
                'success' => false,
                'message' => 'Produit non trouvé'
            ], 404);
        }

        // Vérifier si l'association marchand-zone existe
        $marchandZone = MarchandZoneLivraison::find($request->marchand_zone_livraison_id);
        if (!$marchandZone) {
            return response()->json([
                'success' => false,
                'message' => 'Association marchand-zone non trouvée'
            ], 404);
        }

        // Si l'utilisateur est un marchand, vérifier qu'il est bien le propriétaire du produit et de la zone
        if (Auth::user() && Auth::user()->role === 'Marchand') {
            if ($produit->marchand_id !== Auth::id() || $marchandZone->marchand_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vous n\'êtes pas autorisé à créer cette association'
                ], 403);
            }
        }

        // Vérifier si l'association existe déjà
        $existingAssociation = ProduitZoneLivraison::where('produit_id', $request->produit_id)
            ->where('marchand_zone_livraison_id', $request->marchand_zone_livraison_id)
            ->first();

        if ($existingAssociation) {
            return response()->json([
                'success' => false,
                'message' => 'Cette association produit-zone existe déjà'
            ], 400);
        }

        $produitZone = ProduitZoneLivraison::create($request->all());

        return response()->json([
            'success' => true,
            'data' => $produitZone,
            'message' => 'Association produit-zone créée avec succès'
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        $produitZone = ProduitZoneLivraison::with(['produit', 'marchandZoneLivraison.zoneLivraison', 'marchandZoneLivraison.marchand'])->find($id);

        if (!$produitZone) {
            return response()->json([
                'success' => false,
                'message' => 'Association produit-zone non trouvée'
            ], 404);
        }

        // Si l'utilisateur est un marchand, vérifier qu'il est bien le propriétaire
        if (Auth::user() && Auth::user()->role === 'Marchand' && $produitZone->marchandZoneLivraison->marchand_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas autorisé à accéder à cette ressource'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $produitZone,
            'message' => 'Association produit-zone récupérée avec succès'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $produitZone = ProduitZoneLivraison::with('marchandZoneLivraison')->find($id);

        if (!$produitZone) {
            return response()->json([
                'success' => false,
                'message' => 'Association produit-zone non trouvée'
            ], 404);
        }

        // Si l'utilisateur est un marchand, vérifier qu'il est bien le propriétaire
        if (Auth::user() && Auth::user()->role === 'Marchand' && $produitZone->marchandZoneLivraison->marchand_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas autorisé à modifier cette ressource'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'frais_livraison_specifique' => 'nullable|numeric|min:0',
            'actif' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        $produitZone->update($request->all());

        return response()->json([
            'success' => true,
            'data' => $produitZone,
            'message' => 'Association produit-zone mise à jour avec succès'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        $produitZone = ProduitZoneLivraison::with('marchandZoneLivraison')->find($id);

        if (!$produitZone) {
            return response()->json([
                'success' => false,
                'message' => 'Association produit-zone non trouvée'
            ], 404);
        }

        // Si l'utilisateur est un marchand, vérifier qu'il est bien le propriétaire
        if (Auth::user() && Auth::user()->role === 'Marchand' && $produitZone->marchandZoneLivraison->marchand_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas autorisé à supprimer cette ressource'
            ], 403);
        }

        $produitZone->delete();

        return response()->json([
            'success' => true,
            'message' => 'Association produit-zone supprimée avec succès'
        ]);
    }

    /**
     * Récupérer les zones de livraison d'un produit spécifique.
     *
     * @param string $produitId
     * @return JsonResponse
     */
    public function getZonesByProduit(string $produitId): JsonResponse
    {
        // Vérifier si le produit existe
        $produit = Produit::find($produitId);
        if (!$produit) {
            return response()->json([
                'success' => false,
                'message' => 'Produit non trouvé'
            ], 404);
        }

        // Récupérer les zones de livraison du produit
        $zones = ProduitZoneLivraison::where('produit_id', $produitId)
            ->where('actif', true)
            ->with(['marchandZoneLivraison.zoneLivraison'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $zones,
            'message' => 'Zones de livraison du produit récupérées avec succès'
        ]);
    }

    /**
     * Récupérer toutes les zones de livraison disponibles pour un produit avec leurs informations hiérarchiques.
     *
     * @param string $produitId
     * @return JsonResponse
     */
    public function getAvailableZonesForProduct(string $produitId): JsonResponse
    {
        \Illuminate\Support\Facades\Log::info('getAvailableZonesForProduct called', [
            'produit_id' => $produitId
        ]);

        // Vérifier si le produit existe
        $produit = Produit::find($produitId);
        if (!$produit) {
            return response()->json([
                'success' => false,
                'message' => 'Produit non trouvé'
            ], 404);
        }

        try {
            // Récupérer toutes les zones de livraison associées à ce produit
            $produitZones = ProduitZoneLivraison::where('produit_id', $produitId)
                ->where('actif', true)
                ->with(['marchandZoneLivraison.zoneLivraison'])
                ->get();

            $availableZones = [];

            foreach ($produitZones as $produitZone) {
                if ($produitZone->marchandZoneLivraison && $produitZone->marchandZoneLivraison->zoneLivraison) {
                    $zone = $produitZone->marchandZoneLivraison->zoneLivraison;

                    // Récupérer les informations hiérarchiques (parents)
                    $hierarchyInfo = $this->getZoneHierarchy($zone);

                    // Ajouter les informations de livraison
                    $livraisonData = [
                        'frais_livraison' => $produitZone->frais_livraison_specifique ?? $produitZone->marchandZoneLivraison->frais_livraison,
                        'delai_livraison_min' => $produitZone->marchandZoneLivraison->delai_livraison_min,
                        'delai_livraison_max' => $produitZone->marchandZoneLivraison->delai_livraison_max
                    ];

                    $availableZones[] = [
                        'zone' => [
                            'id' => $zone->id,
                            'nom' => $zone->nom,
                            'type' => $zone->type,
                            'code' => $zone->code,
                            'hierarchy' => $hierarchyInfo
                        ],
                        'livraison' => $livraisonData
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $availableZones,
                'message' => 'Zones de livraison disponibles récupérées avec succès'
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erreur lors de la récupération des zones disponibles', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des zones de livraison disponibles',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer la hiérarchie complète d'une zone (parents)
     *
     * @param ZoneLivraison $zone
     * @return array
     */
    private function getZoneHierarchy(ZoneLivraison $zone): array
    {
        $hierarchy = [];
        $currentZone = $zone;

        // Si la zone est déjà un pays, pas besoin de chercher les parents
        if ($currentZone->type === 'Pays') {
            return [
                [
                    'id' => $currentZone->id,
                    'nom' => $currentZone->nom,
                    'type' => $currentZone->type
                ]
            ];
        }

        // Ajouter la zone actuelle
        $hierarchy[] = [
            'id' => $currentZone->id,
            'nom' => $currentZone->nom,
            'type' => $currentZone->type
        ];

        // Remonter la hiérarchie jusqu'au pays
        while ($currentZone->parent_id) {
            $currentZone = ZoneLivraison::find($currentZone->parent_id);
            if (!$currentZone) break;

            $hierarchy[] = [
                'id' => $currentZone->id,
                'nom' => $currentZone->nom,
                'type' => $currentZone->type
            ];

            if ($currentZone->type === 'Pays') break;
        }

        // Inverser le tableau pour avoir Pays > Region > Ville > Quartier
        return array_reverse($hierarchy);
    }

    /**
     * Vérifier si un produit peut être livré dans une zone spécifique.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkDeliveryAvailability(Request $request): JsonResponse
    {
        \Illuminate\Support\Facades\Log::info('checkDeliveryAvailability called', [
            'request' => $request->all()
        ]);

        $validator = Validator::make($request->all(), [
            'produit_id' => 'required|exists:produits,id',
            'zone_id' => 'required|exists:zones_livraison,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        try {
            $produit = Produit::findOrFail($request->produit_id);

            // Vérifier si la méthode peutEtreLivreDansZone existe
            if (!method_exists($produit, 'peutEtreLivreDansZone')) {
                \Illuminate\Support\Facades\Log::error('La méthode peutEtreLivreDansZone n\'existe pas dans le modèle Produit');

                // Vérifier manuellement si le produit peut être livré dans la zone
                $available = ProduitZoneLivraison::whereHas('marchandZoneLivraison', function ($q) use ($request) {
                    $q->where('zone_livraison_id', $request->zone_id);
                })
                ->where('produit_id', $request->produit_id)
                ->where('actif', true)
                ->exists();
            } else {
                $available = $produit->peutEtreLivreDansZone($request->zone_id);
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erreur lors de la vérification de la disponibilité de livraison', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la vérification de la disponibilité de livraison',
                'error' => $e->getMessage()
            ], 500);
        }

        if ($available) {
            // Récupérer les informations de livraison
            $livraison = ProduitZoneLivraison::whereHas('marchandZoneLivraison', function ($q) use ($request) {
                $q->where('zone_livraison_id', $request->zone_id);
            })
            ->where('produit_id', $request->produit_id)
            ->where('actif', true)
            ->with('marchandZoneLivraison')
            ->first();

            \Illuminate\Support\Facades\Log::info('Livraison spécifique trouvée', [
                'livraison' => $livraison
            ]);

            if (!$livraison) {
                // Si pas de conditions spécifiques, récupérer les conditions standard du marchand
                $livraison = MarchandZoneLivraison::where('marchand_id', $produit->marchand_id)
                    ->where('zone_livraison_id', $request->zone_id)
                    ->where('actif', true)
                    ->first();

                \Illuminate\Support\Facades\Log::info('Livraison standard trouvée', [
                    'livraison' => $livraison
                ]);
            }

            // Préparer les données de livraison
            $livraisonData = null;

            if ($livraison) {
                if ($livraison instanceof ProduitZoneLivraison) {
                    // Cas où on a une association produit-zone spécifique
                    // Charger la relation si elle n'est pas déjà chargée
                    if (!$livraison->relationLoaded('marchandZoneLivraison')) {
                        $livraison->load('marchandZoneLivraison');
                    }

                    // Vérifier que la relation existe
                    if ($livraison->marchandZoneLivraison) {
                        $livraisonData = [
                            'frais_livraison' => $livraison->frais_livraison,
                            'delai_livraison_min' => $livraison->marchandZoneLivraison->delai_livraison_min,
                            'delai_livraison_max' => $livraison->marchandZoneLivraison->delai_livraison_max,
                            'frais_livraison_specifique' => $livraison->frais_livraison_specifique
                        ];
                    } else {
                        $livraisonData = [
                            'frais_livraison' => $livraison->frais_livraison_specifique ?? 0,
                            'delai_livraison_min' => 0,
                            'delai_livraison_max' => 0,
                            'frais_livraison_specifique' => $livraison->frais_livraison_specifique
                        ];
                    }
                } else {
                    // Cas où on a juste une association marchand-zone
                    $livraisonData = [
                        'frais_livraison' => $livraison->frais_livraison,
                        'delai_livraison_min' => $livraison->delai_livraison_min,
                        'delai_livraison_max' => $livraison->delai_livraison_max
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'available' => true,
                    'livraison' => $livraisonData
                ],
                'message' => 'Le produit peut être livré dans cette zone'
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'available' => false
            ],
            'message' => 'Le produit ne peut pas être livré dans cette zone'
        ]);
    }

    /**
     * Vérifier si un produit peut être livré dans une zone spécifique ou dans ses zones enfants.
     * Retourne également les zones enfants disponibles pour la livraison.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkDeliveryAvailabilityWithChildren(Request $request): JsonResponse
    {
        \Illuminate\Support\Facades\Log::info('checkDeliveryAvailabilityWithChildren called', [
            'request' => $request->all()
        ]);

        $validator = Validator::make($request->all(), [
            'produit_id' => 'required|exists:produits,id',
            'zone_id' => 'required|exists:zones_livraison,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        try {
            $produit = Produit::findOrFail($request->produit_id);
            $zone = ZoneLivraison::findOrFail($request->zone_id);

            // Vérifier si le produit peut être livré dans la zone sélectionnée
            $availableInCurrentZone = $produit->peutEtreLivreDansZone($zone->id);

            // Récupérer les zones enfants
            $childrenZones = ZoneLivraison::where('parent_id', $zone->id)
                ->where('actif', true)
                ->get();

            // Vérifier quelles zones enfants peuvent recevoir le produit
            $availableChildrenZones = [];
            foreach ($childrenZones as $childZone) {
                if ($produit->peutEtreLivreDansZone($childZone->id)) {
                    $availableChildrenZones[] = [
                        'id' => $childZone->id,
                        'nom' => $childZone->nom,
                        'type' => $childZone->type,
                        'code' => $childZone->code,
                    ];
                }
            }

            // Préparer les données de livraison pour la zone actuelle si disponible
            $livraisonData = null;
            if ($availableInCurrentZone) {
                // Récupérer les informations de livraison
                $livraison = ProduitZoneLivraison::whereHas('marchandZoneLivraison', function ($q) use ($zone) {
                    $q->where('zone_livraison_id', $zone->id);
                })
                ->where('produit_id', $produit->id)
                ->where('actif', true)
                ->with('marchandZoneLivraison')
                ->first();

                if (!$livraison) {
                    // Si pas de conditions spécifiques, récupérer les conditions standard du marchand
                    $livraison = MarchandZoneLivraison::where('marchand_id', $produit->marchand_id)
                        ->where('zone_livraison_id', $zone->id)
                        ->where('actif', true)
                        ->first();
                }

                if ($livraison) {
                    if ($livraison instanceof ProduitZoneLivraison) {
                        // Cas où on a une association produit-zone spécifique
                        if (!$livraison->relationLoaded('marchandZoneLivraison')) {
                            $livraison->load('marchandZoneLivraison');
                        }

                        if ($livraison->marchandZoneLivraison) {
                            $livraisonData = [
                                'frais_livraison' => $livraison->frais_livraison,
                                'delai_livraison_min' => $livraison->marchandZoneLivraison->delai_livraison_min,
                                'delai_livraison_max' => $livraison->marchandZoneLivraison->delai_livraison_max,
                                'frais_livraison_specifique' => $livraison->frais_livraison_specifique
                            ];
                        }
                    } else {
                        // Cas où on a juste une association marchand-zone
                        $livraisonData = [
                            'frais_livraison' => $livraison->frais_livraison,
                            'delai_livraison_min' => $livraison->delai_livraison_min,
                            'delai_livraison_max' => $livraison->delai_livraison_max
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'available' => $availableInCurrentZone,
                    'livraison' => $livraisonData,
                    'has_children' => count($childrenZones) > 0,
                    'available_children' => $availableChildrenZones,
                    'all_children' => $childrenZones->map(function($zone) {
                        return [
                            'id' => $zone->id,
                            'nom' => $zone->nom,
                            'type' => $zone->type,
                            'code' => $zone->code,
                        ];
                    })
                ],
                'message' => $availableInCurrentZone
                    ? 'Le produit peut être livré dans cette zone'
                    : (count($availableChildrenZones) > 0
                        ? 'Le produit peut être livré dans certaines zones enfants'
                        : 'Le produit ne peut pas être livré dans cette zone ni dans ses zones enfants')
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erreur lors de la vérification de la disponibilité de livraison avec enfants', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la vérification de la disponibilité de livraison',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
