<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Dispute;
use App\Models\DisputeMessage;
use App\Services\ReverbWebSocketService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DisputeController extends Controller
{
    protected ReverbWebSocketService $webSocketService;

    public function __construct(ReverbWebSocketService $webSocketService)
    {
        $this->webSocketService = $webSocketService;
    }
    /**
     * Liste des litiges
     */
    public function index(Request $request)
    {
        $disputes = Dispute::with(['client.user', 'marchand', 'assigneA'])
            ->when($request->search, function ($query, $search) {
                $query->where('numero_litige', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhereHas('client.user', function ($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%");
                      });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('statut', $status);
            })
            ->when($request->priority, function ($query, $priority) {
                $query->where('priorite', $priority);
            })
            ->when($request->assigned_to, function ($query, $assignedTo) {
                if ($assignedTo === 'me') {
                    $query->where('assigne_a', Auth::id());
                } elseif ($assignedTo === 'unassigned') {
                    $query->whereNull('assigne_a');
                }
            })
            ->orderByDesc('created_at')
            ->paginate(20);

        return response()->json([
            'disputes' => $disputes->items(),
            'pagination' => [
                'current_page' => $disputes->currentPage(),
                'last_page' => $disputes->lastPage(),
                'total' => $disputes->total(),
            ]
        ]);
    }

    /**
     * Détails d'un litige avec ses messages
     */
    public function show($id)
    {
        $dispute = Dispute::with([
            'client.user',
            'marchand',
            'assigneA',
            'resoluPar',
            'commandePrincipale' => function ($query) {
                $query->with(['sousCommandes.articles.produit:id,nom,prix_unitaire,image_principale']);
            }
        ])->findOrFail($id);

        $messages = DisputeMessage::where('dispute_id', $id)
            ->orderBy('created_at', 'asc')
            ->get();

        // Marquer les messages comme lus par l'admin
        DisputeMessage::where('dispute_id', $id)
            ->where('lu_par_admin', false)
            ->update(['lu_par_admin' => true]);

        // Formater les données de la commande pour l'affichage
        if ($dispute->commandePrincipale) {
            $dispute->commandePrincipale->produits = $dispute->commandePrincipale->sousCommandes->flatMap(function ($sousCommande) {
                return $sousCommande->articles->map(function ($article) {
                    return [
                        'id' => $article->produit->id,
                        'nom' => $article->produit->nom,
                        'prix_unitaire' => $article->produit->prix_unitaire,
                        'quantite' => $article->quantite,
                        'image_principale' => $article->produit->image_principale,
                    ];
                });
            });

            // Supprimer les sous-commandes pour éviter la redondance
            unset($dispute->commandePrincipale->sousCommandes);
        }

        return response()->json([
            'dispute' => $dispute,
            'messages' => $messages
        ]);
    }

    /**
     * Envoyer un nouveau message
     */
    public function store(Request $request, $disputeId)
    {
        $request->validate([
            'message' => 'required|string|max:2000',
            'type_message' => 'required|string|in:message,solution_proposee,demande_info,resolution,changement_statut',
            'interne' => 'boolean',
            'pieces_jointes' => 'nullable|array',
            'pieces_jointes.*' => 'file|max:5120|mimes:jpg,jpeg,png,pdf,doc,docx'
        ]);

        $dispute = Dispute::findOrFail($disputeId);

        // Gérer les pièces jointes
        $attachments = [];
        if ($request->hasFile('pieces_jointes')) {
            foreach ($request->file('pieces_jointes') as $file) {
                $path = $file->store('dispute-attachments', 'private');
                $attachments[] = $path;
            }
        }

        // Créer le message
        $message = DisputeMessage::create([
            'dispute_id' => $disputeId,
            'auteur_type' => 'admin',
            'auteur_id' => Auth::id(),
            'auteur_nom' => Auth::user()->name,
            'message' => $request->message,
            'type_message' => $request->type_message,
            'pieces_jointes' => $attachments,
            'interne' => $request->boolean('interne', false),
            'lu_par_admin' => true,
        ]);

        // Mettre à jour le statut du litige si nécessaire
        if ($dispute->statut === 'ouvert') {
            $dispute->update([
                'statut' => 'en_cours',
                'date_premiere_reponse' => now(),
                'assigne_a' => Auth::id(),
            ]);
        }

        // Diffuser le message via WebSocket
        $this->webSocketService->broadcastDisputeMessage($message);

        // Envoyer webhook vers lorrelei/ pour notifier le client
        try {
            $webhookUrl = config('services.lorrelei.webhook_url');

            $payload = [
                'type' => 'new_dispute_message_from_admin',
                'data' => [
                    'id' => $message->id,
                    'dispute_id' => $message->dispute_id,
                    'auteur_type' => $message->auteur_type,
                    'auteur_nom' => $message->auteur_nom,
                    'message' => $message->message,
                    'type_message' => $message->type_message,
                    'pieces_jointes' => $message->pieces_jointes,
                    'interne' => $message->interne,
                    'created_at' => $message->created_at->toISOString(),
                    'dispute' => [
                        'id' => $dispute->id,
                        'numero_litige' => $dispute->numero_litige,
                        'statut' => $dispute->statut,
                    ]
                ],
                'timestamp' => now()->toISOString(),
            ];

            Http::timeout(5)->post($webhookUrl, $payload);
        } catch (\Exception $e) {
            Log::warning('Erreur envoi webhook vers lorrelei', [
                'error' => $e->getMessage(),
                'message_id' => $message->id
            ]);
        }

        return response()->json([
            'message' => $message,
            'success' => true
        ]);
    }

    /**
     * Assigner un litige à un admin
     */
    public function assign(Request $request, $id)
    {
        $request->validate([
            'admin_id' => 'required|exists:users,id'
        ]);

        $dispute = Dispute::findOrFail($id);
        $dispute->update(['assigne_a' => $request->admin_id]);

        return response()->json(['success' => true]);
    }

    /**
     * Changer le statut d'un litige
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'statut' => 'required|string|in:ouvert,en_cours,attente_client,attente_marchand,escalade,resolu,ferme,annule',
            'message' => 'nullable|string|max:500'
        ]);

        $dispute = Dispute::findOrFail($id);
        $oldStatus = $dispute->statut;

        $dispute->update([
            'statut' => $request->statut,
            'resolu_par' => $request->statut === 'resolu' ? Auth::id() : null,
            'date_resolution' => $request->statut === 'resolu' ? now() : null,
        ]);

        // Créer un message automatique pour le changement de statut
        if ($request->message) {
            DisputeMessage::create([
                'dispute_id' => $id,
                'auteur_type' => 'admin',
                'auteur_id' => Auth::id(),
                'auteur_nom' => Auth::user()->name,
                'message' => $request->message,
                'type_message' => 'changement_statut',
                'interne' => false,
                'lu_par_admin' => true,
                'metadata' => [
                    'ancien_statut' => $oldStatus,
                    'nouveau_statut' => $request->statut
                ]
            ]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Télécharger une pièce jointe
     */
    public function downloadAttachment($messageId, $filename)
    {
        $message = DisputeMessage::findOrFail($messageId);
        $attachments = $message->pieces_jointes ?? [];

        $filePath = collect($attachments)->first(function ($path) use ($filename) {
            return basename($path) === $filename;
        });

        if (!$filePath || !Storage::disk('private')->exists($filePath)) {
            abort(404);
        }

        return response()->download(storage_path('app/private/' . $filePath));
    }

    /**
     * Statistiques des litiges pour le dashboard
     */
    public function stats()
    {
        $stats = [
            'total_disputes' => Dispute::count(),
            'disputes_ouverts' => Dispute::where('statut', 'ouvert')->count(),
            'disputes_en_cours' => Dispute::where('statut', 'en_cours')->count(),
            'disputes_resolus_aujourd_hui' => Dispute::where('statut', 'resolu')
                ->whereDate('date_resolution', today())->count(),
            'disputes_urgents' => Dispute::where('urgent', true)
                ->whereNotIn('statut', ['resolu', 'ferme', 'annule'])->count(),
            'mes_disputes' => Dispute::where('assigne_a', Auth::id())
                ->whereNotIn('statut', ['resolu', 'ferme', 'annule'])->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Liste des admins pour assignation
     */
    public function admins()
    {
        $admins = \App\Models\User::where('is_admin', true)
            ->select('id', 'name', 'email')
            ->get();

        return response()->json($admins);
    }
}
