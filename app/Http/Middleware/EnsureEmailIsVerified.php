<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureEmailIsVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Si l'utilisateur n'est pas connecté, laisser passer
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Si l'email est déjà vérifié, laisser passer
        if ($user->hasVerifiedEmail()) {
            return $next($request);
        }

        // Si c'est déjà la page de vérification d'email, laisser passer
        if ($request->routeIs('verification.*')) {
            return $next($request);
        }

        // Si c'est une route de logout, laisser passer
        if ($request->routeIs('logout')) {
            return $next($request);
        }

        // Rediriger vers la page de vérification d'email
        return redirect()->route('verification.notice');
    }
}
