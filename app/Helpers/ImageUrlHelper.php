<?php

namespace App\Helpers;

class ImageUrlHelper
{
    /**
     * Génère l'URL complète pour une image en utilisant le CDN configuré
     *
     * @param string|null $imagePath Le chemin relatif de l'image
     * @param string $baseDir Le dossier de base (ex: 'products', 'categories', 'banners')
     * @return string|null L'URL complète de l'image ou null si pas d'image
     */
    public static function getImageUrl(?string $imagePath, string $baseDir = 'products'): ?string
    {
        if (empty($imagePath)) {
            return null;
        }

        $cdnUrl = rtrim(config('app.cdn_image_url'), '/');
        
        // Si le chemin commence déjà par le dossier de base, l'utiliser tel quel
        if (strpos($imagePath, $baseDir . '/') === 0) {
            return "{$cdnUrl}/images/{$imagePath}";
        }
        
        // Sinon, construire le chemin complet
        return "{$cdnUrl}/images/{$baseDir}/{$imagePath}";
    }

    /**
     * Génère les URLs pour un tableau d'images
     *
     * @param array|null $images Tableau des chemins d'images
     * @param string $baseDir Le dossier de base
     * @return array Tableau des URLs complètes
     */
    public static function getImageUrls(?array $images, string $baseDir = 'products'): array
    {
        if (empty($images)) {
            return [];
        }

        return array_map(function ($image) use ($baseDir) {
            return self::getImageUrl($image, $baseDir);
        }, $images);
    }

    /**
     * Génère l'URL d'une miniature
     *
     * @param string|null $imagePath Le chemin de l'image originale
     * @param string $size La taille de la miniature (small, medium, large)
     * @param string $baseDir Le dossier de base
     * @return string|null L'URL de la miniature ou de l'image originale
     */
    public static function getThumbnailUrl(?string $imagePath, string $size = 'medium', string $baseDir = 'products'): ?string
    {
        if (empty($imagePath)) {
            return null;
        }

        $cdnUrl = rtrim(config('app.cdn_image_url'), '/');
        
        // Extraire les informations du chemin de l'image
        $pathParts = explode('/', $imagePath);
        $filename = array_pop($pathParts);
        
        // Si le chemin contient déjà la structure complète
        if (count($pathParts) >= 2) {
            $folderPrefix = array_pop($pathParts);
        } else {
            // Fallback : utiliser le chemin tel quel
            return self::getImageUrl($imagePath, $baseDir);
        }

        // Construire l'URL de la miniature
        $thumbnailPath = "thumbnail/{$baseDir}/{$folderPrefix}/{$size}/{$filename}";
        
        return "{$cdnUrl}/images/{$thumbnailPath}";
    }

    /**
     * Génère les URLs des miniatures pour un tableau d'images
     *
     * @param array|null $images Tableau des chemins d'images
     * @param string $size La taille des miniatures
     * @param string $baseDir Le dossier de base
     * @return array Tableau des URLs de miniatures
     */
    public static function getThumbnailUrls(?array $images, string $size = 'medium', string $baseDir = 'products'): array
    {
        if (empty($images)) {
            return [];
        }

        return array_map(function ($image) use ($size, $baseDir) {
            return self::getThumbnailUrl($image, $size, $baseDir);
        }, $images);
    }

    /**
     * Génère l'URL d'une image de produit avec fallback vers l'image originale
     *
     * @param string|null $imagePath Le chemin de l'image
     * @param int|null $productId L'ID du produit (pour la logique de dossier)
     * @return string|null L'URL de l'image
     */
    public static function getProductImageUrl(?string $imagePath, ?int $productId = null): ?string
    {
        if (empty($imagePath)) {
            return null;
        }

        $cdnUrl = rtrim(config('app.cdn_image_url'), '/');

        // Si le chemin est déjà complet (contient products/)
        if (strpos($imagePath, 'products/') === 0) {
            return "{$cdnUrl}/images/{$imagePath}";
        }

        // Si on a un ID de produit, utiliser la logique de dossier
        if ($productId) {
            $folderPrefix = $productId < 1000 ? '0' : substr((string)$productId, 0, -3);
            return "{$cdnUrl}/images/products/{$folderPrefix}/{$imagePath}";
        }

        // Fallback : utiliser le chemin tel quel
        return "{$cdnUrl}/images/products/{$imagePath}";
    }

    /**
     * Génère les URLs pour les images principales d'un produit (2 premières)
     *
     * @param array|null $images Tableau des images
     * @param int|null $productId L'ID du produit
     * @return array Tableau des URLs des images principales
     */
    public static function getMainProductImageUrls(?array $images, ?int $productId = null): array
    {
        if (empty($images)) {
            return [];
        }

        $mainImages = array_slice($images, 0, 2);
        
        return array_map(function ($image) use ($productId) {
            return self::getProductImageUrl($image, $productId);
        }, $mainImages);
    }

    /**
     * Génère les URLs pour les images additionnelles d'un produit (au-delà des 2 premières)
     *
     * @param array|null $images Tableau des images
     * @param int|null $productId L'ID du produit
     * @return array Tableau des URLs des images additionnelles
     */
    public static function getAdditionalProductImageUrls(?array $images, ?int $productId = null): array
    {
        if (empty($images) || count($images) <= 2) {
            return [];
        }

        $additionalImages = array_slice($images, 2);
        
        return array_map(function ($image) use ($productId) {
            return self::getProductImageUrl($image, $productId);
        }, $additionalImages);
    }
}
