<?php

namespace App\Helpers;

class HelpUrlHelper
{
    /**
     * <PERSON><PERSON><PERSON> l'URL complète pour une page d'aide
     *
     * @param string $path Le chemin de la page d'aide (ex: 'centre-aide', 'guide-marchand')
     * @return string L'URL complète vers la page d'aide
     */
    public static function getHelpUrl(string $path): string
    {
        $baseUrl = config('services.admin_marchand_url', 'http://localhost:8001');
        return rtrim($baseUrl, '/') . '/help/' . ltrim($path, '/');
    }

    /**
     * Génère les URLs pour toutes les pages d'aide
     *
     * @return array Tableau associatif des URLs d'aide
     */
    public static function getAllHelpUrls(): array
    {
        return [
            'help_center' => self::getHelpUrl('centre-aide'),
            'merchant_guide' => self::getHelpUrl('guide-marchand'),
            'faq_inscription' => self::getHelpUrl('faq-inscription'),
            'contact_support' => self::getHelpUrl('contact-support'),
        ];
    }
}
