<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;

class TestFinalMeilisearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:test-final';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test final de l\'intégration Meilisearch';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🎯 Test final de l\'intégration Meilisearch');

        try {
            // Test 1: Connexion
            $this->info('1️⃣ Test de connexion...');
            $connection = $this->meilisearchService->testConnection();
            if ($connection['status'] === 'connected') {
                $this->info('✅ Connexion OK');
            } else {
                $this->error('❌ Connexion échouée');
                return Command::FAILURE;
            }

            // Test 2: Recherche de produits
            $this->info('2️⃣ Test de recherche de produits...');
            $results = $this->meilisearchService->searchProduits('', [], ['limit' => 2]);
            $this->line("   Total produits: {$results['total']}");
            $this->line("   Temps de traitement: {$results['processing_time']}ms");

            if (!empty($results['hits'])) {
                $produit = $results['hits'][0];
                $this->line("   Premier produit ID: {$produit['id']}");
                
                // Test des URLs d'images
                $this->info('3️⃣ Test des URLs d\'images...');
                $imageUrls = $produit['image_urls'] ?? null;
                $mainImageUrls = $produit['main_image_urls'] ?? null;
                $thumbnailUrls = $produit['thumbnail_urls'] ?? null;

                if ($imageUrls) {
                    $this->info('✅ image_urls présentes: ' . count($imageUrls) . ' images');
                    $this->line('   Première image: ' . ($imageUrls[0] ?? 'null'));
                } else {
                    $this->warn('⚠️  image_urls manquantes');
                }

                if ($mainImageUrls) {
                    $this->info('✅ main_image_urls présentes: ' . count($mainImageUrls) . ' images');
                } else {
                    $this->warn('⚠️  main_image_urls manquantes');
                }

                if ($thumbnailUrls) {
                    $this->info('✅ thumbnail_urls présentes');
                    $this->line('   Small thumbnails: ' . count($thumbnailUrls['small'] ?? []));
                } else {
                    $this->warn('⚠️  thumbnail_urls manquantes');
                }

                // Test du multilangue
                $this->info('4️⃣ Test du multilangue...');
                $nom = $produit['nom'] ?? null;
                if ($nom && is_string($nom)) {
                    $nomDecoded = json_decode($nom, true);
                    if ($nomDecoded && isset($nomDecoded['fr']) && isset($nomDecoded['en'])) {
                        $this->info('✅ Multilangue OK');
                        $this->line('   FR: ' . $nomDecoded['fr']);
                        $this->line('   EN: ' . $nomDecoded['en']);
                    } else {
                        $this->warn('⚠️  Format multilangue incorrect');
                    }
                } else {
                    $this->warn('⚠️  Nom manquant');
                }

                // Test des promotions
                $this->info('5️⃣ Test des promotions...');
                $isOnDiscount = $produit['is_on_discount'] ?? false;
                $discountPrice = $produit['discount_price'] ?? null;
                
                if ($discountPrice) {
                    $this->line("   Prix normal: {$produit['prix']}");
                    $this->line("   Prix promo: {$discountPrice}");
                    $this->line("   En promotion: " . ($isOnDiscount ? 'Oui' : 'Non'));
                } else {
                    $this->line("   Pas de promotion sur ce produit");
                }
            }

            // Test 3: Recherche de catégories
            $this->info('6️⃣ Test de recherche de catégories...');
            $categoriesResults = $this->meilisearchService->searchCategories('', [], ['limit' => 1]);
            $this->line("   Total catégories: {$categoriesResults['total']}");

            if (!empty($categoriesResults['hits'])) {
                $categorie = $categoriesResults['hits'][0];
                $this->line("   Première catégorie ID: {$categorie['id']}");
                
                $fullImageUrl = $categorie['full_image_url'] ?? null;
                if ($fullImageUrl) {
                    $this->info('✅ URLs d\'images catégories présentes');
                } else {
                    $this->warn('⚠️  URLs d\'images catégories manquantes');
                }
            }

            $this->info('🎉 Test final terminé avec succès !');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
