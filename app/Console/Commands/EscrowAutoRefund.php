<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EscrowService;
use App\Models\EscrowTransaction;
use App\Models\CommandePrincipale;
use Illuminate\Support\Facades\Log;

class EscrowAutoRefund extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'escrow:auto-refund 
                            {--dry-run : Afficher les transactions éligibles sans les traiter}
                            {--days=14 : Nombre de jours après lesquels rembourser les commandes non livrées}
                            {--limit=20 : Nombre maximum de transactions à traiter}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rembourse automatiquement les commandes non livrées après un délai';

    /**
     * Service d\'escrow
     */
    protected EscrowService $escrowService;

    /**
     * Create a new command instance.
     */
    public function __construct(EscrowService $escrowService)
    {
        parent::__construct();
        $this->escrowService = $escrowService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');
        $this->info("🔄 Démarrage du remboursement automatique (délai: {$days} jours)...");

        try {
            $eligibleTransactions = $this->getEligibleTransactions($days);

            if ($eligibleTransactions->isEmpty()) {
                $this->info('✅ Aucune transaction éligible pour remboursement automatique');
                return Command::SUCCESS;
            }

            if ($this->option('dry-run')) {
                $this->handleDryRun($eligibleTransactions);
            } else {
                $this->handleActualRefunds($eligibleTransactions);
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Erreur fatale: {$e->getMessage()}");
            Log::error('Erreur dans escrow:auto-refund', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Récupère les transactions éligibles pour remboursement automatique
     */
    private function getEligibleTransactions(int $days): \Illuminate\Database\Eloquent\Collection
    {
        $cutoffDate = now()->subDays($days);

        return EscrowTransaction::where('statut', 'held')
            ->where('en_litige', false)
            ->where('date_hold', '<=', $cutoffDate)
            ->whereHas('commandePrincipale', function ($query) {
                $query->whereIn('statut_global', ['PayementConfirme', 'EnPreparation', 'PartielLivré'])
                      ->whereDoesntHave('sousCommandes', function ($subQuery) {
                          $subQuery->where('statut', 'livre');
                      });
            })
            ->with(['commandePrincipale.sousCommandes'])
            ->limit($this->option('limit'))
            ->get();
    }

    /**
     * Gère le mode dry-run (simulation)
     */
    private function handleDryRun(\Illuminate\Database\Eloquent\Collection $transactions): void
    {
        $this->warn('🔍 MODE DRY-RUN - Aucune modification ne sera effectuée');

        $this->info("📋 {$transactions->count()} transaction(s) éligible(s) pour remboursement:");

        $headers = ['ID Escrow', 'Commande', 'Montant', 'Méthode', 'Date Hold', 'Statut Commande'];
        $rows = [];

        foreach ($transactions as $transaction) {
            $rows[] = [
                substr($transaction->id, 0, 8) . '...',
                $transaction->commandePrincipale->numero_commande,
                $transaction->montant_formate,
                strtoupper($transaction->payment_method),
                $transaction->date_hold->format('d/m/Y H:i'),
                $transaction->commandePrincipale->statut_global
            ];
        }

        $this->table($headers, $rows);

        $totalMontant = $transactions->sum('montant_total');
        $this->info("💰 Montant total à rembourser: " . number_format($totalMontant, 0, ',', ' ') . " FCFA");

        // Afficher les détails par méthode de paiement
        $byMethod = $transactions->groupBy('payment_method');
        $this->info("📊 Répartition par méthode de paiement:");
        foreach ($byMethod as $method => $methodTransactions) {
            $count = $methodTransactions->count();
            $amount = $methodTransactions->sum('montant_total');
            $this->info("   • " . strtoupper($method) . ": {$count} transaction(s), " . number_format($amount, 0, ',', ' ') . " FCFA");
        }
    }

    /**
     * Gère les remboursements effectifs
     */
    private function handleActualRefunds(\Illuminate\Database\Eloquent\Collection $transactions): void
    {
        $this->info("🚀 Traitement de {$transactions->count()} remboursement(s)...");

        $processed = [];
        $errors = [];

        foreach ($transactions as $transaction) {
            try {
                $this->info("💳 Remboursement de la commande {$transaction->commandePrincipale->numero_commande}...");

                $result = $this->escrowService->refundFundsToClient(
                    $transaction->commandePrincipale,
                    'Remboursement automatique - Commande non livrée dans les délais'
                );

                if ($result['success']) {
                    $processed[] = [
                        'escrow_transaction_id' => $transaction->id,
                        'commande_numero' => $transaction->commandePrincipale->numero_commande,
                        'montant_rembourse' => $result['montant_rembourse'],
                        'refund_id' => $result['refund_id']
                    ];
                    $this->info("   ✅ Remboursé: " . number_format($result['montant_rembourse'], 0, ',', ' ') . " FCFA");
                } else {
                    $errors[] = [
                        'escrow_transaction_id' => $transaction->id,
                        'error' => 'Échec du remboursement'
                    ];
                    $this->error("   ❌ Échec du remboursement");
                }

            } catch (\Exception $e) {
                $errors[] = [
                    'escrow_transaction_id' => $transaction->id,
                    'error' => $e->getMessage()
                ];
                $this->error("   ❌ Erreur: {$e->getMessage()}");
            }
        }

        // Afficher le résumé
        $this->info("📊 Résultats du traitement:");
        $this->info("   • Remboursements réussis: " . count($processed));
        $this->info("   • Erreurs: " . count($errors));

        if (!empty($processed)) {
            $this->info("✅ Remboursements effectués:");
            
            $headers = ['Commande', 'Montant Remboursé', 'ID Remboursement'];
            $rows = [];

            foreach ($processed as $refund) {
                $rows[] = [
                    $refund['commande_numero'],
                    number_format($refund['montant_rembourse'], 0, ',', ' ') . ' FCFA',
                    $refund['refund_id'] ?? 'N/A'
                ];
            }

            $this->table($headers, $rows);

            $totalRembourse = array_sum(array_column($processed, 'montant_rembourse'));
            $this->info("💰 Montant total remboursé: " . number_format($totalRembourse, 0, ',', ' ') . " FCFA");
        }

        if (!empty($errors)) {
            $this->error("❌ Erreurs rencontrées:");
            foreach ($errors as $error) {
                $this->error("   • Escrow {$error['escrow_transaction_id']}: {$error['error']}");
            }
        }

        // Mettre à jour les statuts des commandes remboursées
        $this->updateOrderStatuses($processed);
    }

    /**
     * Met à jour les statuts des commandes remboursées
     */
    private function updateOrderStatuses(array $processed): void
    {
        if (empty($processed)) {
            return;
        }

        $this->info("🔄 Mise à jour des statuts de commandes...");

        foreach ($processed as $refund) {
            try {
                $commande = CommandePrincipale::where('numero_commande', $refund['commande_numero'])->first();
                if ($commande) {
                    $commande->update([
                        'statut_global' => 'Remboursé',
                        'statut_paiement' => 'Remboursé',
                        'date_remboursement' => now(),
                        'raison_remboursement' => 'Remboursement automatique - Non livraison'
                    ]);

                    // Mettre à jour aussi les sous-commandes
                    $commande->sousCommandes()->update([
                        'statut' => 'annule',
                        'raison_annulation' => 'Remboursement automatique - Non livraison'
                    ]);
                }
            } catch (\Exception $e) {
                $this->error("   ❌ Erreur mise à jour commande {$refund['commande_numero']}: {$e->getMessage()}");
            }
        }

        $this->info("✅ Statuts mis à jour pour " . count($processed) . " commande(s)");
    }
}
