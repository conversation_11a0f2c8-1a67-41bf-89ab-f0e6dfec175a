<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductVariant;

class TestVariantImageUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:variant-image-urls {variant_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste les URLs d\'images des variants';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $variantId = $this->argument('variant_id');

        if ($variantId) {
            $variant = ProductVariant::find($variantId);
            if (!$variant) {
                $this->error("Variant {$variantId} non trouvé");
                return Command::FAILURE;
            }
        } else {
            $variant = ProductVariant::first();
            if (!$variant) {
                $this->error('Aucun variant trouvé');
                return Command::FAILURE;
            }
        }

        $this->info("🖼️  Test des URLs d'images pour le variant ID: {$variant->id}");

        try {
            // Afficher les données brutes
            $this->info('📋 Données brutes:');
            $this->line("   Produit ID: {$variant->produit_id}");
            $this->line("   SKU: " . ($variant->sku ?? 'null'));
            $this->line("   Prix supplément: {$variant->prix_supplement}");
            $this->line("   Stock: {$variant->stock}");
            $this->line("   Images brutes: " . json_encode($variant->images));

            // Tester l'accessor image_urls
            $this->info('🔗 Test de l\'accessor image_urls:');
            $imageUrls = $variant->image_urls;

            if (empty($imageUrls)) {
                $this->warn('⚠️  Aucune URL d\'image générée');
            } else {
                foreach ($imageUrls as $index => $url) {
                    $this->line("   Image {$index}: {$url}");

                    // Vérifier si l'URL est bien formatée (exclure le // de http://)
                    $pathPart = str_replace(['http://', 'https://'], '', $url);
                    if (str_contains($pathPart, '//')) {
                        $this->error("   ❌ Double slash détecté dans le chemin");
                    } elseif (substr_count($url, 'products/0/variants/') > 1) {
                        $this->error("   ❌ Duplication de chemin détectée");
                    } else {
                        $this->info("   ✅ URL bien formatée");
                    }
                }
            }

            // Tester avec différents types d'images
            $this->info('🧪 Test avec différents formats d\'images:');

            // Test 1: Image avec chemin complet
            $testImages1 = ['products/0/variants/2/test-image.jpg'];
            $this->line("   Test 1 - Chemin complet: " . json_encode($testImages1));
            $variant->images = $testImages1;
            $urls1 = $variant->image_urls;
            $this->line("   Résultat: " . json_encode($urls1));

            // Test 2: Image avec nom simple
            $testImages2 = ['simple-image.jpg'];
            $this->line("   Test 2 - Nom simple: " . json_encode($testImages2));
            $variant->images = $testImages2;
            $urls2 = $variant->image_urls;
            $this->line("   Résultat: " . json_encode($urls2));

            // Restaurer les images originales
            $variant->refresh();

            $this->info('🎉 Test terminé !');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
