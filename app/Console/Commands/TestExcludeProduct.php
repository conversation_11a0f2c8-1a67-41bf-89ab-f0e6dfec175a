<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;
use App\Models\Produit;

class TestExcludeProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:exclude-product';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste le filtre exclude_product avec Meilisearch';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Test du filtre exclude_product avec Meilisearch');

        try {
            // Récupérer un produit pour le test
            $produit = Produit::first();
            if (!$produit) {
                $this->error('❌ Aucun produit trouvé pour le test');
                return Command::FAILURE;
            }

            $this->line("Produit de test ID: {$produit->id}");
            $this->line("Catégorie: {$produit->categorie_id}");

            // Test 1: Recherche sans exclusion
            $this->info('1️⃣ Recherche sans exclusion...');
            $resultsSansExclusion = $this->meilisearchService->searchProduits('', ["categorie_id = {$produit->categorie_id}"], ['limit' => 10]);
            $this->line("   Total produits: {$resultsSansExclusion['total']}");
            
            $produitTrouve = false;
            foreach ($resultsSansExclusion['hits'] as $hit) {
                if ($hit['id'] == $produit->id) {
                    $produitTrouve = true;
                    break;
                }
            }
            
            if ($produitTrouve) {
                $this->info('✅ Produit trouvé dans la recherche normale');
            } else {
                $this->warn('⚠️  Produit non trouvé dans la recherche normale');
            }

            // Test 2: Recherche avec exclusion
            $this->info('2️⃣ Recherche avec exclusion...');
            $resultsAvecExclusion = $this->meilisearchService->searchProduits('', [
                "categorie_id = {$produit->categorie_id}",
                "id != {$produit->id}"
            ], ['limit' => 10]);
            $this->line("   Total produits: {$resultsAvecExclusion['total']}");
            
            $produitTrouveAvecExclusion = false;
            foreach ($resultsAvecExclusion['hits'] as $hit) {
                if ($hit['id'] == $produit->id) {
                    $produitTrouveAvecExclusion = true;
                    break;
                }
            }
            
            if (!$produitTrouveAvecExclusion) {
                $this->info('✅ Produit correctement exclu de la recherche');
            } else {
                $this->error('❌ Produit trouvé malgré l\'exclusion !');
            }

            // Test 3: Vérifier que le nombre de résultats a diminué
            if ($resultsAvecExclusion['total'] < $resultsSansExclusion['total']) {
                $this->info('✅ Le nombre de résultats a diminué avec l\'exclusion');
                $difference = $resultsSansExclusion['total'] - $resultsAvecExclusion['total'];
                $this->line("   Différence: {$difference} produit(s)");
            } else {
                $this->warn('⚠️  Le nombre de résultats n\'a pas changé');
            }

            // Test 4: Test via API simulée
            $this->info('3️⃣ Test via simulation API...');
            $request = new \Illuminate\Http\Request([
                'exclude_product' => $produit->id,
                'limit' => 10
            ]);

            // Simuler la construction des filtres
            $filters = $this->buildTestFilters($request);
            $this->line("   Filtres construits: " . json_encode($filters));

            if (in_array("id != {$produit->id}", $filters)) {
                $this->info('✅ Filtre exclude_product correctement ajouté');
            } else {
                $this->error('❌ Filtre exclude_product manquant');
            }

            $this->info('🎉 Test terminé !');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Simulation de la méthode buildMeilisearchFilters pour test
     */
    private function buildTestFilters(\Illuminate\Http\Request $request): array
    {
        $filters = [];

        // Exclure un produit spécifique (utile pour les produits similaires)
        if ($request->has('exclude_product')) {
            $excludeProductId = $request->input('exclude_product');
            $filters[] = "id != {$excludeProductId}";
        }

        return $filters;
    }
}
