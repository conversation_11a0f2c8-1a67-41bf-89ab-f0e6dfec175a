<?php

namespace App\Console\Commands;

use App\Services\UserInvitationService;
use Illuminate\Console\Command;

class CleanupExpiredInvitations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invitations:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Nettoyer les invitations expirées';

    /**
     * Execute the console command.
     */
    public function handle(UserInvitationService $invitationService): int
    {
        $this->info('Nettoyage des invitations expirées...');

        $cleaned = $invitationService->cleanupExpiredInvitations();

        $this->info("✅ {$cleaned} invitation(s) expirée(s) nettoyée(s).");

        return Command::SUCCESS;
    }
}
