<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use MeiliSearch\Client;
use MeiliSearch\Exceptions\MeiliSearchException;
use Illuminate\Support\Facades\Log;

class ConfigureMeilisearchIndexes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:configure-indexes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Configure les attributs des index Meilisearch';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔧 Configuration des index Meilisearch...');

        try {
            $config = config('meilisearch');
            $client = new Client($config['host'], $config['key']);

            foreach ($config['indexes'] as $indexKey => $indexConfig) {
                $this->info("⚙️  Configuration de l'index: {$indexConfig['name']}");
                
                $index = $client->index($indexConfig['name']);

                // Configurer les attributs de recherche
                if (isset($indexConfig['searchable_attributes'])) {
                    $this->line("   📝 Attributs de recherche...");
                    $task = $index->updateSearchableAttributes($indexConfig['searchable_attributes']);
                    $client->waitForTask($task['taskUid']);
                }

                // Configurer les attributs filtrables
                if (isset($indexConfig['filterable_attributes'])) {
                    $this->line("   🔍 Attributs filtrables...");
                    $task = $index->updateFilterableAttributes($indexConfig['filterable_attributes']);
                    $client->waitForTask($task['taskUid']);
                }

                // Configurer les attributs triables
                if (isset($indexConfig['sortable_attributes'])) {
                    $this->line("   📊 Attributs triables...");
                    $task = $index->updateSortableAttributes($indexConfig['sortable_attributes']);
                    $client->waitForTask($task['taskUid']);
                }

                // Configurer les règles de classement
                if (isset($indexConfig['ranking_rules'])) {
                    $this->line("   🏆 Règles de classement...");
                    $task = $index->updateRankingRules($indexConfig['ranking_rules']);
                    $client->waitForTask($task['taskUid']);
                }

                // Configurer les mots vides
                if (isset($indexConfig['stop_words'])) {
                    $this->line("   🚫 Mots vides...");
                    $task = $index->updateStopWords($indexConfig['stop_words']);
                    $client->waitForTask($task['taskUid']);
                }

                // Configurer les synonymes
                if (isset($indexConfig['synonyms'])) {
                    $this->line("   🔄 Synonymes...");
                    $task = $index->updateSynonyms($indexConfig['synonyms']);
                    $client->waitForTask($task['taskUid']);
                }

                $this->info("✅ Index {$indexConfig['name']} configuré");
            }

            $this->info('🎉 Configuration terminée avec succès !');
            $this->warn('💡 Maintenant indexez vos données avec:');
            $this->line('   php artisan meilisearch:index-products');
            $this->line('   php artisan meilisearch:index-categories');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la configuration: ' . $e->getMessage());
            Log::error('Erreur lors de la configuration des index Meilisearch', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }
}
