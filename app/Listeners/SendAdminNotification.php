<?php

namespace App\Listeners;

use App\Events\MerchantSubmissionReceived;
use App\Models\User;
use App\Notifications\AdminMerchantSubmissionNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class SendAdminNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(MerchantSubmissionReceived $event): void
    {
        try {
            // Récupérer tous les administrateurs
            $admins = User::where('is_admin', true)
                ->where('is_active', true)
                ->get();

            if ($admins->isEmpty()) {
                Log::warning('Aucun administrateur trouvé pour notifier la soumission marchand', [
                    'validation_id' => $event->validation->id,
                ]);
                return;
            }

            // Envoyer la notification à tous les admins
            Notification::send($admins, new AdminMerchantSubmissionNotification($event->validation));

            Log::info('Notification admin envoyée pour nouvelle soumission marchand', [
                'validation_id' => $event->validation->id,
                'admin_count' => $admins->count(),
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'envoi de notification admin', [
                'validation_id' => $event->validation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-lancer l'exception pour que la queue puisse retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(MerchantSubmissionReceived $event, \Throwable $exception): void
    {
        Log::error('Échec définitif de l\'envoi de notification admin', [
            'validation_id' => $event->validation->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
