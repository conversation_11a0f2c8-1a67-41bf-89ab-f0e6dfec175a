<?php

namespace App\Providers;

use App\Services\AdminNotificationService;
use App\Services\ReverbWebSocketService;
use App\Services\StockManagementService;
use App\Services\NotificationService;
use App\Services\CheckoutService;
use App\Services\CommissionCalculationService;
use App\Services\PaymentSplittingService;
use App\Services\EscrowService;
use App\Services\PayPalService;
use App\Services\DisputeService;
use App\Services\ClientMarchandMessagingService;
use Illuminate\Support\ServiceProvider;

/**
 * Service Provider pour les services business
 */
class BusinessServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Enregistrer StockManagementService comme singleton
        $this->app->singleton(StockManagementService::class, function ($app) {
            return new StockManagementService();
        });

        // Enregistrer NotificationService comme singleton
        $this->app->singleton(NotificationService::class, function ($app) {
            return new NotificationService();
        });

        // Enregistrer AdminNotificationService comme singleton
        $this->app->singleton(AdminNotificationService::class, function ($app) {
            return new AdminNotificationService();
        });

        // Enregistrer PayPalService comme singleton
        $this->app->singleton(PayPalService::class, function ($app) {
            return new PayPalService();
        });

        // Enregistrer EscrowService comme singleton
        $this->app->singleton(EscrowService::class, function ($app) {
            return new EscrowService(
                $app->make(PayPalService::class)
            );
        });

        // Enregistrer ReverbWebSocketService comme singleton
        $this->app->singleton(ReverbWebSocketService::class, function ($app) {
            return new ReverbWebSocketService();
        });

        // Enregistrer DisputeService comme singleton
        $this->app->singleton(DisputeService::class, function ($app) {
            return new DisputeService(
                $app->make(EscrowService::class),
                $app->make(NotificationService::class),
                $app->make(AdminNotificationService::class),
                $app->make(ReverbWebSocketService::class)
            );
        });

        // Enregistrer ClientMarchandMessagingService comme singleton
        $this->app->singleton(ClientMarchandMessagingService::class, function ($app) {
            return new ClientMarchandMessagingService(
                $app->make(NotificationService::class),
                $app->make(AdminNotificationService::class)
            );
        });

        // Enregistrer CheckoutService avec ses dépendances
        $this->app->singleton(CheckoutService::class, function ($app) {
            return new CheckoutService(
                $app->make(StockManagementService::class),
                $app->make(NotificationService::class),
                $app->make(PaymentSplittingService::class),
                $app->make(CommissionCalculationService::class),
                $app->make(EscrowService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
