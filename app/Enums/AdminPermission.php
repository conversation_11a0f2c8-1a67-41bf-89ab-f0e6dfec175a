<?php

namespace App\Enums;

enum AdminPermission: string
{
    // === GESTION DES UTILISATEURS ===
    case MANAGE_USERS = 'manage_users';
    case CREATE_ADMINS = 'create_admins';
    case EDIT_ADMINS = 'edit_admins';
    case DELETE_ADMINS = 'delete_admins';
    case MANAGE_ROLES = 'manage_roles';
    case VIEW_USER_LOGS = 'view_user_logs';

    // === GESTION DES MARCHANDS ===
    case VIEW_MERCHANTS = 'view_merchants';
    case CREATE_MERCHANTS = 'create_merchants';
    case EDIT_MERCHANTS = 'edit_merchants';
    case DELETE_MERCHANTS = 'delete_merchants';
    case VALIDATE_MERCHANTS = 'validate_merchants';
    case SUSPEND_MERCHANTS = 'suspend_merchants';
    case MANAGE_MERCHANT_DOCUMENTS = 'manage_merchant_documents';

    // === GESTION FINANCIÈRE ===
    case VIEW_FINANCES = 'view_finances';
    case MANAGE_PAYMENTS = 'manage_payments';
    case VIEW_BANKING_INFO = 'view_banking_info';
    case MANAGE_SUBSCRIPTIONS = 'manage_subscriptions';
    case MANAGE_COMMISSIONS = 'manage_commissions';
    case VIEW_FINANCIAL_REPORTS = 'view_financial_reports';
    case MANAGE_REFUNDS = 'manage_refunds';

    // === SUPPORT CLIENT ===
    case VIEW_SUPPORT = 'view_support';
    case MANAGE_TICKETS = 'manage_tickets';
    case MANAGE_DISPUTES = 'manage_disputes';
    case VIEW_CUSTOMER_DATA = 'view_customer_data';
    case MANAGE_REVIEWS = 'manage_reviews';

    // === GESTION DU CATALOGUE ===
    case VIEW_PRODUCTS = 'view_products';
    case MANAGE_PRODUCTS = 'manage_products';
    case MANAGE_CATEGORIES = 'manage_categories';
    case MODERATE_PRODUCTS = 'moderate_products';

    // === GESTION DES COMMANDES ===
    case VIEW_ORDERS = 'view_orders';
    case MANAGE_ORDERS = 'manage_orders';
    case VIEW_ORDER_DETAILS = 'view_order_details';
    case MANAGE_SHIPPING = 'manage_shipping';

    // === SYSTÈME ET CONFIGURATION ===
    case MANAGE_SETTINGS = 'manage_settings';
    case VIEW_LOGS = 'view_logs';
    case MANAGE_SYSTEM = 'manage_system';
    case MANAGE_BACKUPS = 'manage_backups';
    case VIEW_ANALYTICS = 'view_analytics';
    case MANAGE_NOTIFICATIONS = 'manage_notifications';

    // === MARKETING ===
    case MANAGE_PROMOTIONS = 'manage_promotions';
    case MANAGE_CAMPAIGNS = 'manage_campaigns';
    case VIEW_MARKETING_ANALYTICS = 'view_marketing_analytics';

    /**
     * Obtenir le label lisible de la permission
     */
    public function label(): string
    {
        return match($this) {
            // Gestion des utilisateurs
            self::MANAGE_USERS => 'Gérer les utilisateurs',
            self::CREATE_ADMINS => 'Créer des administrateurs',
            self::EDIT_ADMINS => 'Modifier les administrateurs',
            self::DELETE_ADMINS => 'Supprimer les administrateurs',
            self::MANAGE_ROLES => 'Gérer les rôles',
            self::VIEW_USER_LOGS => 'Voir les logs utilisateurs',

            // Gestion des marchands
            self::VIEW_MERCHANTS => 'Voir les marchands',
            self::CREATE_MERCHANTS => 'Créer des marchands',
            self::EDIT_MERCHANTS => 'Modifier les marchands',
            self::DELETE_MERCHANTS => 'Supprimer les marchands',
            self::VALIDATE_MERCHANTS => 'Valider les marchands',
            self::SUSPEND_MERCHANTS => 'Suspendre les marchands',
            self::MANAGE_MERCHANT_DOCUMENTS => 'Gérer les documents marchands',

            // Gestion financière
            self::VIEW_FINANCES => 'Voir les finances',
            self::MANAGE_PAYMENTS => 'Gérer les paiements',
            self::VIEW_BANKING_INFO => 'Voir les infos bancaires',
            self::MANAGE_SUBSCRIPTIONS => 'Gérer les abonnements',
            self::MANAGE_COMMISSIONS => 'Gérer les commissions',
            self::VIEW_FINANCIAL_REPORTS => 'Voir les rapports financiers',
            self::MANAGE_REFUNDS => 'Gérer les remboursements',

            // Support client
            self::VIEW_SUPPORT => 'Voir le support',
            self::MANAGE_TICKETS => 'Gérer les tickets',
            self::MANAGE_DISPUTES => 'Gérer les litiges',
            self::VIEW_CUSTOMER_DATA => 'Voir les données clients',
            self::MANAGE_REVIEWS => 'Gérer les avis',

            // Catalogue
            self::VIEW_PRODUCTS => 'Voir les produits',
            self::MANAGE_PRODUCTS => 'Gérer les produits',
            self::MANAGE_CATEGORIES => 'Gérer les catégories',
            self::MODERATE_PRODUCTS => 'Modérer les produits',

            // Commandes
            self::VIEW_ORDERS => 'Voir les commandes',
            self::MANAGE_ORDERS => 'Gérer les commandes',
            self::VIEW_ORDER_DETAILS => 'Voir les détails des commandes',
            self::MANAGE_SHIPPING => 'Gérer les expéditions',

            // Système
            self::MANAGE_SETTINGS => 'Gérer les paramètres',
            self::VIEW_LOGS => 'Voir les logs',
            self::MANAGE_SYSTEM => 'Gérer le système',
            self::MANAGE_BACKUPS => 'Gérer les sauvegardes',
            self::VIEW_ANALYTICS => 'Voir les analytics',
            self::MANAGE_NOTIFICATIONS => 'Gérer les notifications',

            // Marketing
            self::MANAGE_PROMOTIONS => 'Gérer les promotions',
            self::MANAGE_CAMPAIGNS => 'Gérer les campagnes',
            self::VIEW_MARKETING_ANALYTICS => 'Voir les analytics marketing',
        };
    }

    /**
     * Obtenir la catégorie de la permission
     */
    public function category(): string
    {
        return match($this) {
            self::MANAGE_USERS, self::CREATE_ADMINS, self::EDIT_ADMINS, 
            self::DELETE_ADMINS, self::MANAGE_ROLES, self::VIEW_USER_LOGS => 'Utilisateurs',

            self::VIEW_MERCHANTS, self::CREATE_MERCHANTS, self::EDIT_MERCHANTS,
            self::DELETE_MERCHANTS, self::VALIDATE_MERCHANTS, self::SUSPEND_MERCHANTS,
            self::MANAGE_MERCHANT_DOCUMENTS => 'Marchands',

            self::VIEW_FINANCES, self::MANAGE_PAYMENTS, self::VIEW_BANKING_INFO,
            self::MANAGE_SUBSCRIPTIONS, self::MANAGE_COMMISSIONS, self::VIEW_FINANCIAL_REPORTS,
            self::MANAGE_REFUNDS => 'Finances',

            self::VIEW_SUPPORT, self::MANAGE_TICKETS, self::MANAGE_DISPUTES,
            self::VIEW_CUSTOMER_DATA, self::MANAGE_REVIEWS => 'Support',

            self::VIEW_PRODUCTS, self::MANAGE_PRODUCTS, self::MANAGE_CATEGORIES,
            self::MODERATE_PRODUCTS => 'Catalogue',

            self::VIEW_ORDERS, self::MANAGE_ORDERS, self::VIEW_ORDER_DETAILS,
            self::MANAGE_SHIPPING => 'Commandes',

            self::MANAGE_SETTINGS, self::VIEW_LOGS, self::MANAGE_SYSTEM,
            self::MANAGE_BACKUPS, self::VIEW_ANALYTICS, self::MANAGE_NOTIFICATIONS => 'Système',

            self::MANAGE_PROMOTIONS, self::MANAGE_CAMPAIGNS,
            self::VIEW_MARKETING_ANALYTICS => 'Marketing',
        };
    }

    /**
     * Obtenir toutes les permissions groupées par catégorie
     */
    public static function getGrouped(): array
    {
        $grouped = [];
        foreach (self::cases() as $permission) {
            $category = $permission->category();
            $grouped[$category][] = $permission;
        }
        return $grouped;
    }
}
