<?php

namespace App\Filament\Resources\AdminRoleResource\Pages;

use App\Filament\Resources\AdminRoleResource;
use App\Enums\AdminPermission;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAdminRoles extends ListRecords
{
    protected static string $resource = AdminRoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->isSuperAdmin() ||
                    (auth()->user()?->hasAdminPermission(AdminPermission::MANAGE_ROLES) ?? false)
                ),
        ];
    }
}
