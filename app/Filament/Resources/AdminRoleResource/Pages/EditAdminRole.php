<?php

namespace App\Filament\Resources\AdminRoleResource\Pages;

use App\Filament\Resources\AdminRoleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAdminRole extends EditRecord
{
    protected static string $resource = AdminRoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn (): bool => 
                    $this->record->canBeDeleted() && 
                    (auth()->user()?->isSuperAdmin() ?? false)
                ),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // S'assurer que les permissions sont un tableau
        if (isset($data['permissions']) && is_string($data['permissions'])) {
            $data['permissions'] = json_decode($data['permissions'], true) ?? [];
        }

        // Empêcher la modification du slug pour les rôles système
        if ($this->record->is_system_role && !auth()->user()?->isSuperAdmin()) {
            unset($data['slug']);
            unset($data['is_system_role']);
        }

        return $data;
    }
}
