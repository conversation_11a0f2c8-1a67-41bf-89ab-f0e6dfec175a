<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AdminUserResource\Pages;
use App\Models\AdminUser;
use App\Models\AdminRole;
use App\Models\User;
use App\Enums\AdminPermission;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AdminUserResource extends Resource
{
    protected static ?string $model = AdminUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationLabel = 'Utilisateurs Admin';

    protected static ?string $modelLabel = 'Utilisateur Admin';

    protected static ?string $pluralModelLabel = 'Utilisateurs Admin';

    protected static ?string $navigationGroup = 'Gestion des Utilisateurs';

    protected static ?int $navigationSort = 2;

    public static function canAccess(): bool
    {
        return static::checkAdminPermission(AdminPermission::MANAGE_USERS);
    }

    /**
     * Helper pour vérifier les permissions admin avec fallback pour super admin
     */
    private static function checkAdminPermission(AdminPermission $permission): bool
    {
        $user = auth()->user();

        // Permettre l'accès aux super admins même sans AdminUser (pour la configuration initiale)
        if ($user?->isSuperAdmin()) {
            return true;
        }

        return $user?->hasAdminPermission($permission) ?? false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations utilisateur')
                    ->schema([
                        Forms\Components\TextInput::make('user.name')
                            ->label('Nom complet')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('user.email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->unique(
                                table: User::class,
                                column: 'email',
                                modifyRuleUsing: function ($rule, $livewire) {
                                    if ($livewire instanceof \Filament\Resources\Pages\EditRecord) {
                                        return $rule->ignore($livewire->record->user_id, 'id');
                                    }
                                    return $rule;
                                }
                            ),

                        Forms\Components\TextInput::make('user.password')
                            ->label('Mot de passe')
                            ->password()
                            ->dehydrateStateUsing(fn (?string $state): ?string =>
                                filled($state) ? Hash::make($state) : null
                            )
                            ->dehydrated(fn (?string $state): bool => filled($state))
                            ->required(fn (Forms\Get $get, string $operation): bool =>
                                $operation === 'create' && !$get('send_invitation')
                            )
                            ->helperText(fn (Forms\Get $get): string =>
                                $get('send_invitation')
                                    ? 'Laissez vide si vous envoyez une invitation'
                                    : 'Mot de passe requis'
                            ),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Rôle et permissions')
                    ->schema([
                        Forms\Components\Select::make('role_id')
                            ->label('Rôle')
                            ->options(AdminRole::active()->pluck('name', 'id'))
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('department')
                            ->label('Département')
                            ->options([
                                'management' => 'Direction',
                                'finance' => 'Finance',
                                'support' => 'Support',
                                'marketing' => 'Marketing',
                                'tech' => 'Technique',
                                'operations' => 'Opérations',
                                'legal' => 'Juridique',
                                'hr' => 'Ressources Humaines',
                            ])
                            ->searchable(),

                        Forms\Components\Select::make('access_level')
                            ->label('Niveau d\'accès')
                            ->options([
                                'read' => 'Lecture seule',
                                'write' => 'Lecture/Écriture',
                                'full' => 'Accès complet',
                                'super_admin' => 'Super Administrateur',
                            ])
                            ->default('read')
                            ->required(),

                        Forms\Components\CheckboxList::make('permissions')
                            ->label('Permissions spécifiques')
                            ->options(function () {
                                $grouped = AdminPermission::getGrouped();
                                $options = [];
                                foreach ($grouped as $category => $permissions) {
                                    foreach ($permissions as $permission) {
                                        $options[$permission->value] = $permission->label();
                                    }
                                }
                                return $options;
                            })
                            ->columns(3)
                            ->helperText('Permissions supplémentaires en plus de celles du rôle'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Paramètres')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Compte actif')
                            ->default(true),

                        Forms\Components\Toggle::make('send_invitation')
                            ->label('Envoyer une invitation par email')
                            ->default(true)
                            ->visible(fn (string $operation): bool => $operation === 'create')
                            ->helperText('Si activé, un email d\'invitation sera envoyé au lieu de créer le mot de passe directement'),

                        Forms\Components\Textarea::make('notes')
                            ->label('Notes internes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Nom')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('role.name')
                    ->label('Rôle')
                    ->badge()
                    ->color(fn (AdminUser $record): string => match($record->role?->slug) {
                        'super_admin' => 'danger',
                        'admin' => 'warning',
                        'finance_manager' => 'success',
                        'support_client' => 'info',
                        'merchant_manager' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('department')
                    ->label('Département')
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'management' => 'Direction',
                        'finance' => 'Finance',
                        'support' => 'Support',
                        'marketing' => 'Marketing',
                        'tech' => 'Technique',
                        'operations' => 'Opérations',
                        'legal' => 'Juridique',
                        'hr' => 'RH',
                        default => $state,
                    })
                    ->badge(),

                Tables\Columns\TextColumn::make('access_level')
                    ->label('Niveau d\'accès')
                    ->formatStateUsing(fn (string $state): string => match($state) {
                        'read' => 'Lecture',
                        'write' => 'Écriture',
                        'full' => 'Complet',
                        'super_admin' => 'Super Admin',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'super_admin' => 'danger',
                        'full' => 'warning',
                        'write' => 'success',
                        'read' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('last_login_at')
                    ->label('Dernière connexion')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Jamais'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role_id')
                    ->label('Rôle')
                    ->options(AdminRole::active()->pluck('name', 'id')),

                Tables\Filters\SelectFilter::make('department')
                    ->label('Département')
                    ->options([
                        'management' => 'Direction',
                        'finance' => 'Finance',
                        'support' => 'Support',
                        'marketing' => 'Marketing',
                        'tech' => 'Technique',
                        'operations' => 'Opérations',
                        'legal' => 'Juridique',
                        'hr' => 'RH',
                    ]),

                Tables\Filters\SelectFilter::make('access_level')
                    ->label('Niveau d\'accès')
                    ->options([
                        'read' => 'Lecture',
                        'write' => 'Écriture',
                        'full' => 'Complet',
                        'super_admin' => 'Super Admin',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Actif'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn (AdminUser $record): bool =>
                        static::checkAdminPermission(AdminPermission::EDIT_ADMINS)
                    ),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn (AdminUser $record): string => $record->is_active ? 'Désactiver' : 'Activer')
                    ->icon(fn (AdminUser $record): string => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (AdminUser $record): string => $record->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->visible(fn (AdminUser $record): bool =>
                        static::checkAdminPermission(AdminPermission::EDIT_ADMINS)
                    )
                    ->action(function (AdminUser $record): void {
                        $record->update(['is_active' => !$record->is_active]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn (): bool =>
                            static::checkAdminPermission(AdminPermission::DELETE_ADMINS)
                        ),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informations utilisateur')
                    ->schema([
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Nom complet'),
                        Infolists\Components\TextEntry::make('user.email')
                            ->label('Email'),
                        Infolists\Components\IconEntry::make('user.email_verified_at')
                            ->label('Email vérifié')
                            ->boolean(),
                        Infolists\Components\TextEntry::make('user.created_at')
                            ->label('Compte créé le')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Rôle et permissions')
                    ->schema([
                        Infolists\Components\TextEntry::make('role.name')
                            ->label('Rôle')
                            ->badge(),
                        Infolists\Components\TextEntry::make('department')
                            ->label('Département')
                            ->formatStateUsing(fn (?string $state): string => match($state) {
                                'management' => 'Direction',
                                'finance' => 'Finance',
                                'support' => 'Support',
                                'marketing' => 'Marketing',
                                'tech' => 'Technique',
                                'operations' => 'Opérations',
                                'legal' => 'Juridique',
                                'hr' => 'Ressources Humaines',
                                default => $state ?? 'Non défini',
                            }),
                        Infolists\Components\TextEntry::make('access_level')
                            ->label('Niveau d\'accès')
                            ->formatStateUsing(fn (string $state): string => match($state) {
                                'read' => 'Lecture seule',
                                'write' => 'Lecture/Écriture',
                                'full' => 'Accès complet',
                                'super_admin' => 'Super Administrateur',
                                default => $state,
                            })
                            ->badge(),
                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Compte actif')
                            ->boolean(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Activité')
                    ->schema([
                        Infolists\Components\TextEntry::make('last_login_at')
                            ->label('Dernière connexion')
                            ->dateTime()
                            ->placeholder('Jamais connecté'),
                        Infolists\Components\TextEntry::make('created_by')
                            ->label('Créé par')
                            ->formatStateUsing(fn (?int $state): string => 
                                $state ? User::find($state)?->name ?? 'Utilisateur supprimé' : 'Système'
                            ),
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Créé le')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Modifié le')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Notes')
                    ->schema([
                        Infolists\Components\TextEntry::make('notes')
                            ->label('Notes internes')
                            ->placeholder('Aucune note')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn (AdminUser $record): bool => !empty($record->notes)),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdminUsers::route('/'),
            'create' => Pages\CreateAdminUser::route('/create'),
            'view' => Pages\ViewAdminUser::route('/{record}'),
            'edit' => Pages\EditAdminUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with(['user', 'role']);
    }
}
