<?php

namespace App\Filament\Resources\ProduitResource\RelationManagers;

use App\Models\Size;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class SizesRelationManager extends RelationManager
{
    protected static string $relationship = 'sizes';

    protected static ?string $recordTitleAttribute = 'code';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('size_id')
                    ->label('Taille (Size)')
                    ->options(Size::where('is_active', true)->get()->pluck('full_name', 'id'))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->createOptionForm([
                        Forms\Components\TextInput::make('name_fr')
                            ->label('Nom (français)')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('name_en')
                            ->label('Nom (anglais)')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('code')
                            ->label('Code')
                            ->required()
                            ->maxLength(255)
                            ->unique(),
                        
                        Forms\Components\Select::make('category')
                            ->label('Catégorie')
                            ->options([
                                'clothing' => 'Vêtements (Clothing)',
                                'shoes' => 'Chaussures (Shoes)',
                                'accessories' => 'Accessoires (Accessories)',
                                'other' => 'Autre (Other)',
                            ])
                            ->required(),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('Actif')
                            ->default(true),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('name_fr')
                    ->label('Nom (français)')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('name_en')
                    ->label('Nom (anglais)')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('category')
                    ->label('Catégorie')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'other' => 'Autre (Other)',
                        default => $state,
                    })
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('Catégorie')
                    ->options([
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'other' => 'Autre (Other)',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->preloadRecordSelect()
                    ->label('Ajouter des tailles')
                    ->modalHeading('Ajouter des tailles au produit')
                    ->modalDescription('Sélectionnez les tailles disponibles pour ce produit')
                    ->multiple(),
                
                Tables\Actions\CreateAction::make()
                    ->label('Créer une nouvelle taille')
                    ->modalHeading('Créer une nouvelle taille')
                    ->using(function (array $data, string $model): Size {
                        return Size::create($data);
                    }),
            ])
            ->actions([
                Tables\Actions\DetachAction::make()
                    ->label('Retirer'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Retirer la sélection'),
                ]),
            ]);
    }
}
