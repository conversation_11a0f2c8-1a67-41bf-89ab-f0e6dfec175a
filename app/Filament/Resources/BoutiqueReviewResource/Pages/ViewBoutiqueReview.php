<?php

namespace App\Filament\Resources\BoutiqueReviewResource\Pages;

use App\Filament\Resources\BoutiqueReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Illuminate\Support\Facades\Auth;

class ViewBoutiqueReview extends ViewRecord
{
    protected static string $resource = BoutiqueReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informations de l\'avis')
                    ->schema([
                        TextEntry::make('marchand.nomEntreprise')
                            ->label('Boutique')
                            ->visible(fn () => Auth::user()?->adminUser),

                        TextEntry::make('name')
                            ->label('Nom du client'),

                        TextEntry::make('email')
                            ->label('Email du client')
                            ->visible(fn () => Auth::user()?->adminUser),

                        TextEntry::make('rating')
                            ->label('Note')
                            ->formatStateUsing(fn (int $state): string => str_repeat('⭐', $state) . " ({$state}/5)"),

                        TextEntry::make('title')
                            ->label('Titre de l\'avis'),

                        TextEntry::make('comment')
                            ->label('Commentaire')
                            ->columnSpanFull(),

                        TextEntry::make('created_at')
                            ->label('Date de création')
                            ->dateTime('d/m/Y à H:i'),
                    ])
                    ->columns(2),

                Section::make('Images de l\'avis')
                    ->schema([
                        RepeatableEntry::make('image_urls')
                            ->label('')
                            ->schema([
                                ImageEntry::make('url')
                                    ->label('')
                                    ->height(150)
                                    ->width(150),
                            ])
                            ->columns(4),
                    ])
                    ->visible(fn ($record) => !empty($record->image_urls)),

                Section::make('Statut et modération')
                    ->schema([
                        IconEntry::make('is_approved')
                            ->label('Approuvé')
                            ->boolean(),

                        IconEntry::make('is_verified')
                            ->label('Achat vérifié')
                            ->boolean(),

                        IconEntry::make('is_reported')
                            ->label('Signalé')
                            ->boolean(),

                        TextEntry::make('likes')
                            ->label('Likes')
                            ->badge()
                            ->color('success'),

                        TextEntry::make('dislikes')
                            ->label('Dislikes')
                            ->badge()
                            ->color('danger'),

                        TextEntry::make('report_reason')
                            ->label('Raison du signalement')
                            ->visible(fn ($record) => $record->is_reported && $record->report_reason)
                            ->columnSpanFull(),
                    ])
                    ->columns(3),

                Section::make('Réponse du marchand')
                    ->schema([
                        TextEntry::make('marchand_response')
                            ->label('Réponse')
                            ->columnSpanFull(),

                        TextEntry::make('marchand_response_at')
                            ->label('Date de réponse')
                            ->dateTime('d/m/Y à H:i'),
                    ])
                    ->visible(fn ($record) => !empty($record->marchand_response)),

                Section::make('Informations techniques')
                    ->schema([
                        TextEntry::make('ip_address')
                            ->label('Adresse IP')
                            ->visible(fn () => Auth::user()?->adminUser),

                        TextEntry::make('user_id')
                            ->label('ID utilisateur')
                            ->visible(fn () => Auth::user()?->adminUser),

                        TextEntry::make('updated_at')
                            ->label('Dernière modification')
                            ->dateTime('d/m/Y à H:i'),
                    ])
                    ->columns(2)
                    ->visible(fn () => Auth::user()?->adminUser)
                    ->collapsed(),
            ]);
    }
}
