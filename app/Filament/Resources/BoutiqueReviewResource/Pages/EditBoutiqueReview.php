<?php

namespace App\Filament\Resources\BoutiqueReviewResource\Pages;

use App\Filament\Resources\BoutiqueReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class EditBoutiqueReview extends EditRecord
{
    protected static string $resource = BoutiqueReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn () => Auth::user()?->adminUser?->hasPermission(\App\Enums\AdminPermission::DELETE_REVIEWS)),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        $user = Auth::user();
        
        if ($user?->adminUser) {
            return Notification::make()
                ->success()
                ->title('Avis modéré')
                ->body('Les modifications de modération ont été enregistrées.');
        }
        
        if ($user?->marchandUser) {
            return Notification::make()
                ->success()
                ->title('Réponse enregistrée')
                ->body('Votre réponse à l\'avis a été enregistrée.');
        }

        return Notification::make()
            ->success()
            ->title('Avis mis à jour')
            ->body('L\'avis a été mis à jour avec succès.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $user = Auth::user();
        
        // Si c'est un marchand qui répond
        if ($user?->marchandUser && isset($data['marchand_response'])) {
            $data['marchand_response_at'] = now();
        }
        
        return $data;
    }

    protected function afterSave(): void
    {
        $user = Auth::user();
        $record = $this->record;
        
        // Notifications spécifiques selon le type d'utilisateur
        if ($user?->adminUser) {
            // Log de l'action de modération
            activity()
                ->performedOn($record)
                ->causedBy($user)
                ->withProperties([
                    'action' => 'moderation',
                    'is_approved' => $record->is_approved,
                    'report_reason' => $record->report_reason,
                ])
                ->log('Avis modéré par un administrateur');
        }
        
        if ($user?->marchandUser && $record->marchand_response) {
            // Log de la réponse marchand
            activity()
                ->performedOn($record)
                ->causedBy($user)
                ->withProperties([
                    'action' => 'merchant_response',
                    'response_length' => strlen($record->marchand_response),
                ])
                ->log('Réponse ajoutée par le marchand');
        }
    }
}
