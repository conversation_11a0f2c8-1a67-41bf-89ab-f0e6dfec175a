<?php

namespace App\Filament\Resources\AdminUserResource\Pages;

use App\Filament\Resources\AdminUserResource;
use App\Enums\AdminPermission;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAdminUsers extends ListRecords
{
    protected static string $resource = AdminUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->isSuperAdmin() ||
                    (auth()->user()?->hasAdminPermission(AdminPermission::CREATE_ADMINS) ?? false)
                ),
        ];
    }
}
