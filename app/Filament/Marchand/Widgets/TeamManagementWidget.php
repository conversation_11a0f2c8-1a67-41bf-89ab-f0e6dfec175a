<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\MarchandUser;
use App\Enums\MarchandPermission;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TeamManagementWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected static ?int $sort = 3;

    public static function canView(): bool
    {
        return auth()->user()?->marchand &&
               (auth()->user()->id === auth()->user()->marchand->user_id ||
                (auth()->user()?->hasMarchandPermission(
                    MarchandPermission::VIEW_TEAM,
                    auth()->user()?->marchand
                ) ?? false));
    }

    protected function getStats(): array
    {
        $marchandId = auth()->user()?->marchand?->id;

        if (!$marchandId) {
            return [];
        }

        return [
            Stat::make('Membres de l\'équipe', $this->getTeamMembersCount($marchandId))
                ->description($this->getTeamMembersDescription($marchandId))
                ->descriptionIcon('heroicon-m-users')
                ->color('success')
                ->chart($this->getTeamMembersChart($marchandId)),

            Stat::make('Invitations en attente', $this->getPendingInvitationsCount($marchandId))
                ->description($this->getPendingInvitationsDescription($marchandId))
                ->descriptionIcon('heroicon-m-envelope')
                ->color($this->getPendingInvitationsCount($marchandId) > 0 ? 'warning' : 'success'),

            Stat::make('Connexions récentes', $this->getRecentLoginsCount($marchandId))
                ->description('Dernières 24h')
                ->descriptionIcon('heroicon-m-clock')
                ->color('info')
                ->chart($this->getRecentLoginsChart($marchandId)),

            Stat::make('Rôles actifs', $this->getActiveRolesCount($marchandId))
                ->description('Types de rôles utilisés')
                ->descriptionIcon('heroicon-m-key')
                ->color('primary'),
        ];
    }

    private function getTeamMembersCount(int $marchandId): int
    {
        return MarchandUser::where('marchand_id', $marchandId)
            ->where('is_active', true)
            ->count();
    }

    private function getTeamMembersDescription(int $marchandId): string
    {
        $managers = MarchandUser::where('marchand_id', $marchandId)
            ->where('is_active', true)
            ->whereIn('access_level', ['owner', 'manager'])
            ->count();
        
        return "{$managers} Gestionnaires";
    }

    private function getTeamMembersChart(int $marchandId): array
    {
        // Graphique des ajouts d'équipe sur les 7 derniers jours
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            $count = MarchandUser::where('marchand_id', $marchandId)
                ->whereDate('created_at', $date)
                ->count();
            $data[] = $count;
        }
        return $data;
    }

    private function getPendingInvitationsCount(int $marchandId): int
    {
        return MarchandUser::where('marchand_id', $marchandId)
            ->whereNotNull('invitation_token')
            ->where('invitation_expires_at', '>', now())
            ->count();
    }

    private function getPendingInvitationsDescription(int $marchandId): string
    {
        $expired = MarchandUser::where('marchand_id', $marchandId)
            ->whereNotNull('invitation_token')
            ->where('invitation_expires_at', '<=', now())
            ->count();
        
        return $expired > 0 ? "{$expired} expirées" : "Toutes valides";
    }

    private function getRecentLoginsCount(int $marchandId): int
    {
        return MarchandUser::where('marchand_id', $marchandId)
            ->where('is_active', true)
            ->whereHas('user', function($query) {
                $query->where('last_login_at', '>=', now()->subDay());
            })
            ->count();
    }

    private function getRecentLoginsChart(int $marchandId): array
    {
        // Graphique des connexions sur les 7 derniers jours
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = MarchandUser::where('marchand_id', $marchandId)
                ->where('is_active', true)
                ->whereHas('user', function($query) use ($date) {
                    $query->whereDate('last_login_at', $date->toDateString());
                })
                ->count();
            $data[] = $count;
        }
        return $data;
    }

    private function getActiveRolesCount(int $marchandId): int
    {
        return MarchandUser::where('marchand_id', $marchandId)
            ->where('is_active', true)
            ->distinct('role_id')
            ->count('role_id');
    }
}
