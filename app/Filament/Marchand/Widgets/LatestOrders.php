<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\SousCommandeVendeur;
use App\Services\CommandeAdapterService;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\MarchandPermission;
use App\Helpers\CurrencyHelper;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Collection;

class LatestOrders extends BaseWidget
{
    use HasPermissionChecks;

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;

    protected static ?string $heading = 'Dernières Sous-Commandes';

    public static function canView(): bool
    {
        return static::canViewMarchand(MarchandPermission::VIEW_ORDERS);
    }

    public function getTableRecords(): Collection
    {
        $marchandId = Auth::user()->marchand?->id;

        if (!$marchandId) {
            return collect();
        }
        $dernieresSousCommandes = new Collection();
        $commandeService = new CommandeAdapterService();
        $data = $commandeService->getDernieresSousCommandesMarchand($marchandId, 10);

        foreach ($data as $sousCommande) {
            $dernieresSousCommandes->add($sousCommande);
        }
        return $dernieresSousCommandes;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(SousCommandeVendeur::query()->where('id', null))
            ->columns([
                Tables\Columns\TextColumn::make('numero_sous_commande')
                    ->label('N° Sous-commande')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Numéro copié!')
                    ->copyMessageDuration(1500)
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('numero_commande_principale')
                    ->label('Commande principale')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('client_nom')
                    ->label('Client')
                    ->searchable()
                    ->default('Client inconnu'),

                Tables\Columns\TextColumn::make('montant_ttc')
                    ->label('Montant TTC')
                    ->money(CurrencyHelper::getFilamentCurrencyCode())
                    ->sortable(),

                Tables\Columns\TextColumn::make('statut')
                    ->label('Statut')
                    ->badge()
                    ->color(fn (string $state): string => match($state) {
                        'EnAttente' => 'warning',
                        'Confirmé' => 'info',
                        'EnPreparation' => 'primary',
                        'PrêtExpédition' => 'warning',
                        'Expédié' => 'info',
                        'EnTransit' => 'primary',
                        'Livré' => 'success',
                        'Annulé' => 'danger',
                        'Retourné' => 'danger',
                        'Remboursé' => 'danger',
                        default => 'gray'
                    })
                    ->icon(fn (string $state): string => match($state) {
                        'EnAttente' => 'heroicon-m-clock',
                        'Confirmé' => 'heroicon-m-check',
                        'EnPreparation' => 'heroicon-m-arrow-path',
                        'PrêtExpédition' => 'heroicon-m-archive-box',
                        'Expédié' => 'heroicon-m-truck',
                        'EnTransit' => 'heroicon-m-arrow-right',
                        'Livré' => 'heroicon-m-check-circle',
                        'Annulé' => 'heroicon-m-x-circle',
                        'Retourné' => 'heroicon-m-arrow-uturn-left',
                        'Remboursé' => 'heroicon-m-banknotes',
                        default => 'heroicon-m-question-mark-circle'
                    }),

                Tables\Columns\IconColumn::make('versement_effectué')
                    ->label('Versé')
                    ->boolean(),

                Tables\Columns\TextColumn::make('date_creation')
                    ->label('Date')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Voir')
                    ->icon('heroicon-m-eye')
                    ->color('primary')
                    ->url(fn ($record): string =>
                        route('filament.marchand.resources.commandes.view', ['record' => $record['id']])
                    ),
            ])
            ->emptyStateHeading('Aucune sous-commande')
            ->emptyStateDescription('Vos sous-commandes apparaîtront ici.')
            ->emptyStateIcon('heroicon-o-shopping-cart');
    }
}
