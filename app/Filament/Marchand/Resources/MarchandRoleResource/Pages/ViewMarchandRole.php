<?php

namespace App\Filament\Marchand\Resources\MarchandRoleResource\Pages;

use App\Filament\Marchand\Resources\MarchandRoleResource;
use App\Enums\MarchandPermission;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewMarchandRole extends ViewRecord
{
    protected static string $resource = MarchandRoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn (): bool =>
                    (!$this->record->is_system_role || auth()->user()?->isOwner(auth()->user()?->marchand)) &&
                    auth()->user()?->marchand &&
                    (auth()->user()->id === auth()->user()->marchand->user_id ||
                     (auth()->user()?->hasMarchandPermission(
                         MarchandPermission::MANAGE_ROLES,
                         auth()->user()?->marchand
                     ) ?? false))
                ),
            Actions\DeleteAction::make()
                ->visible(fn (): bool => 
                    $this->record->canBeDeleted() && 
                    (auth()->user()?->isOwner(auth()->user()?->marchand) ?? false)
                )
                ->requiresConfirmation()
                ->modalHeading('Supprimer ce rôle')
                ->modalDescription('Êtes-vous sûr de vouloir supprimer ce rôle ? Cette action est irréversible.')
                ->modalSubmitActionLabel('Supprimer'),
        ];
    }
}
