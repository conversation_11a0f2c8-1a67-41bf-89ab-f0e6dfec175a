<?php

namespace App\Filament\Marchand\Resources\MarchandRoleResource\Pages;

use App\Filament\Marchand\Resources\MarchandRoleResource;
use App\Enums\MarchandPermission;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMarchandRoles extends ListRecords
{
    protected static string $resource = MarchandRoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->visible(fn (): bool =>
                    auth()->user()?->marchand &&
                    (auth()->user()->id === auth()->user()->marchand->user_id ||
                     (auth()->user()?->hasMarchandPermission(
                         MarchandPermission::MANAGE_ROLES,
                         auth()->user()?->marchand
                     ) ?? false))
                ),
        ];
    }
}
