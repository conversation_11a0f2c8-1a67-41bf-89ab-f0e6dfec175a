<?php

namespace App\Filament\Marchand\Resources\MarchandTeamResource\Pages;

use App\Filament\Marchand\Resources\MarchandTeamResource;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class EditMarchandTeam extends EditRecord
{
    protected static string $resource = MarchandTeamResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return DB::transaction(function () use ($record, $data) {
            // Mettre à jour les données utilisateur si présentes
            if (isset($data['user'])) {
                $userData = $data['user'];
                
                // Ne pas écraser le mot de passe s'il est vide
                if (empty($userData['password'])) {
                    unset($userData['password']);
                }
                
                $record->user->update($userData);
                unset($data['user']);
            }

            // Mettre à jour le MarchandUser
            $record->update($data);

            return $record;
        });
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Charger les données de l'utilisateur
        $data['user'] = [
            'name' => $this->record->user->name,
            'email' => $this->record->user->email,
            'password' => '', // Ne pas pré-remplir le mot de passe
        ];

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // S'assurer que les permissions sont un tableau
        if (isset($data['permissions']) && is_string($data['permissions'])) {
            $data['permissions'] = json_decode($data['permissions'], true) ?? [];
        }

        return $data;
    }
}
