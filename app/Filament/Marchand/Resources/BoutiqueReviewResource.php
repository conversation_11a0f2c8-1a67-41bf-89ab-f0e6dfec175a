<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\BoutiqueReviewResource\Pages;
use App\Models\BoutiqueReview;
use App\Enums\MarchandPermission;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class BoutiqueReviewResource extends Resource
{
    protected static ?string $model = BoutiqueReview::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationLabel = 'Avis Clients';

    protected static ?string $modelLabel = 'Avis Client';

    protected static ?string $pluralModelLabel = 'Avis Clients';

    protected static ?string $navigationGroup = 'Support & Communication';

    protected static ?int $navigationSort = 1;

    public static function canViewAny(): bool
    {
        $user = Auth::user();
        
        if (!$user || !$user->marchandUser) {
            return false;
        }

        // Marchand propriétaire peut tout voir
        if ($user->marchandUser->access_level === 'owner') {
            return true;
        }

        // Vérification des permissions pour les autres
        return $user->marchandUser->hasPermission(MarchandPermission::VIEW_REVIEWS);
    }

    public static function canCreate(): bool
    {
        return false; // Les avis sont créés par les clients
    }

    public static function canEdit(Model $record): bool
    {
        $user = Auth::user();
        
        if (!$user || !$user->marchandUser) {
            return false;
        }

        // Vérifier que l'avis appartient au marchand connecté
        if ($record->marchand_id !== $user->marchandUser->marchand_id) {
            return false;
        }

        // Marchand propriétaire peut tout faire
        if ($user->marchandUser->access_level === 'owner') {
            return true;
        }

        // Vérification des permissions pour les autres
        return $user->marchandUser->hasPermission(MarchandPermission::RESPOND_TO_REVIEWS);
    }

    public static function canDelete(Model $record): bool
    {
        return false; // Les marchands ne peuvent pas supprimer les avis
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();
        $user = Auth::user();

        // Filtrer par marchand connecté
        if ($user && $user->marchandUser) {
            $query->where('marchand_id', $user->marchandUser->marchand_id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informations de l\'avis')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nom du client')
                            ->disabled(),
                        
                        Forms\Components\Select::make('rating')
                            ->label('Note')
                            ->options([
                                1 => '1 étoile',
                                2 => '2 étoiles',
                                3 => '3 étoiles',
                                4 => '4 étoiles',
                                5 => '5 étoiles',
                            ])
                            ->disabled(),
                        
                        Forms\Components\TextInput::make('title')
                            ->label('Titre')
                            ->disabled(),
                        
                        Forms\Components\Textarea::make('comment')
                            ->label('Commentaire')
                            ->disabled()
                            ->rows(4),
                    ]),

                Forms\Components\Section::make('Votre réponse')
                    ->schema([
                        Forms\Components\Textarea::make('marchand_response')
                            ->label('Réponse à l\'avis')
                            ->rows(4)
                            ->placeholder('Répondez à cet avis client...')
                            ->helperText('Votre réponse sera visible publiquement sous l\'avis.'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('rating')
                    ->label('Note')
                    ->formatStateUsing(fn (int $state): string => str_repeat('⭐', $state))
                    ->sortable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Titre')
                    ->limit(30)
                    ->searchable(),

                Tables\Columns\TextColumn::make('comment')
                    ->label('Commentaire')
                    ->limit(50)
                    ->searchable(),

                Tables\Columns\IconColumn::make('is_approved')
                    ->label('Approuvé')
                    ->boolean(),

                Tables\Columns\IconColumn::make('marchand_response')
                    ->label('Répondu')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !empty($record->marchand_response)),

                Tables\Columns\TextColumn::make('likes')
                    ->label('👍')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_verified')
                    ->label('Vérifié')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('is_approved')
                    ->label('Statut')
                    ->options([
                        true => 'Approuvé',
                        false => 'En attente',
                    ])
                    ->placeholder('Tous les statuts'),

                SelectFilter::make('rating')
                    ->label('Note')
                    ->options([
                        1 => '1 étoile',
                        2 => '2 étoiles',
                        3 => '3 étoiles',
                        4 => '4 étoiles',
                        5 => '5 étoiles',
                    ]),

                Filter::make('verified')
                    ->label('Achats vérifiés')
                    ->query(fn (Builder $query): Builder => $query->where('is_verified', true)),

                Filter::make('without_response')
                    ->label('Sans réponse')
                    ->query(fn (Builder $query): Builder => $query->whereNull('marchand_response')),

                Filter::make('with_images')
                    ->label('Avec images')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('images')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->label('Répondre')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->visible(fn (BoutiqueReview $record) => empty($record->marchand_response)),
            ])
            ->bulkActions([
                // Pas d'actions en masse pour les marchands
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBoutiqueReviews::route('/'),
            'view' => Pages\ViewBoutiqueReview::route('/{record}'),
            'edit' => Pages\EditBoutiqueReview::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        $user = Auth::user();
        
        if ($user?->marchandUser) {
            return static::getModel()::where('marchand_id', $user->marchandUser->marchand_id)
                ->where('is_approved', true)
                ->whereNull('marchand_response')
                ->count();
        }
        
        return null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getNavigationBadge() > 0 ? 'warning' : null;
    }
}
