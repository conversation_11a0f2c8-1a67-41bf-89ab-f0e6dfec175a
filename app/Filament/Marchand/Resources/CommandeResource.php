<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\CommandeResource\Pages;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\MarchandPermission;
use App\Models\SousCommandeVendeur;
use App\Services\CommandeAdapterService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class CommandeResource extends Resource
{
    use HasPermissionChecks;

    protected static ?string $model = SousCommandeVendeur::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationGroup = 'Commandes & Expéditions';

    protected static ?int $navigationSort = 2;

    protected static ?string $recordTitleAttribute = 'numero_sous_commande';

    public static function canAccess(): bool
    {
        return static::canViewMarchand(MarchandPermission::VIEW_ORDERS);
    }

    public static function canCreate(): bool
    {
        return false; // Les commandes ne sont pas créées manuellement
    }

    public static function canEdit(Model $record): bool
    {
        return static::canEditMarchand(MarchandPermission::MANAGE_ORDERS);
    }

    public static function canDelete(Model $record): bool
    {
        return static::canDeleteMarchand(MarchandPermission::MANAGE_ORDERS);
    }

    public static function canViewAny(): bool
    {
        return static::canViewMarchand(MarchandPermission::VIEW_ORDERS);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de la Sous-Commande')
                            ->schema([
                                Forms\Components\TextInput::make('numero_sous_commande')
                                    ->label('Numéro de sous-commande')
                                    ->disabled(),

                                Forms\Components\TextInput::make('commandePrincipale.numero_commande')
                                    ->label('Commande principale')
                                    ->disabled(),

                                Forms\Components\Select::make('statut')
                                    ->options([
                                        'EnAttente' => 'En attente',
                                        'Confirmé' => 'Confirmé',
                                        'EnPreparation' => 'En préparation',
                                        'PrêtExpédition' => 'Prêt pour expédition',
                                        'Expédié' => 'Expédié',
                                        'EnTransit' => 'En transit',
                                        'Livré' => 'Livré',
                                        'Annulé' => 'Annulé',
                                        'Retourné' => 'Retourné',
                                        'Remboursé' => 'Remboursé',
                                    ])
                                    ->required(),

                                Forms\Components\TextInput::make('montant_ttc')
                                    ->label('Montant TTC')
                                    ->disabled()
                                    ->prefix('€'),

                                Forms\Components\TextInput::make('montant_versement_marchand')
                                    ->label('Montant versement marchand')
                                    ->disabled()
                                    ->prefix('€'),

                                Forms\Components\DatePicker::make('date_expedition_prevue')
                                    ->label('Date d\'expédition prévue'),

                                Forms\Components\DatePicker::make('date_livraison_prevue')
                                    ->label('Date de livraison prévue'),

                                Forms\Components\TextInput::make('numero_suivi')
                                    ->label('Numéro de suivi')
                                    ->maxLength(100),

                                Forms\Components\TextInput::make('transporteur')
                                    ->label('Transporteur')
                                    ->maxLength(100),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),
                
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Client')
                            ->schema([
                                Forms\Components\TextInput::make('commandePrincipale.client.prenom')
                                    ->label('Prénom')
                                    ->disabled(),

                                Forms\Components\TextInput::make('commandePrincipale.client.nom')
                                    ->label('Nom')
                                    ->disabled(),

                                Forms\Components\TextInput::make('commandePrincipale.client.user.email')
                                    ->label('Email')
                                    ->disabled(),

                                Forms\Components\TextInput::make('commandePrincipale.client.telephone')
                                    ->label('Téléphone')
                                    ->disabled(),
                            ]),

                        Forms\Components\Section::make('Informations Livraison')
                            ->schema([
                                Forms\Components\TextInput::make('frais_livraison')
                                    ->label('Frais de livraison')
                                    ->disabled()
                                    ->prefix('€'),

                                Forms\Components\TextInput::make('zoneLivraison.nom')
                                    ->label('Zone de livraison')
                                    ->disabled(),

                                Forms\Components\TextInput::make('delai_preparation_jours')
                                    ->label('Délai de préparation (jours)')
                                    ->numeric(),

                                Forms\Components\Toggle::make('versement_effectué')
                                    ->label('Versement effectué')
                                    ->disabled(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('numero_sous_commande')
                    ->label('N° Sous-commande')
                    ->searchable()
                    ->copyable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('commandePrincipale.numero_commande')
                    ->label('Commande principale')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('commandePrincipale.client.nom')
                    ->label('Client')
                    ->formatStateUsing(fn (SousCommandeVendeur $record): string =>
                        ($record->commandePrincipale->client->prenom ?? '') . ' ' .
                        ($record->commandePrincipale->client->nom ?? 'Client supprimé')
                    )
                    ->searchable(),

                Tables\Columns\TextColumn::make('montant_ttc')
                    ->label('Montant TTC')
                    ->money('XOF')
                    ->sortable(),

                Tables\Columns\TextColumn::make('montant_versement_marchand')
                    ->label('Versement')
                    ->money('XOF')
                    ->sortable(),

                Tables\Columns\SelectColumn::make('statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'Confirmé' => 'Confirmé',
                        'EnPreparation' => 'En préparation',
                        'PrêtExpédition' => 'Prêt expédition',
                        'Expédié' => 'Expédié',
                        'EnTransit' => 'En transit',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'Retourné' => 'Retourné',
                        'Remboursé' => 'Remboursé',
                    ])
                    ->sortable(),

                Tables\Columns\IconColumn::make('versement_effectué')
                    ->label('Versé')
                    ->boolean(),

                Tables\Columns\TextColumn::make('date_creation')
                    ->label('Date de création')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('numero_suivi')
                    ->label('Suivi')
                    ->placeholder('Non disponible')
                    ->copyable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'Confirmé' => 'Confirmé',
                        'EnPreparation' => 'En préparation',
                        'PrêtExpédition' => 'Prêt pour expédition',
                        'Expédié' => 'Expédié',
                        'EnTransit' => 'En transit',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'Retourné' => 'Retourné',
                        'Remboursé' => 'Remboursé',
                    ]),

                Tables\Filters\TernaryFilter::make('versement_effectué')
                    ->label('Versement effectué')
                    ->placeholder('Tous')
                    ->trueLabel('Versé')
                    ->falseLabel('En attente'),

                Tables\Filters\Filter::make('date_creation')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Depuis le'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Jusqu\'au'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_creation', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date_creation', '<=', $date),
                            );
                    }),

                Tables\Filters\Filter::make('montant_min')
                    ->form([
                        Forms\Components\TextInput::make('montant_min')
                            ->label('Montant minimum')
                            ->numeric()
                            ->prefix('€'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['montant_min'],
                            fn (Builder $query, $montant): Builder => $query->where('montant_ttc', '>=', $montant),
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('voir_commande_principale')
                    ->label('Commande principale')
                    ->icon('heroicon-o-arrow-top-right-on-square')
                    ->color('info')
                    ->url(fn (SousCommandeVendeur $record): string =>
                        route('filament.admin.resources.commandes.view', [
                            'record' => $record->commande_principale_id
                        ])
                    )
                    ->openUrlInNewTab(),
                Tables\Actions\Action::make('voir_articles')
                    ->label('Articles')
                    ->icon('heroicon-o-list-bullet')
                    ->color('warning')
                    ->url(fn (SousCommandeVendeur $record): string =>
                        route('filament.marchand.resources.articles.index', [
                            'tableFilters[sous_commande_id][value]' => $record->id
                        ])
                    ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('update_status')
                        ->label('Mettre à jour le statut')
                        ->form([
                            Forms\Components\Select::make('statut')
                                ->label('Nouveau statut')
                                ->options([
                                    'EnAttente' => 'En attente',
                                    'Confirmé' => 'Confirmé',
                                    'EnPreparation' => 'En préparation',
                                    'PrêtExpédition' => 'Prêt pour expédition',
                                    'Expédié' => 'Expédié',
                                    'EnTransit' => 'En transit',
                                    'Livré' => 'Livré',
                                    'Annulé' => 'Annulé',
                                ])
                                ->required(),
                        ])
                        ->action(function (array $data, array $records): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'statut' => $data['statut'],
                                ]);
                            }
                        }),
                    Tables\Actions\BulkAction::make('marquer_expedie')
                        ->label('Marquer comme expédié')
                        ->icon('heroicon-o-truck')
                        ->color('success')
                        ->form([
                            Forms\Components\TextInput::make('numero_suivi')
                                ->label('Numéro de suivi')
                                ->required(),
                            Forms\Components\TextInput::make('transporteur')
                                ->label('Transporteur')
                                ->required(),
                        ])
                        ->action(function (array $data, array $records): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'statut' => 'Expédié',
                                    'numero_suivi' => $data['numero_suivi'],
                                    'transporteur' => $data['transporteur'],
                                    'date_expedition_reelle' => now(),
                                ]);
                            }
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCommandes::route('/'),
            'create' => Pages\CreateCommande::route('/create'),
            'view' => Pages\ViewCommande::route('/{record}'),
            'edit' => Pages\EditCommande::route('/{record}/edit'),
        ];
    }
    
    public static function getEloquentQuery(): Builder
    {
        // Utiliser notre service d'adaptation pour obtenir les sous-commandes du marchand connecté
        $marchandId = auth()->user()->marchand?->id;

        if (!$marchandId) {
            // Si pas de marchand connecté, retourner une query vide
            return parent::getEloquentQuery()->whereRaw('1 = 0');
        }

        return app(CommandeAdapterService::class)->getSousCommandesForMarchand($marchandId);
    }
}
