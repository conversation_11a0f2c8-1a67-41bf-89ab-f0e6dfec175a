<?php

namespace App\Filament\Marchand\Pages;

use App\Filament\Marchand\Widgets\MarchandStatsOverview;
use App\Filament\Marchand\Widgets\SalesChart;
use App\Filament\Marchand\Widgets\VersementsWidget;
use App\Filament\Marchand\Widgets\CommissionsWidget;
use App\Filament\Marchand\Widgets\LatestOrders;
use App\Filament\Marchand\Widgets\MarchandDisputesWidget;
use App\Filament\Marchand\Widgets\TrialAlertWidget;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected function getHeaderWidgets(): array
    {
        return [
            TrialAlertWidget::class,
            MarchandStatsOverview::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            SalesChart::class,
            MarchandDisputesWidget::class,
            LatestOrders::class, // ✅ Corrigé - utilise maintenant getTableRecords()
            VersementsWidget::class,
            CommissionsWidget::class,
        ];
    }
}
