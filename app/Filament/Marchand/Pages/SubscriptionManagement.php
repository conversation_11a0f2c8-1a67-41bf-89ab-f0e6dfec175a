<?php

namespace App\Filament\Marchand\Pages;

use App\Models\MarchandAbonnement;
use Filament\Pages\Page;
use Illuminate\Support\Carbon;

class SubscriptionManagement extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    
    protected static string $view = 'filament.marchand.pages.subscription-management';
    
    protected static ?string $navigationGroup = 'Mon Abonnement';
    
    protected static ?string $title = 'Mon Abonnement';
    
    protected static ?string $navigationLabel = 'Gérer mon abonnement';

    public function getViewData(): array
    {
        $marchand = auth()->user()->marchand;
        
        if (!$marchand) {
            return [
                'currentSubscription' => null,
                'availablePlans' => [],
                'isTrialActive' => false,
            ];
        }

        $currentSubscription = MarchandAbonnement::where('marchand_id', $marchand->id)
            ->where('statut', 'actif')
            ->first();

        $isTrialActive = $currentSubscription && 
                        $currentSubscription->type_abonnement === 'trial' && 
                        $currentSubscription->fin_periode_essai > now();

        $availablePlans = $this->getAvailablePlans($currentSubscription);

        return [
            'currentSubscription' => $currentSubscription,
            'availablePlans' => $availablePlans,
            'isTrialActive' => $isTrialActive,
            'trialDaysRemaining' => $isTrialActive ? 
                now()->diffInDays($currentSubscription->fin_periode_essai, false) : 0,
        ];
    }

    private function getAvailablePlans($currentSubscription): array
    {
        $plans = [
            'gratuit' => [
                'name' => 'Gratuit',
                'price' => 0,
                'period' => 'mois',
                'commission' => '5-10%',
                'features' => [
                    'Produits illimités',
                    'Support par email',
                    'Commission standard',
                ],
                'color' => 'gray',
                'popular' => false,
            ],
            'basique' => [
                'name' => 'Basique',
                'price' => 32797.85,
                'period' => 'mois',
                'commission' => '4-8%',
                'features' => [
                    'Toutes les fonctionnalités Gratuit',
                    'Support prioritaire (email + chat)',
                    'Commission réduite',
                    'Réduction logistique 5%',
                ],
                'color' => 'blue',
                'popular' => true,
            ],
            'premium' => [
                'name' => 'Premium',
                'price' => 65595.70,
                'period' => 'mois',
                'commission' => '3-6%',
                'features' => [
                    'Toutes les fonctionnalités Basique',
                    'Analytics avancées',
                    'Gestionnaire dédié',
                    'Commission très réduite',
                    'Réduction logistique 10%',
                ],
                'color' => 'purple',
                'popular' => false,
            ],
            'elite' => [
                'name' => 'Elite',
                'price' => 131191.40,
                'period' => 'mois',
                'commission' => '2-4%',
                'features' => [
                    'Toutes les fonctionnalités Premium',
                    'IA prédictive',
                    'Événements exclusifs',
                    'Commission minimale',
                    'Réduction logistique 15%',
                ],
                'color' => 'gold',
                'popular' => false,
            ],
        ];

        // Marquer le plan actuel
        if ($currentSubscription) {
            $currentType = $currentSubscription->type_abonnement === 'trial' ? 'basique' : $currentSubscription->type_abonnement;
            if (isset($plans[$currentType])) {
                $plans[$currentType]['current'] = true;
            }
        }

        return $plans;
    }
}
