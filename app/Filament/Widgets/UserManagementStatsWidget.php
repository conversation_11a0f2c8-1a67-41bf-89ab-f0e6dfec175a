<?php

namespace App\Filament\Widgets;

use App\Models\AdminUser;
use App\Models\MarchandUser;
use App\Models\User;
use App\Enums\AdminPermission;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class UserManagementStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected static ?int $sort = 2;

    public static function canView(): bool
    {
        return auth()->user()?->isSuperAdmin() ||
               (auth()->user()?->hasAdminPermission(AdminPermission::MANAGE_USERS) ?? false);
    }

    protected function getStats(): array
    {
        return [
            Stat::make('Utilisateurs Admin', $this->getAdminUsersCount())
                ->description($this->getAdminUsersDescription())
                ->descriptionIcon('heroicon-m-shield-check')
                ->color('success')
                ->chart($this->getAdminUsersChart()),

            Stat::make('Équipes Marchands', $this->getMarchandUsersCount())
                ->description($this->getMarchandUsersDescription())
                ->descriptionIcon('heroicon-m-users')
                ->color('info')
                ->chart($this->getMarchandUsersChart()),

            Stat::make('Invitations en attente', $this->getPendingInvitationsCount())
                ->description($this->getPendingInvitationsDescription())
                ->descriptionIcon('heroicon-m-envelope')
                ->color($this->getPendingInvitationsCount() > 0 ? 'warning' : 'success')
                ->chart($this->getPendingInvitationsChart()),

            Stat::make('Connexions récentes', $this->getRecentLoginsCount())
                ->description('Dernières 24h')
                ->descriptionIcon('heroicon-m-clock')
                ->color('primary')
                ->chart($this->getRecentLoginsChart()),
        ];
    }

    private function getAdminUsersCount(): int
    {
        return AdminUser::where('is_active', true)->count();
    }

    private function getAdminUsersDescription(): string
    {
        $superAdmins = AdminUser::where('is_active', true)
            ->where('access_level', 'super_admin')
            ->count();
        
        return "{$superAdmins} Super Admins";
    }

    private function getAdminUsersChart(): array
    {
        // Graphique des créations d'admin sur les 7 derniers jours
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            $count = AdminUser::whereDate('created_at', $date)->count();
            $data[] = $count;
        }
        return $data;
    }

    private function getMarchandUsersCount(): int
    {
        return MarchandUser::where('is_active', true)->count();
    }

    private function getMarchandUsersDescription(): string
    {
        $owners = MarchandUser::where('is_active', true)
            ->where('access_level', 'owner')
            ->count();
        
        return "{$owners} Propriétaires";
    }

    private function getMarchandUsersChart(): array
    {
        // Graphique des ajouts d'équipe sur les 7 derniers jours
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            $count = MarchandUser::whereDate('created_at', $date)->count();
            $data[] = $count;
        }
        return $data;
    }

    private function getPendingInvitationsCount(): int
    {
        $adminInvitations = User::whereNotNull('password_reset_token')
            ->where('password_reset_expires_at', '>', now())
            ->whereHas('adminUser')
            ->count();

        $marchandInvitations = MarchandUser::whereNotNull('invitation_token')
            ->where('invitation_expires_at', '>', now())
            ->count();

        return $adminInvitations + $marchandInvitations;
    }

    private function getPendingInvitationsDescription(): string
    {
        $adminInvitations = User::whereNotNull('password_reset_token')
            ->where('password_reset_expires_at', '>', now())
            ->whereHas('adminUser')
            ->count();

        $marchandInvitations = MarchandUser::whereNotNull('invitation_token')
            ->where('invitation_expires_at', '>', now())
            ->count();

        return "{$adminInvitations} Admin, {$marchandInvitations} Marchand";
    }

    private function getPendingInvitationsChart(): array
    {
        // Graphique des invitations sur les 7 derniers jours
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            
            $adminInvitations = User::whereNotNull('password_reset_token')
                ->whereDate('password_reset_expires_at', '>=', $date)
                ->whereHas('adminUser')
                ->count();

            $marchandInvitations = MarchandUser::whereNotNull('invitation_token')
                ->whereDate('invitation_expires_at', '>=', $date)
                ->count();

            $data[] = $adminInvitations + $marchandInvitations;
        }
        return $data;
    }

    private function getRecentLoginsCount(): int
    {
        return User::where('last_login_at', '>=', now()->subDay())->count();
    }

    private function getRecentLoginsChart(): array
    {
        // Graphique des connexions sur les 7 derniers jours
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = User::whereDate('last_login_at', $date->toDateString())->count();
            $data[] = $count;
        }
        return $data;
    }
}
