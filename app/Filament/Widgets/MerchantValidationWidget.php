<?php

namespace App\Filament\Widgets;

use App\Models\MerchantValidation;
use App\Filament\Traits\HasPermissionChecks;
use App\Enums\AdminPermission;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class MerchantValidationWidget extends BaseWidget
{
    use HasPermissionChecks;

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = 2;

    protected static ?string $heading = 'Validations Marchands en Attente';

    public static function canView(): bool
    {
        return static::canViewAdmin(AdminPermission::VALIDATE_MERCHANTS);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                MerchantValidation::query()
                    ->where('status', 'EN_ATTENTE_VALIDATION')
                    ->with(['user'])
                    ->latest('submitted_at')
            )
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Nom')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('business_info.nomEntreprise')
                    ->label('Entreprise')
                    ->getStateUsing(fn ($record) => $record->business_info['nomEntreprise'] ?? 'Non spécifié')
                    ->searchable(),
                Tables\Columns\TextColumn::make('business_info.type_business')
                    ->label('Type')
                    ->getStateUsing(fn ($record) => match($record->business_info['type_business'] ?? '') {
                        'individuel' => 'Individuel',
                        'entreprise' => 'Entreprise',
                        'cooperative' => 'Coopérative',
                        'grande_entreprise' => 'Grande entreprise',
                        default => 'Non spécifié'
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Individuel' => 'info',
                        'Entreprise' => 'success',
                        'Coopérative' => 'warning',
                        'Grande entreprise' => 'primary',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('submitted_at')
                    ->label('Soumis le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('days_waiting')
                    ->label('En attente depuis')
                    ->getStateUsing(fn ($record) => $record->submitted_at ? $record->submitted_at->diffForHumans() : 'N/A')
                    ->badge()
                    ->color(function ($record): string {
                        if (!$record->submitted_at) return 'gray';
                        $days = $record->submitted_at->diffInDays(now());
                        return match (true) {
                            $days <= 1 => 'success',
                            $days <= 3 => 'warning',
                            default => 'danger',
                        };
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('Examiner')
                    ->icon('heroicon-m-eye')
                    ->color('info')
                    ->url(fn (MerchantValidation $record): string =>
                        route('filament.admin.resources.merchant-validations.view', $record)
                    ),
                Tables\Actions\Action::make('approve')
                    ->label('Approuver')
                    ->icon('heroicon-m-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Approuver ce marchand')
                    ->modalDescription(fn (MerchantValidation $record) =>
                        'Êtes-vous sûr de vouloir approuver ' . $record->user->name . ' ?'
                    )
                    ->action(function (MerchantValidation $record) {
                        $service = new \App\Services\MerchantValidationService();
                        $result = $service->approuverMarchand($record->id, auth()->id());

                        if ($result['success']) {
                            \Filament\Notifications\Notification::make()
                                ->title('Marchand approuvé avec succès')
                                ->success()
                                ->send();
                        } else {
                            \Filament\Notifications\Notification::make()
                                ->title('Erreur lors de l\'approbation')
                                ->body($result['message'])
                                ->danger()
                                ->send();
                        }
                    }),
                Tables\Actions\Action::make('reject')
                    ->label('Rejeter')
                    ->icon('heroicon-m-x-circle')
                    ->color('danger')
                    ->form([
                        \Filament\Forms\Components\Textarea::make('rejection_reason')
                            ->label('Raison du rejet')
                            ->required()
                            ->maxLength(1000),
                    ])
                    ->modalHeading('Rejeter ce marchand')
                    ->modalDescription(fn (MerchantValidation $record) =>
                        'Pourquoi rejetez-vous la demande de ' . $record->user->name . ' ?'
                    )
                    ->action(function (MerchantValidation $record, array $data) {
                        $service = new \App\Services\MerchantValidationService();
                        $result = $service->rejeterMarchand($record->id, auth()->id(), $data['rejection_reason']);

                        if ($result['success']) {
                            \Filament\Notifications\Notification::make()
                                ->title('Marchand rejeté')
                                ->success()
                                ->send();
                        } else {
                            \Filament\Notifications\Notification::make()
                                ->title('Erreur lors du rejet')
                                ->body($result['message'])
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->emptyStateHeading('Aucune validation en attente')
            ->emptyStateDescription('Toutes les demandes de marchands ont été traitées.')
            ->emptyStateIcon('heroicon-o-shield-check')
            ->defaultSort('submitted_at', 'desc')
            ->paginated([5, 10, 25]);
    }


}
