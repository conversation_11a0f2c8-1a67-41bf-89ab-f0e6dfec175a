<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Produit;
use App\Models\Categorie;
use App\Models\Marchand;
use App\Services\MeilisearchService;

class TestMeilisearchSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:test-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Teste la synchronisation automatique avec Meilisearch';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Test de synchronisation Meilisearch...');

        try {
            // Vérifier la connexion
            $this->info('🔍 Vérification de la connexion...');
            $connection = $this->meilisearchService->testConnection();
            if ($connection['status'] !== 'connected') {
                $this->error('❌ Connexion Meilisearch échouée');
                return Command::FAILURE;
            }
            $this->info('✅ Connexion OK');

            // Compter les produits avant
            $this->info('📊 Comptage des produits avant test...');
            $resultsBefore = $this->meilisearchService->searchProduits('', [], ['limit' => 1]);
            $countBefore = $resultsBefore['total'];
            $this->line("   Produits indexés avant: {$countBefore}");

            // Créer un produit de test
            $this->info('🆕 Création d\'un produit de test...');
            
            // Récupérer une catégorie et un marchand existants
            $categorie = Categorie::first();
            $marchand = Marchand::first();
            
            if (!$categorie || !$marchand) {
                $this->error('❌ Aucune catégorie ou marchand trouvé. Créez-en d\'abord.');
                return Command::FAILURE;
            }

            $produit = new Produit();
            $produit->nom = 'Produit Test Meilisearch ' . now()->format('H:i:s');
            $produit->description = 'Description du produit de test pour Meilisearch';
            $produit->prix = 99.99;
            $produit->stock = 10;
            $produit->categorie_id = $categorie->id;
            $produit->marchand_id = $marchand->id;
            $produit->product_code = 'TEST-' . uniqid();
            $produit->marque = 'Test Brand';
            $produit->images = json_encode(['test-image.jpg']);
            
            $this->line("   Nom: {$produit->nom}");
            $this->line("   Catégorie: {$categorie->nom}");
            $this->line("   Marchand: {$marchand->nomEntreprise}");

            // Sauvegarder (l'observer devrait se déclencher)
            $produit->save();
            $this->info('✅ Produit créé avec ID: ' . $produit->id);

            // Attendre un peu pour la synchronisation
            $this->info('⏳ Attente de la synchronisation...');
            sleep(2);

            // Compter les produits après
            $this->info('📊 Comptage des produits après test...');
            $resultsAfter = $this->meilisearchService->searchProduits('', [], ['limit' => 1]);
            $countAfter = $resultsAfter['total'];
            $this->line("   Produits indexés après: {$countAfter}");

            // Vérifier si le produit est indexé
            $this->info('🔍 Recherche du produit créé...');
            $searchResults = $this->meilisearchService->searchProduits($produit->nom);
            $found = false;
            foreach ($searchResults['hits'] as $hit) {
                if ($hit['id'] == $produit->id) {
                    $found = true;
                    break;
                }
            }

            if ($found) {
                $this->info('✅ Produit trouvé dans Meilisearch !');
                $this->line("   Temps de traitement: {$searchResults['processing_time']}ms");
            } else {
                $this->warn('⚠️  Produit non trouvé dans Meilisearch');
            }

            // Test de modification
            $this->info('✏️  Test de modification...');
            $produit->nom = $produit->nom . ' (Modifié)';
            $produit->save();
            
            sleep(1);
            
            $searchResults = $this->meilisearchService->searchProduits('Modifié');
            $foundModified = false;
            foreach ($searchResults['hits'] as $hit) {
                if ($hit['id'] == $produit->id && str_contains($hit['nom'], 'Modifié')) {
                    $foundModified = true;
                    break;
                }
            }

            if ($foundModified) {
                $this->info('✅ Modification synchronisée !');
            } else {
                $this->warn('⚠️  Modification non synchronisée');
            }

            // Nettoyage
            $this->info('🧹 Nettoyage...');
            $produit->delete();
            
            sleep(1);
            
            $resultsAfterDelete = $this->meilisearchService->searchProduits($produit->nom);
            $foundAfterDelete = false;
            foreach ($resultsAfterDelete['hits'] as $hit) {
                if ($hit['id'] == $produit->id) {
                    $foundAfterDelete = true;
                    break;
                }
            }

            if (!$foundAfterDelete) {
                $this->info('✅ Suppression synchronisée !');
            } else {
                $this->warn('⚠️  Suppression non synchronisée');
            }

            $this->info('🎉 Test terminé avec succès !');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors du test: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
