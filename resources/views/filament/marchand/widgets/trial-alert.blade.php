<div>
    @if($showAlert)
    <div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
        <div class="flex items-center gap-4">
            <!-- Icône -->
            <div class="flex h-12 w-12 items-center justify-center rounded-xl {{ $isLastDay ? 'bg-red-100 dark:bg-red-900/20' : ($isLastWeek ? 'bg-orange-100 dark:bg-orange-900/20' : 'bg-blue-100 dark:bg-blue-900/20') }}">
                @if($isLastDay)
                    <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                @elseif($isLastWeek)
                    <svg class="h-6 w-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                @else
                    <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                @endif
            </div>

            <!-- Contenu principal -->
            <div class="flex-1">
                <h3 class="text-lg font-semibold {{ $isLastDay ? 'text-red-900 dark:text-red-100' : ($isLastWeek ? 'text-orange-900 dark:text-orange-100' : 'text-blue-900 dark:text-blue-100') }}">
                    @if($isLastDay)
                        🚨 Votre période d'essai se termine aujourd'hui !
                    @elseif($daysRemaining == 1)
                        ⏰ Votre période d'essai se termine demain
                    @else
                        ✨ Période d'essai active - {{ $daysRemaining }} jours restants
                    @endif
                </h3>

                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    @if($isLastDay)
                        Votre compte passera automatiquement au plan gratuit à minuit. Choisissez un abonnement pour continuer à bénéficier des avantages du plan Basique.
                    @else
                        Profitez de toutes les fonctionnalités du plan Basique jusqu'au {{ $trialEndDate->format('d/m/Y') }}.
                        Après cette date, votre compte passera automatiquement au plan gratuit.
                    @endif
                </p>

                <!-- Avantages du trial -->
                <div class="mt-3 flex flex-wrap gap-2">
                    <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        ✅ Commission 4-8%
                    </span>
                    <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        ✅ Support prioritaire
                    </span>
                    <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        ✅ Réduction logistique 5%
                    </span>
                </div>
            </div>

            <!-- Bouton d'action -->
            <div class="flex flex-col gap-2">
                <a href="{{ route('subscription.choose') }}"
                   class="inline-flex items-center justify-center rounded-lg {{ $isLastDay ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700' }} px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900">
                    @if($isLastDay)
                        🚀 Choisir un plan maintenant
                    @else
                        🚀 Upgrade Your Plan
                    @endif
                </a>

                @if(!$isLastDay)
                    <span class="text-xs text-center text-gray-500 dark:text-gray-400">
                        Ou continuez votre essai
                    </span>
                @endif
            </div>
        </div>

        <!-- Barre de progression -->
        <div class="mt-4">
            @php
                $totalDays = 14;
                $progressPercentage = max(0, min(100, (($totalDays - $daysRemaining) / $totalDays) * 100));
            @endphp

            <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                <span>Progression de l'essai</span>
                <span>{{ round($progressPercentage) }}% utilisé</span>
            </div>

            <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                <div class="h-2 rounded-full transition-all duration-300 {{ $isLastDay ? 'bg-red-500' : ($isLastWeek ? 'bg-orange-500' : 'bg-blue-500') }}"
                     style="width: {{ $progressPercentage }}%"></div>
            </div>
        </div>
    </div>
    @endif

</div>

