<x-filament-panels::page>
    <div class="space-y-6">
        @if($isTrialActive)
            <!-- Alerte Trial Active -->
            <div class="rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border border-blue-200 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800">
                <div class="flex items-start gap-4">
                    <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-100 dark:bg-blue-900/30">
                        <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">
                            ✨ Période d'essai active - {{ $trialDaysRemaining }} jours restants
                        </h3>
                        <p class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                            Vous profitez actuellement de toutes les fonctionnalités du plan Basique gratuitement jusqu'au 
                            {{ $currentSubscription->fin_periode_essai->format('d/m/Y à H:i') }}.
                        </p>
                        
                        <!-- Fonctionnalités du trial -->
                        <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-3">
                            <div class="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                                <svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Commission 4-8%
                            </div>
                            <div class="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                                <svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Support prioritaire
                            </div>
                            <div class="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                                <svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Réduction logistique 5%
                            </div>
                            <div class="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                                <svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Produits illimités
                            </div>
                        </div>
                        
                        <!-- Barre de progression -->
                        @php
                            $totalDays = 14;
                            $progressPercentage = max(0, min(100, (($totalDays - $trialDaysRemaining) / $totalDays) * 100));
                        @endphp
                        
                        <div class="mt-4">
                            <div class="flex items-center justify-between text-xs text-blue-600 dark:text-blue-400 mb-1">
                                <span>Progression de l'essai</span>
                                <span>{{ round($progressPercentage) }}% utilisé</span>
                            </div>
                            <div class="w-full bg-blue-200 rounded-full h-2 dark:bg-blue-800">
                                <div class="h-2 rounded-full bg-blue-500 transition-all duration-300" style="width: {{ $progressPercentage }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Abonnement actuel -->
        @if($currentSubscription)
            <div class="rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Mon abonnement actuel</h2>
                
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                            @if($currentSubscription->type_abonnement === 'trial')
                                Plan Basique (Essai gratuit)
                            @else
                                Plan {{ ucfirst($currentSubscription->type_abonnement) }}
                            @endif
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            @if($currentSubscription->type_abonnement === 'trial')
                                Gratuit jusqu'au {{ $currentSubscription->fin_periode_essai->format('d/m/Y') }}
                            @else
                                {{ number_format($currentSubscription->prix_mensuel, 0, ',', ' ') }} FCFA/mois
                            @endif
                        </p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Commission: {{ $currentSubscription->commission_taux_min }}% - {{ $currentSubscription->commission_taux_max }}%
                        </p>
                    </div>
                    
                    @if($currentSubscription->type_abonnement !== 'elite')
                        <a href="{{ route('subscription.choose') }}" 
                           class="inline-flex items-center justify-center rounded-lg bg-blue-600 hover:bg-blue-700 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors">
                            🚀 Upgrade Your Plan
                        </a>
                    @endif
                </div>
            </div>
        @endif

        <!-- Plans disponibles -->
        <div class="rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Plans disponibles</h2>
                <a href="{{ route('subscription.choose') }}" 
                   class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                    Voir tous les détails →
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                @foreach($availablePlans as $planKey => $plan)
                    <div class="relative rounded-lg border {{ isset($plan['current']) ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700' }} p-4">
                        @if($plan['popular'])
                            <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                <span class="bg-blue-600 text-white text-xs font-medium px-3 py-1 rounded-full">Populaire</span>
                            </div>
                        @endif
                        
                        @if(isset($plan['current']))
                            <div class="absolute -top-2 right-4">
                                <span class="bg-green-600 text-white text-xs font-medium px-3 py-1 rounded-full">Actuel</span>
                            </div>
                        @endif
                        
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $plan['name'] }}</h3>
                            <div class="mt-2">
                                @if($plan['price'] > 0)
                                    <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($plan['price'], 0, ',', ' ') }}</span>
                                    <span class="text-sm text-gray-600 dark:text-gray-400"> FCFA/{{ $plan['period'] }}</span>
                                @else
                                    <span class="text-2xl font-bold text-green-600 dark:text-green-400">Gratuit</span>
                                @endif
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Commission: {{ $plan['commission'] }}</p>
                        </div>
                        
                        <ul class="mt-4 space-y-2">
                            @foreach(array_slice($plan['features'], 0, 3) as $feature)
                                <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $feature }}
                                </li>
                            @endforeach
                        </ul>
                        
                        @if(!isset($plan['current']))
                            <div class="mt-4">
                                <a href="{{ route('subscription.choose') }}" 
                                   class="w-full inline-flex items-center justify-center rounded-lg {{ $plan['popular'] ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-900 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white' }} px-4 py-2 text-sm font-medium shadow-sm transition-colors">
                                    Choisir ce plan
                                </a>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</x-filament-panels::page>
