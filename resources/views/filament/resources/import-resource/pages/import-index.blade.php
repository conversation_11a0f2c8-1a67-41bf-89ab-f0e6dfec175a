
<x-filament::page>
    <style>
        .uppercase {
            text-transform: uppercase;
        }
        .tracking-wider {
            letter-spacing: 0.1em;
        }
        tbody tr td{
            color: rgb(41, 41, 41);
        }
        .dark tbody tr td{
            color: rgb(209, 213, 219);
        }

        /* Styles pour le modal d'importation */
        .import-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .import-modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 90%;
            width: 400px;
        }

        .dark .import-modal-content {
            background-color: #1f2937;
            color: white;
        }

        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin: 1rem auto;
        }

        .dark .loader {
            border: 5px solid #374151;
            border-top: 5px solid #60a5fa;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <!-- Modal d'importation -->
    @if($isImporting)
    <div class="import-modal">
        <div class="import-modal-content">
            <h3 class="text-lg font-medium mb-4">{{ $importMessage }}</h3>
            <div class="loader"></div>
            <p class="text-sm mt-4">Veuillez patienter pendant que nous traitons votre fichier...</p>
        </div>
    </div>
    @endif
    {{ $this->form }}

    @if($show_preview)
        <div class="mt-6">
            <x-filament::section>
                <x-slot name="heading">Aperçu des catégories à importer</x-slot>
                <x-slot name="description">Vérifiez les catégories avant de confirmer l'importation</x-slot>

                <div class="mb-4">
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                        <span class="font-semibold">Total des catégories :</span> {{ $preview_total }}
                    </p>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full border dark:border-gray-700 bg-white dark:bg-gray-800">
                        <thead>
                            <tr>
                                <th class="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 text-left text-xs font-semibold  uppercase tracking-wider">
                                    Ligne
                                </th>
                                <th class="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 text-left text-xs font-semibold  uppercase tracking-wider">
                                    Nom (FR)
                                </th>
                                <th class="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 text-left text-xs font-semibold  uppercase tracking-wider">
                                    Nom (EN)
                                </th>
                                <th class="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 text-left text-xs font-semibold  uppercase tracking-wider">
                                    Catégorie parente
                                </th>
                                <th class="px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 text-left text-xs font-semibold  uppercase tracking-wider">
                                    Statut
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($preview_categories as $category)
                            <tr class="{{ $category['exists'] ? 'bg-yellow-50 dark:bg-yellow-900/20' : 'bg-white dark:bg-gray-800' }}">
                                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm  ">
                                    {{ $category['row_index'] }}
                                </td>
                                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm ">
                                    {{ $category['nom_fr'] }}
                                </td>
                                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm ">
                                    {{ $category['nom_en'] }}
                                </td>
                                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm ">
                                    {{ $category['categorie_parent'] }}
                                </td>
                                <td class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm">
                                    @if($category['exists'])
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-300" style=" color: yellow;">
                                            Existe déjà (similaire à "{{ $category['similar_to'] }}")
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-300" style="color: green;">
                                            Nouvelle catégorie
                                        </span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="flex justify-end mt-4">
                    <button type="button" wire:click="cancelImportCategories" class="inline-flex items-center px-4 py-2 bg-gray-500 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-600 focus:bg-gray-600 active:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150 mr-2">
                        Annuler
                    </button>
                    <button type="button" wire:click="confirmImportCategories" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        Confirmer l'importation
                    </button>
                </div>
            </x-filament::section>
        </div>
    @else
        <div class="flex flex-col gap-6 mt-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Modèles de fichiers -->
                <x-filament::section>
                    <x-slot name="heading">Modèles de fichiers</x-slot>
                    <x-slot name="description">Téléchargez des modèles de fichiers pour l'importation</x-slot>

                    <div class="flex flex-col gap-4">
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Modèle pour les catégories</h3>
                            <a href="{{ route('filament.admin.resources.importations.download-categories-template') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Télécharger le modèle CSV
                            </a>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Modèle pour les produits</h3>
                            <a href="{{ route('filament.admin.resources.importations.download-products-template') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Télécharger le modèle CSV
                            </a>
                        </div>
                    </div>
                </x-filament::section>

                <!-- Actions d'importation -->
                <x-filament::section>
                    <x-slot name="heading">Actions d'importation</x-slot>
                    <x-slot name="description">Lancez l'importation des données</x-slot>

                    <div class="flex flex-col gap-4">
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Importer les catégories</h3>
                            <button type="button" wire:click="importCategories" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Lancer l'importation
                            </button>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Importer les produits</h3>
                            <button type="button" wire:click="importProducts" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Lancer l'importation
                            </button>
                        </div>
                    </div>
                </x-filament::section>
            </div>

            <!-- Instructions -->
            <x-filament::section>
                <x-slot name="heading">Instructions</x-slot>
                <x-slot name="description">Comment utiliser l'importation de données</x-slot>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 mb-2" >Importation de catégories</h3>
                        <ul class="list-disc ml-5 text-sm text-gray-600" >
                            <li>Le fichier doit contenir les colonnes suivantes : nom_fr, nom_en, description_fr, description_en, categorie_parent, image_url</li>
                            <li>La colonne nom_fr est obligatoire, nom_en est recommandé pour la traduction</li>
                            <li>Les noms et descriptions sont automatiquement gérés comme des champs traduisibles</li>
                            <li>Pour créer une sous-catégorie, indiquez le nom français de la catégorie parente dans la colonne categorie_parent</li>
                            <li>L'image_url doit être un chemin relatif (ex: categories/nom.jpg) ou une URL complète</li>
                            <li>Les catégories existantes sont mises à jour si le nom français correspond</li>
                            <li>Les images doivent être téléchargées séparément dans le dossier public correspondant</li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Importation de produits</h3>
                        <ul class="list-disc ml-5 text-sm text-gray-600" >
                            <li>Le fichier doit contenir les colonnes suivantes : nom_fr, nom_en, description_fr, description_en, categorie, prix, currency, stock, prix_remise, date_debut_remise, date_fin_remise, poids, longueur, largeur, hauteur, product_code, marque</li>
                            <li>Les colonnes nom_fr, categorie et prix sont obligatoires</li>
                            <li>Les noms et descriptions sont automatiquement gérés comme des champs traduisibles</li>
                            <li>La catégorie peut être spécifiée par son nom français ou anglais</li>
                            <li>La colonne currency permet de spécifier la devise (FCFA, EUR, USD, GBP, XAF, XOF). Si non spécifiée, FCFA sera utilisée par défaut</li>
                            <li>Les codes de devise sont automatiquement convertis en majuscules lors de l'importation</li>
                            <li>Les dates doivent être au format YYYY-MM-DD</li>
                            <li>Les produits existants sont mis à jour si le nom français correspond</li>
                            <li>Les prix peuvent utiliser le point ou la virgule comme séparateur décimal</li>
                            <li>Les images doivent être téléchargées séparément dans le dossier public correspondant</li>
                        </ul>
                    </div>
                </div>
            </x-filament::section>
        </div>
    @endif
</x-filament::page>
