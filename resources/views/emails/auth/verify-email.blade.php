@extends('emails.layouts.master')

@section('title', 'Vérifiez votre adresse email')

@section('content')
<div class="hero-section">
    <div style="font-size: 48px; margin-bottom: 16px;">📧</div>
    <h1 class="hero-title">Bienvenue sur Lorrelei !</h1>
    <p class="hero-subtitle">
        Bonjour {{ $user_name }}, merci de vous être inscrit(e) sur notre marketplace.
    </p>
</div>

<!-- Message principal -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">🔐 Vérification de votre email</h3>
    </div>
    
    <p style="margin-bottom: 20px;">
        Pour finaliser votre inscription et accéder à votre compte, nous devons vérifier votre adresse email.
    </p>
    
    <p style="margin-bottom: 24px;">
        Cliquez simplement sur le bouton ci-dessous pour confirmer votre adresse email :
    </p>
</div>

<!-- Bouton de vérification -->
@include('emails.components.button', [
    'url' => $verification_url,
    'text' => '✅ Vérifier mon email',
    'type' => 'primary'
])

<!-- Instructions alternatives -->
<div class="card">
    <div class="card-header">
        <h4 class="card-title">Le bouton ne fonctionne pas ?</h4>
    </div>
    
    <p style="margin-bottom: 16px;">
        Si vous ne pouvez pas cliquer sur le bouton, copiez et collez ce lien dans votre navigateur :
    </p>
    
    <div style="background-color: var(--gray-50); padding: 12px; border-radius: 6px; border: 1px solid var(--gray-200); word-break: break-all; font-family: monospace; font-size: 12px; color: var(--gray-700);">
        {{ $verification_url }}
    </div>
</div>

<!-- Informations importantes -->
<div class="card">
    <div class="card-header">
        <h4 class="card-title">🛡️ Sécurité de votre compte</h4>
    </div>
    
    <div style="padding: 0;">
        <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-200);">
            <div style="width: 24px; height: 24px; background-color: var(--success-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                <span style="color: white; font-size: 12px;">✓</span>
            </div>
            <span>Votre email sera protégé et ne sera jamais partagé</span>
        </div>
        
        <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-200);">
            <div style="width: 24px; height: 24px; background-color: var(--success-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                <span style="color: white; font-size: 12px;">✓</span>
            </div>
            <span>Vous recevrez uniquement les notifications importantes</span>
        </div>
        
        <div style="display: flex; align-items: center; padding: 12px 0;">
            <div style="width: 24px; height: 24px; background-color: var(--success-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                <span style="color: white; font-size: 12px;">✓</span>
            </div>
            <span>Vous pouvez vous désabonner à tout moment</span>
        </div>
    </div>
</div>

<!-- Prochaines étapes -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex items-start">
        <div style="width: 40px; height: 40px; background-color: var(--primary-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
            <span style="color: white; font-weight: bold;">🚀</span>
        </div>
        <div>
            <h4 style="color: var(--primary-color); margin-bottom: 12px;">Après la vérification</h4>
            <ul style="color: var(--gray-700); margin: 0; padding-left: 0; list-style: none;">
                <li style="margin-bottom: 8px;">• Accédez à votre dashboard personnel</li>
                <li style="margin-bottom: 8px;">• Parcourez notre catalogue de produits</li>
                <li style="margin-bottom: 8px;">• Passez vos premières commandes</li>
                <li style="margin-bottom: 0;">• Profitez d'offres exclusives</li>
            </ul>
        </div>
    </div>
</div>

<!-- Aide -->
<div class="text-center" style="margin-top: 32px;">
    <p style="color: var(--gray-600); margin-bottom: 16px;">
        <strong>Ce lien de vérification expirera dans 60 minutes.</strong>
    </p>
    
    <p style="color: var(--gray-600); margin-bottom: 0;">
        Si vous n'avez pas créé de compte sur Lorrelei, vous pouvez ignorer cet email.
    </p>
</div>

<!-- Contact support -->
<div class="text-center" style="margin-top: 24px;">
    <p style="color: var(--gray-500); font-size: 14px;">
        Besoin d'aide ? {' '}
        <a href="{{ config('app.url') }}/contact" style="color: var(--primary-color); text-decoration: none;">
            Contactez notre support
        </a>
    </p>
</div>
@endsection
