{{--
Composant bouton pour emails
Usage: @include('emails.components.button', [
    'url' => 'https://example.com',
    'text' => 'Voir ma commande',
    'type' => 'primary' // primary, secondary, outline
])
--}}

@php
    $type = $type ?? 'primary';
    $class = 'btn btn-' . $type;
@endphp

<div class="text-center" style="margin: 24px 0;">
    <a href="{{ $url }}" class="{{ $class }}" style="display: inline-block; padding: 14px 28px; font-size: 16px; font-weight: 600; text-decoration: none; border-radius: 8px; text-align: center; 
    @if($type === 'primary')
        background-color: #3B82F6; color: #FFFFFF;
    @elseif($type === 'secondary')
        background-color: #10B981; color: #FFFFFF;
    @elseif($type === 'outline')
        background-color: transparent; color: #3B82F6; border: 2px solid #3B82F6;
    @endif
    ">
        {{ $text }}
    </a>
</div>
