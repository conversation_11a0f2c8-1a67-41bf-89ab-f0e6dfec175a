@extends('emails.layouts.master')

@section('title', 'Nouvelle commande #' . $sous_commande_numero)

@section('content')
<div class="hero-section">
    <div style="font-size: 48px; margin-bottom: 16px;">🛍️</div>
    <h1 class="hero-title">Nouvelle commande reçue !</h1>
    <p class="hero-subtitle">
        Bonjour {{ $marchand_nom }}, vous avez reçu une nouvelle commande à traiter.
    </p>
</div>

<!-- Informations de la commande -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">📋 Détails de la commande</h3>
    </div>
    
    <table class="table">
        <tr>
            <td><strong>Numéro de sous-commande</strong></td>
            <td><span class="badge badge-info">#{{ $sous_commande_numero }}</span></td>
        </tr>
        <tr>
            <td><strong>Commande principale</strong></td>
            <td>#{{ $commande_principale_numero }}</td>
        </tr>
        <tr>
            <td><strong>Date de commande</strong></td>
            <td>{{ $date_commande }}</td>
        </tr>
        <tr>
            <td><strong>Client</strong></td>
            <td>{{ $client_nom }}</td>
        </tr>
        <tr>
            <td><strong>Nombre d'articles</strong></td>
            <td>{{ $nombre_articles }}</td>
        </tr>
        <tr>
            <td><strong>Montant de votre part</strong></td>
            <td><strong style="color: var(--success-color); font-size: 18px;">{{ $montant_sous_commande }}€</strong></td>
        </tr>
    </table>
</div>

<!-- Statut et urgence -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">⚡ Action requise</h3>
    </div>
    
    <div style="display: flex; align-items: center; padding: 16px; background-color: #FEF3C7; border-radius: 8px; border-left: 4px solid var(--warning-color);">
        <div style="width: 40px; height: 40px; background-color: var(--warning-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px;">
            <span style="color: white; font-weight: bold;">⚠️</span>
        </div>
        <div>
            <div style="font-weight: 600; color: var(--gray-900);">Commande en attente de traitement</div>
            <div style="color: var(--gray-600); font-size: 14px;">Veuillez confirmer la réception et préparer les articles</div>
        </div>
    </div>
</div>

<!-- Étapes à suivre -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">📝 Étapes à suivre</h3>
    </div>
    
    <div style="padding: 0;">
        <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-200);">
            <div style="width: 24px; height: 24px; background-color: var(--warning-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                <span style="color: white; font-size: 12px;">1</span>
            </div>
            <span><strong>Confirmer la réception</strong> de la commande dans votre dashboard</span>
        </div>
        
        <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-200);">
            <div style="width: 24px; height: 24px; background-color: var(--gray-300); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                <span style="color: white; font-size: 12px;">2</span>
            </div>
            <span><strong>Préparer les articles</strong> commandés</span>
        </div>
        
        <div style="display: flex; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--gray-200);">
            <div style="width: 24px; height: 24px; background-color: var(--gray-300); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                <span style="color: white; font-size: 12px;">3</span>
            </div>
            <span><strong>Emballer et expédier</strong> le colis</span>
        </div>
        
        <div style="display: flex; align-items: center; padding: 12px 0;">
            <div style="width: 24px; height: 24px; background-color: var(--gray-300); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                <span style="color: white; font-size: 12px;">4</span>
            </div>
            <span><strong>Mettre à jour le statut</strong> avec les informations de suivi</span>
        </div>
    </div>
</div>

<!-- Actions -->
@include('emails.components.button', [
    'url' => route('dashboard.order-details', $commande_principale_id ?? '#'),
    'text' => '🎯 Traiter la commande',
    'type' => 'primary'
])

<div class="text-center" style="margin-top: 16px;">
    <a href="{{ config('app.url') }}/merchant/dashboard" style="color: var(--gray-600); text-decoration: none; font-size: 14px;">
        📊 Mon dashboard
    </a>
    <span style="color: var(--gray-400); margin: 0 8px;">|</span>
    <a href="{{ config('app.url') }}/merchant/orders" style="color: var(--gray-600); text-decoration: none; font-size: 14px;">
        📦 Toutes mes commandes
    </a>
</div>

<!-- Rappel important -->
<div style="background-color: #DBEAFE; padding: 24px; border-radius: 12px; margin-top: 32px; border-left: 4px solid var(--primary-color);">
    <h4 style="color: var(--primary-color); margin-bottom: 12px;">💡 Rappel important</h4>
    <p style="color: var(--gray-700); margin-bottom: 8px;">
        • <strong>Délai de traitement :</strong> Veuillez traiter cette commande dans les 24h
    </p>
    <p style="color: var(--gray-700); margin-bottom: 8px;">
        • <strong>Communication :</strong> Le client sera automatiquement notifié de chaque étape
    </p>
    <p style="color: var(--gray-700); margin-bottom: 0;">
        • <strong>Support :</strong> En cas de problème, contactez notre équipe support
    </p>
</div>
@endsection
