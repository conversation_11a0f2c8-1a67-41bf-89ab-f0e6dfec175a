// Components
import { Head, useForm } from '@inertiajs/react';
import { Store, LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { useTranslation } from '@/hooks/use-translation';

interface Props {
    status?: string;
}

export default function ForgotPassword({ status }: Props) {
    const { translate } = useTranslation();
    const { data, setData, post, processing, errors } = useForm({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('password.email'));
    };

    return (
        <AuthLayout
            title={
                <div className="flex items-center justify-center gap-2 text-primary">
                    <Store className="h-6 w-6" />
                    <span><PERSON><PERSON><PERSON>and</span>
                </div>
            }
            description={String(translate('auth.forgot_password.description'))}
        >
            <Head title={String(translate('auth.forgot_password.title'))} />

            <div className="mb-4 text-sm text-muted-foreground">
                {translate('auth.forgot_password.message')}
            </div>

            {status && (
                <div className="mb-4 text-sm font-medium text-green-600">
                    {translate('auth.forgot_password.success')}
                </div>
            )}

            <form onSubmit={submit} className="space-y-4">
                <div className="grid gap-2">
                    <Label htmlFor="email">{translate('auth.forgot_password.email')}</Label>
                    <Input
                        id="email"
                        type="email"
                        name="email"
                        value={data.email}
                        className="mt-1 block w-full"
                        onChange={(e) => setData('email', e.target.value)}
                        required
                        autoFocus
                        placeholder={String(translate('auth.forgot_password.email_placeholder'))}
                    />
                    <InputError message={errors.email} />
                </div>

                <div className="flex items-center justify-between">
                    <Button type="submit" disabled={processing}>
                        {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                        {translate('auth.forgot_password.submit')}
                    </Button>

                    <TextLink href={route('login')} className="text-sm">
                        {translate('auth.forgot_password.back_to_login')}
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
}
