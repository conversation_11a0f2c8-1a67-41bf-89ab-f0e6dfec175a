import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Store, LoaderCircle } from 'lucide-react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';
import { useTranslation } from '@/hooks/use-translation';

interface Props {
    token: string;
    email: string;
}

export default function ResetPassword({ token, email }: Props) {
    const { translate } = useTranslation();
    const { data, setData, post, processing, errors } = useForm({
        token,
        email,
        password: '',
        password_confirmation: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('password.store'));
    };

    return (
        <AuthLayout
            title={
                <div className="flex items-center justify-center gap-2 text-primary">
                    <Store className="h-6 w-6" />
                    <span>Lorel<PERSON> Marchand</span>
                </div>
            }
            description={String(translate('auth.reset_password.description'))}
        >
            <Head title={String(translate('auth.reset_password.title'))} />

            <form onSubmit={submit} className="space-y-4">
                <div className="grid gap-2">
                    <Label htmlFor="email">{translate('auth.reset_password.email')}</Label>
                    <Input
                        id="email"
                        type="email"
                        value={data.email}
                        onChange={(e) => setData('email', e.target.value)}
                        className="mt-1 block w-full"
                        autoComplete="username"
                        placeholder={String(translate('auth.reset_password.email_placeholder'))}
                    />
                    <InputError message={errors.email} />
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="password">{translate('auth.reset_password.password')}</Label>
                    <Input
                        id="password"
                        type="password"
                        value={data.password}
                        onChange={(e) => setData('password', e.target.value)}
                        className="mt-1 block w-full"
                        autoComplete="new-password"
                        placeholder={String(translate('auth.reset_password.password_placeholder'))}
                    />
                    <InputError message={errors.password} />
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="password_confirmation">
                        {translate('auth.reset_password.confirm_password')}
                    </Label>
                    <Input
                        id="password_confirmation"
                        type="password"
                        value={data.password_confirmation}
                        onChange={(e) => setData('password_confirmation', e.target.value)}
                        className="mt-1 block w-full"
                        autoComplete="new-password"
                        placeholder={String(translate('auth.reset_password.confirm_password_placeholder'))}
                    />
                    <InputError message={errors.password_confirmation} />
                </div>

                <Button type="submit" className="w-full" disabled={processing}>
                    {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                    {translate('auth.reset_password.submit')}
                </Button>
            </form>
        </AuthLayout>
    );
}
