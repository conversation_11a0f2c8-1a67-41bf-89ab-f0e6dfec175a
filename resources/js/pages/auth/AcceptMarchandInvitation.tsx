import React, { useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Store, Eye, EyeOff } from 'lucide-react';
import { initializeTheme } from '@/hooks/use-appearance';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    marchand: {
        id: number;
        name: string;
        description: string;
    };
    marchandUser: {
        role: string;
        access_level: string;
        permissions: Record<string, Array<{ value: string; label: string }>>;
    };
    token: string;
    acceptUrl: string;
}

export default function AcceptMarchandInvitation({ user, marchand, marchandUser, token, acceptUrl }: Props) {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        password: '',
        password_confirmation: '',
    });

    // Initialiser le thème au chargement de la page
    useEffect(() => {
        initializeTheme();
    }, []);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(acceptUrl);
    };

    const getAccessLevelLabel = (level: string) => {
        const labels: Record<string, string> = {
            owner: 'Propriétaire',
            manager: 'Gestionnaire',
            employee: 'Employé',
            read: 'Lecture seule',
            write: 'Lecture/Écriture',
        };
        return labels[level] || level;
    };

    return (
        <>
            <Head title="Rejoindre l'équipe" />
            
            <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="text-center">
                        <Store className="mx-auto h-12 w-12 text-green-600" />
                        <h2 className="mt-6 text-3xl font-extrabold text-foreground">
                            Bienvenue dans l'équipe !
                        </h2>
                        <p className="mt-2 text-sm text-muted-foreground">
                            Configurez votre accès au dashboard marchand
                        </p>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Informations de la boutique</CardTitle>
                            <CardDescription>
                                Vous rejoignez l'équipe de cette boutique
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">Boutique</Label>
                                <p className="text-sm text-foreground font-semibold">{marchand.name}</p>
                            </div>
                            {marchand.description && (
                                <div>
                                    <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                                    <p className="text-sm text-muted-foreground">{marchand.description}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Votre rôle dans l'équipe</CardTitle>
                            <CardDescription>
                                Détails de votre accès et permissions
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">Nom</Label>
                                <p className="text-sm text-foreground">{user.name}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                                <p className="text-sm text-foreground">{user.email}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">Rôle</Label>
                                <p className="text-sm text-foreground">{marchandUser.role}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">Niveau d'accès</Label>
                                <p className="text-sm text-foreground">{getAccessLevelLabel(marchandUser.access_level)}</p>
                            </div>
                            
                            {Object.keys(marchandUser.permissions).length > 0 && (
                                <div>
                                    <Label className="text-sm font-medium text-muted-foreground mb-2 block">
                                        Vos permissions
                                    </Label>
                                    <div className="space-y-3">
                                        {Object.entries(marchandUser.permissions).map(([category, permissions]) => (
                                            <div key={category}>
                                                <p className="text-xs font-medium text-muted-foreground mb-1">{category}</p>
                                                <div className="flex flex-wrap gap-1">
                                                    {permissions.slice(0, 3).map((permission) => (
                                                        <Badge key={permission.value} variant="secondary" className="text-xs">
                                                            {permission.label}
                                                        </Badge>
                                                    ))}
                                                    {permissions.length > 3 && (
                                                        <Badge variant="outline" className="text-xs">
                                                            +{permissions.length - 3} autres
                                                        </Badge>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Créer votre mot de passe</CardTitle>
                            <CardDescription>
                                Choisissez un mot de passe sécurisé pour votre compte
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div>
                                    <Label htmlFor="password">Mot de passe</Label>
                                    <div className="relative">
                                        <Input
                                            id="password"
                                            type={showPassword ? 'text' : 'password'}
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            className={errors.password ? 'border-red-500' : ''}
                                            placeholder="Entrez votre mot de passe"
                                            required
                                        />
                                        <button
                                            type="button"
                                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                            onClick={() => setShowPassword(!showPassword)}
                                        >
                                            {showPassword ? (
                                                <EyeOff className="h-4 w-4 text-muted-foreground" />
                                            ) : (
                                                <Eye className="h-4 w-4 text-muted-foreground" />
                                            )}
                                        </button>
                                    </div>
                                    {errors.password && (
                                        <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="password_confirmation">Confirmer le mot de passe</Label>
                                    <div className="relative">
                                        <Input
                                            id="password_confirmation"
                                            type={showPasswordConfirmation ? 'text' : 'password'}
                                            value={data.password_confirmation}
                                            onChange={(e) => setData('password_confirmation', e.target.value)}
                                            className={errors.password_confirmation ? 'border-red-500' : ''}
                                            placeholder="Confirmez votre mot de passe"
                                            required
                                        />
                                        <button
                                            type="button"
                                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                            onClick={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                                        >
                                            {showPasswordConfirmation ? (
                                                <EyeOff className="h-4 w-4 text-muted-foreground" />
                                            ) : (
                                                <Eye className="h-4 w-4 text-muted-foreground" />
                                            )}
                                        </button>
                                    </div>
                                    {errors.password_confirmation && (
                                        <p className="mt-1 text-sm text-red-600">{errors.password_confirmation}</p>
                                    )}
                                </div>

                                {errors.token && (
                                    <Alert variant="destructive">
                                        <AlertDescription>{errors.token}</AlertDescription>
                                    </Alert>
                                )}

                                <Button
                                    type="submit"
                                    className="w-full"
                                    disabled={processing}
                                >
                                    {processing ? 'Configuration...' : 'Rejoindre l\'équipe'}
                                </Button>
                            </form>
                        </CardContent>
                    </Card>

                    <div className="text-center">
                        <p className="text-xs text-muted-foreground">
                            En rejoignant cette équipe, vous acceptez les conditions d'utilisation
                            et la politique de confidentialité de Lorelei.
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
