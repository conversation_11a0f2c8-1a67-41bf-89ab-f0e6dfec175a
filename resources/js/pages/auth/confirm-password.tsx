// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import EcommerceAuthLayout from '@/layouts/auth/ecommerce-auth-layout';
import { useTranslation } from '@/hooks/use-translation';

export default function ConfirmPassword() {
    const { translate } = useTranslation();
    const { data, setData, post, processing, errors, reset } = useForm<Required<{ password: string }>>({
        password: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('password.confirm'), {
            onFinish: () => reset('password'),
        });
    };

    return (
        <EcommerceAuthLayout
            title={translate('pages.confirm_password.form_title')}
            description={translate('pages.confirm_password.description')}
        >
            <Head title={translate('pages.confirm_password.title')} />

            <form onSubmit={submit}>
                <div className="space-y-6">
                    <div className="grid gap-2">
                        <Label htmlFor="password">{translate('pages.confirm_password.password')}</Label>
                        <Input
                            id="password"
                            type="password"
                            name="password"
                            placeholder={translate('pages.confirm_password.password_placeholder')}
                            autoComplete="current-password"
                            value={data.password}
                            autoFocus
                            onChange={(e) => setData('password', e.target.value)}
                        />

                        <InputError message={errors.password} />
                    </div>

                    <div className="flex items-center">
                        <Button className="w-full" disabled={processing}>
                            {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                            {processing ? translate('pages.confirm_password.confirming') : translate('pages.confirm_password.confirm')}
                        </Button>
                    </div>
                </div>
            </form>
        </EcommerceAuthLayout>
    );
}
