import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Building, MapPin, Phone, Mail, Globe, DollarSign } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    marchand?: {
        nomEntreprise?: string;
        pays_business?: string;
        ville_business?: string;
        type_business?: string;
        description_business?: string;
        telephone_principal?: string;
        email_business?: string;
        site_web?: string;
        chiffre_affaires_estime?: number;
        nombre_employes?: number;
        categories_produits?: string[];
    };
    countries?: Record<string, string>;
}

// Liste de pays par défaut si aucune n'est fournie
const defaultCountries: Record<string, string> = {
    'CM': 'Cameroun',
    'FR': 'France',
    'BE': 'Belgique',
    'CH': 'Suisse',
    'CA': 'Canada',
    'CI': 'Côte d\'Ivoire',
    'SN': 'Sénégal',
    'GA': 'Gabon',
    'CG': 'Congo',
    'BJ': 'Bénin'
};

export default function BusinessInfo({ user, marchand, countries = defaultCountries }: Props) {
    const { translate } = useTranslation();
    const { data, setData, post, processing, errors } = useForm<{
        nomEntreprise: string;
        pays_business: string;
        ville_business: string;
        type_business: string;
        description_business: string;
        telephone_principal: string;
        email_business: string;
        site_web: string;
        chiffre_affaires_estime: string | number;
        nombre_employes: string | number;
        categories_produits: string[];
        accepte_conditions: boolean | false;
    }>({
        nomEntreprise: marchand?.nomEntreprise || '',
        pays_business: marchand?.pays_business || '',
        ville_business: marchand?.ville_business || '',
        type_business: marchand?.type_business || '',
        description_business: marchand?.description_business || '',
        telephone_principal: marchand?.telephone_principal || '',
        email_business: marchand?.email_business || '',
        site_web: marchand?.site_web || '',
        chiffre_affaires_estime: marchand?.chiffre_affaires_estime || '',
        nombre_employes: marchand?.nombre_employes || '',
        categories_produits: marchand?.categories_produits || [],
        accepte_conditions: false,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        // Préparer les données pour la soumission
        const formData = {
            ...data,
            chiffre_affaires_estime: data.chiffre_affaires_estime ? Number(data.chiffre_affaires_estime) : null,
            nombre_employes: data.nombre_employes ? Number(data.nombre_employes) : null,
            accepte_conditions: Boolean(data.accepte_conditions),
        };

        // Mettre à jour les données du formulaire avant la soumission
        Object.keys(formData).forEach(key => {
            setData(key as any, formData[key as keyof typeof formData]);
        });

        post(route('seller.business-info.store'), {
            onError: (errors) => {
                console.error('Erreurs de soumission:', errors);
                // Scroll vers le premier champ avec erreur avec un délai pour l'animation
                setTimeout(() => {
                    const firstErrorField = Object.keys(errors)[0];
                    if (firstErrorField) {
                        const element = document.getElementById(firstErrorField);
                        element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // Ajouter un focus pour attirer l'attention
                        element?.focus();
                    }
                }, 100);
            },
            onSuccess: () => {
                console.log('Formulaire soumis avec succès');
            },
        });
    };

    const businessTypes = [
        { value: 'individuel', label: 'Entrepreneur individuel' },
        { value: 'entreprise', label: 'Entreprise' },
        { value: 'cooperative', label: 'Coopérative' },
        { value: 'grande_entreprise', label: 'Grande entreprise' },
    ];



    return (
        <>
            <Head title={String(translate('seller.business.title'))} />

            <div className="min-h-screen bg-background text-foreground">
                {/* Header avec contrôles */}
                <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="container mx-auto px-4">
                        <nav className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <span className="text-2xl font-bold text-primary">Lorelei</span>
                                <span className="text-xl font-medium text-muted-foreground">Marchand</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <LanguageSwitcher />
                                <AppearanceToggleDropdown />
                            </div>
                        </nav>
                    </div>
                </header>

                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Afficher les erreurs globales */}
                    {(errors as any).error && (
                        <Alert variant="destructive" className="mb-6 text-red-800 border-red-200 bg-red-500/20">
                            <AlertDescription className=''>{(errors as any).error}</AlertDescription>
                        </Alert>
                    )}

                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-muted-foreground">
                                {translate('seller.business.step', { current: '1', total: '3' })}
                            </span>
                            <span className="text-sm text-muted-foreground">
                                {translate('seller.business.step_name')}
                            </span>
                        </div>
                        <Progress value={33} className="h-2" />
                    </div>

                    <Card className="border-border bg-card">
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2 text-card-foreground">
                                <Building className="w-5 h-5 text-primary" />
                                <span>{translate('seller.business.form_title')}</span>
                            </CardTitle>
                            <CardDescription className="text-muted-foreground">
                                {translate('seller.business.form_description')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={submit} className="space-y-6">
                                {/* Informations de base */}
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="nomEntreprise">Nom de l'entreprise *</Label>
                                        <Input
                                            id="nomEntreprise"
                                            value={data.nomEntreprise}
                                            onChange={(e) => setData('nomEntreprise', e.target.value)}
                                            placeholder="Ex: Ma Boutique SARL"
                                            className={errors?.nomEntreprise ? 'field-error' : ''}
                                        />
                                        {errors?.nomEntreprise && (
                                            <p className="error-message">{errors.nomEntreprise}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="type_business">Type d'entreprise *</Label>
                                        <div className={errors?.type_business ? 'select-error' : ''}>
                                            <Select value={data.type_business} onValueChange={(value) => setData('type_business', value)}>
                                                <SelectTrigger className={errors?.type_business ? 'field-error' : ''}>
                                                    <SelectValue placeholder="Sélectionnez le type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {businessTypes.map((type) => (
                                                        <SelectItem key={type.value} value={type.value}>
                                                            {type.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        {(errors as any).type_business && (
                                            <p className="error-message">{errors.type_business}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Localisation */}
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="pays_business" className="flex items-center space-x-1">
                                            <MapPin className="w-4 h-4" />
                                            <span>Pays *</span>
                                        </Label>
                                        <Select value={data.pays_business} onValueChange={(value) => setData('pays_business', value)}>
                                            <SelectTrigger className={errors?.pays_business ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}>
                                                <SelectValue placeholder="Sélectionnez votre pays" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(countries).map(([code, name]) => (
                                                    <SelectItem key={code} value={code}>
                                                        {name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors?.pays_business && (
                                            <p className="text-sm text-red-500 mt-1">{errors.pays_business}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="ville_business">Ville *</Label>
                                        <Input
                                            id="ville_business"
                                            value={data.ville_business}
                                            onChange={(e) => setData('ville_business', e.target.value)}
                                            placeholder="Ex: Douala"
                                            className={errors?.ville_business ? 'field-error' : ''}
                                        />
                                        {errors?.ville_business && (
                                            <p className="error-message">{errors.ville_business}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Contact */}
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="telephone_principal" className="flex items-center space-x-1">
                                            <Phone className="w-4 h-4" />
                                            <span>Téléphone principal *</span>
                                        </Label>
                                        <Input
                                            id="telephone_principal"
                                            value={data.telephone_principal}
                                            onChange={(e) => setData('telephone_principal', e.target.value)}
                                            placeholder="Ex: +237 6XX XXX XXX"
                                            className={errors?.telephone_principal ? 'field-error' : ''}
                                        />
                                        {errors?.telephone_principal && (
                                            <p className="error-message">{errors.telephone_principal}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email_business" className="flex items-center space-x-1">
                                            <Mail className="w-4 h-4" />
                                            <span>Email professionnel</span>
                                        </Label>
                                        <Input
                                            id="email_business"
                                            type="email"
                                            value={data.email_business}
                                            onChange={(e) => setData('email_business', e.target.value)}
                                            placeholder="<EMAIL>"
                                            className={errors?.email_business ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                        />
                                        {errors?.email_business && (
                                            <p className="text-sm text-red-500 mt-1">{errors.email_business}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Description */}
                                <div className="space-y-2">
                                    <Label htmlFor="description_business">Description de votre activité</Label>
                                    <Textarea
                                        id="description_business"
                                        value={data.description_business}
                                        onChange={(e) => setData('description_business', e.target.value)}
                                        placeholder="Décrivez brièvement votre activité commerciale..."
                                        rows={4}
                                        className={errors?.description_business ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                    />
                                    {errors?.description_business && (
                                        <p className="text-sm text-red-500 mt-1">{errors.description_business}</p>
                                    )}
                                </div>

                                {/* Informations complémentaires */}
                                <div className="grid md:grid-cols-3 gap-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="site_web" className="flex items-center space-x-1">
                                            <Globe className="w-4 h-4" />
                                            <span>Site web</span>
                                        </Label>
                                        <Input
                                            id="site_web"
                                            type="url"
                                            value={data.site_web}
                                            onChange={(e) => setData('site_web', e.target.value)}
                                            placeholder="https://monsite.com"
                                            className={errors?.site_web ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                        />
                                        {errors?.site_web && (
                                            <p className="text-sm text-red-500 mt-1">{errors.site_web}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="chiffre_affaires_estime" className="flex items-center space-x-1">
                                            <DollarSign className="w-4 h-4" />
                                            <span>CA estimé (FCFA/mois)</span>
                                        </Label>
                                        <Input
                                            id="chiffre_affaires_estime"
                                            type="number"
                                            value={data.chiffre_affaires_estime}
                                            onChange={(e) => setData('chiffre_affaires_estime', e.target.value)}
                                            placeholder="1000000"
                                            className={errors?.chiffre_affaires_estime ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                        />
                                        {errors?.chiffre_affaires_estime && (
                                            <p className="text-sm text-red-500 mt-1">{errors.chiffre_affaires_estime}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="nombre_employes">Nombre d'employés</Label>
                                        <Input
                                            id="nombre_employes"
                                            type="number"
                                            value={data.nombre_employes}
                                            onChange={(e) => setData('nombre_employes', e.target.value)}
                                            placeholder="5"
                                            className={errors?.nombre_employes ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                        />
                                        {errors?.nombre_employes && (
                                            <p className="text-sm text-red-500 mt-1">{errors.nombre_employes}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Conditions */}
                                <div className="space-y-4">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="accepte_conditions"
                                            checked={data.accepte_conditions}
                                            onCheckedChange={checked => setData('accepte_conditions', !!checked)}
                                        />
                                        <Label htmlFor="accepte_conditions" className="text-sm">
                                            J'accepte les{' '}
                                            <a href="#" className="text-blue-600 hover:underline">
                                                conditions générales d'utilisation
                                            </a>{' '}
                                            et la{' '}
                                            <a href="#" className="text-blue-600 hover:underline">
                                                politique de confidentialité
                                            </a>
                                        </Label>
                                    </div>
                                    {errors?.accepte_conditions && (
                                        <p className="error-message">{errors.accepte_conditions}</p>
                                    )}
                                </div>

                                {/* Actions */}
                                <div className="flex justify-between pt-6">
                                    <Button type="button" variant="outline" asChild>
                                        <a href={route('seller.welcome')}>Retour</a>
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Enregistrement...' : 'Continuer'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
