import React, { useState, useEffect } from 'react';
import ProductListInfinite from '../../components/ecommerce/ProductListInfinite';
import { ProductService } from '../../services/ProductService';
import { Product } from '../../types/Product';

/**
 * Page de test pour vérifier que les produits similaires excluent bien le produit actuel
 */
export default function SimilarProductsTest() {
    const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const productService = new ProductService();

    useEffect(() => {
        // Charger un produit pour le test
        const loadTestProduct = async () => {
            try {
                const products = await productService.getProducts(1, 1);
                if (products.length > 0) {
                    setCurrentProduct(products[0]);
                }
            } catch (error) {
                console.error('Erreur lors du chargement du produit de test:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadTestProduct();
    }, []);

    if (isLoading) {
        return <div className="p-8">Chargement...</div>;
    }

    if (!currentProduct) {
        return <div className="p-8">Aucun produit trouvé pour le test</div>;
    }

    return (
        <div className="container mx-auto p-8">
            <h1 className="text-3xl font-bold mb-8">Test des Produits Similaires</h1>
            
            <div className="mb-8 p-4 bg-blue-50 rounded-lg">
                <h2 className="text-xl font-semibold mb-2">Produit actuel (à exclure)</h2>
                <p><strong>ID:</strong> {currentProduct.id}</p>
                <p><strong>Nom:</strong> {JSON.stringify(currentProduct.nom)}</p>
                <p><strong>Catégorie:</strong> {currentProduct.category}</p>
            </div>

            <div className="mb-4">
                <h2 className="text-xl font-semibold">Produits similaires (sans le produit actuel)</h2>
                <p className="text-gray-600 mb-4">
                    Le produit ID {currentProduct.id} ne devrait PAS apparaître dans la liste ci-dessous.
                </p>
            </div>

            <ProductListInfinite
                categoryId={currentProduct.category}
                itemsPerPage={8}
                emptyMessage="Aucun produit similaire trouvé"
                className="min-h-[200px]"
                filters={{
                    exclude_product: currentProduct.id
                }}
                showPagination={true}
            />

            <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
                <h3 className="font-semibold mb-2">Instructions de test :</h3>
                <ol className="list-decimal list-inside space-y-1">
                    <li>Vérifiez que le produit ID {currentProduct.id} n'apparaît pas dans la liste des produits similaires</li>
                    <li>Vérifiez que les autres produits de la même catégorie sont bien affichés</li>
                    <li>Testez la pagination pour vous assurer que le produit exclu n'apparaît sur aucune page</li>
                </ol>
            </div>
        </div>
    );
}
