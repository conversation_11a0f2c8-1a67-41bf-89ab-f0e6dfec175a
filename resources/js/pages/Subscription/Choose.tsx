import { Head, Link, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    CheckCircle, 
    Clock, 
    Star,
    ArrowLeft,
    Zap,
    Crown,
    Gem
} from 'lucide-react';

interface Plan {
    name: string;
    price: number;
    period: string;
    commission: string;
    description: string;
    features: string[];
    color: string;
    popular: boolean;
    buttonText: string;
    current?: boolean;
}

interface Props {
    currentSubscription: any;
    isTrialActive: boolean;
    trialDaysRemaining: number;
    plans: Record<string, Plan>;
    marchand: any;
}

export default function Choose({ 
    currentSubscription, 
    isTrialActive, 
    trialDaysRemaining, 
    plans, 
    marchand 
}: Props) {
    const { post, processing } = useForm();

    const handleSubscribe = (planKey: string) => {
        if (plans[planKey].current) return;
        
        post(route('subscription.subscribe'), {
            data: { plan: planKey },
        });
    };

    const getPlanIcon = (planKey: string) => {
        switch (planKey) {
            case 'gratuit':
                return <CheckCircle className="w-6 h-6" />;
            case 'basique':
                return <Zap className="w-6 h-6" />;
            case 'premium':
                return <Crown className="w-6 h-6" />;
            case 'elite':
                return <Gem className="w-6 h-6" />;
            default:
                return <CheckCircle className="w-6 h-6" />;
        }
    };

    const getPlanColorClasses = (color: string, isPopular: boolean = false) => {
        const baseClasses = "relative rounded-xl border p-6 transition-all duration-200 hover:shadow-lg";
        
        if (isPopular) {
            return `${baseClasses} border-blue-500 bg-blue-50 dark:bg-blue-950/20 ring-2 ring-blue-500`;
        }
        
        switch (color) {
            case 'blue':
                return `${baseClasses} border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700`;
            case 'purple':
                return `${baseClasses} border-purple-200 dark:border-purple-800 hover:border-purple-300 dark:hover:border-purple-700`;
            case 'gold':
                return `${baseClasses} border-yellow-200 dark:border-yellow-800 hover:border-yellow-300 dark:hover:border-yellow-700`;
            default:
                return `${baseClasses} border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600`;
        }
    };

    const getButtonClasses = (planKey: string, plan: Plan) => {
        if (plan.current) {
            return "w-full bg-gray-400 text-white cursor-not-allowed";
        }
        
        if (plan.popular) {
            return "w-full bg-blue-600 hover:bg-blue-700 text-white";
        }
        
        switch (plan.color) {
            case 'purple':
                return "w-full bg-purple-600 hover:bg-purple-700 text-white";
            case 'gold':
                return "w-full bg-yellow-600 hover:bg-yellow-700 text-white";
            default:
                return "w-full bg-gray-600 hover:bg-gray-700 text-white";
        }
    };

    return (
        <>
            <Head title="Choisir votre abonnement - Lorelei Marchand" />
            
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-950 py-12">
                <div className="container mx-auto px-4 max-w-7xl">
                    {/* Header */}
                    <div className="text-center mb-12">
                        <Link href={route('marchand.dashboard')} 
                              className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mb-6">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Retour au dashboard
                        </Link>
                        
                        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Choisissez votre plan d'abonnement
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Sélectionnez le plan qui correspond le mieux à vos besoins et développez votre activité avec Lorelei
                        </p>
                    </div>

                    {/* Alerte Trial */}
                    {isTrialActive && (
                        <Alert className="mb-8 border-blue-200 bg-blue-50 dark:bg-blue-950/20">
                            <Clock className="h-4 w-4 text-blue-600" />
                            <AlertDescription className="text-blue-800 dark:text-blue-200">
                                <strong>Période d'essai active :</strong> Il vous reste {trialDaysRemaining} jour{trialDaysRemaining > 1 ? 's' : ''} 
                                pour profiter gratuitement des fonctionnalités du plan Basique. 
                                Choisissez votre abonnement dès maintenant pour continuer sans interruption.
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Abonnement actuel */}
                    {currentSubscription && (
                        <Card className="mb-8">
                            <CardHeader>
                                <CardTitle>Votre abonnement actuel</CardTitle>
                                <CardDescription>
                                    Vous êtes actuellement sur le plan{' '}
                                    <strong>
                                        {currentSubscription.type_abonnement === 'trial' 
                                            ? 'Basique (Essai gratuit)' 
                                            : plans[currentSubscription.type_abonnement]?.name || currentSubscription.type_abonnement
                                        }
                                    </strong>
                                </CardDescription>
                            </CardHeader>
                        </Card>
                    )}

                    {/* Plans d'abonnement */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {Object.entries(plans).map(([planKey, plan]) => (
                            <div key={planKey} className={getPlanColorClasses(plan.color, plan.popular)}>
                                {/* Badge populaire */}
                                {plan.popular && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <Badge className="bg-blue-600 text-white px-3 py-1">
                                            <Star className="w-3 h-3 mr-1" />
                                            Populaire
                                        </Badge>
                                    </div>
                                )}

                                {/* Badge plan actuel */}
                                {plan.current && (
                                    <div className="absolute -top-3 right-4">
                                        <Badge className="bg-green-600 text-white px-3 py-1">
                                            Plan actuel
                                        </Badge>
                                    </div>
                                )}

                                <div className="text-center">
                                    {/* Icône du plan */}
                                    <div className={`mx-auto w-12 h-12 rounded-full flex items-center justify-center mb-4 ${
                                        plan.color === 'blue' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400' :
                                        plan.color === 'purple' ? 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400' :
                                        plan.color === 'gold' ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400' :
                                        'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                                    }`}>
                                        {getPlanIcon(planKey)}
                                    </div>

                                    {/* Nom du plan */}
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                        {plan.name}
                                    </h3>

                                    {/* Prix */}
                                    <div className="mb-2">
                                        {plan.price > 0 ? (
                                            <>
                                                <span className="text-3xl font-bold text-gray-900 dark:text-white">
                                                    {new Intl.NumberFormat('fr-FR').format(plan.price)}
                                                </span>
                                                <span className="text-gray-600 dark:text-gray-400 ml-1">
                                                    FCFA/{plan.period}
                                                </span>
                                            </>
                                        ) : (
                                            <span className="text-3xl font-bold text-green-600 dark:text-green-400">
                                                Gratuit
                                            </span>
                                        )}
                                    </div>

                                    {/* Commission */}
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        Commission: {plan.commission}
                                    </p>

                                    {/* Description */}
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                                        {plan.description}
                                    </p>
                                </div>

                                {/* Fonctionnalités */}
                                <ul className="space-y-3 mb-6">
                                    {plan.features.map((feature, index) => (
                                        <li key={index} className="flex items-start text-sm">
                                            <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                                            <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                                        </li>
                                    ))}
                                </ul>

                                {/* Bouton d'action */}
                                <Button
                                    onClick={() => handleSubscribe(planKey)}
                                    disabled={plan.current || processing}
                                    className={getButtonClasses(planKey, plan)}
                                >
                                    {processing ? 'Traitement...' : plan.buttonText}
                                </Button>
                            </div>
                        ))}
                    </div>

                    {/* Informations supplémentaires */}
                    <div className="mt-12 text-center">
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                            Vous pouvez changer de plan à tout moment depuis votre dashboard.
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">
                            Des questions ? Contactez notre équipe support à{' '}
                            <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
