/**
 * Formate un prix en chaîne de caractères avec la devise
 *
 * @param price - Le prix à formater
 * @param currency - La devise (par défaut: FCFA)
 * @returns Le prix formaté
 */
export function formatPrice(price: number | string | null | undefined, currency: string = 'FCFA'): string {
  if (price === null || price === undefined) {
    return `0 ${currency}`;
  }

  const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

  return `${numericPrice.toLocaleString('fr-FR', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })} ${currency}`;
}

/**
 * Formate une date en chaîne de caractères
 *
 * @param date - La date à formater
 * @param locale - La locale (par défaut: fr-FR)
 * @returns La date formatée
 */
export function formatDate(date: Date | string, locale: string = 'fr-FR'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Tronque un texte à une longueur maximale
 *
 * @param text - Le texte à tronquer
 * @param maxLength - La longueur maximale (par défaut: 100)
 * @param suffix - Le suffixe à ajouter si le texte est tronqué (par défaut: ...)
 * @returns Le texte tronqué
 */
export function truncateText(text: string, maxLength: number = 100, suffix: string = '...'): string {
  if (!text) return '';

  if (text.length <= maxLength) {
    return text;
  }

  return text.substring(0, maxLength) + suffix;
}

/**
 * Formate une date en temps relatif (il y a X minutes, heures, jours...)
 *
 * @param date - La date à formater
 * @returns Le temps relatif formaté
 */
export function formatRelativeDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'À l\'instant';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `Il y a ${diffInMonths} mois`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `Il y a ${diffInYears} an${diffInYears > 1 ? 's' : ''}`;
}
