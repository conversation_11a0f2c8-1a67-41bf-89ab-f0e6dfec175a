import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

// Configuration Pusher pour Reverb
window.Pusher = Pusher;

class ReverbWebSocketService {
    private echo: Echo | null = null;
    private channels: Map<string, any> = new Map();
    private isConnected: boolean = false;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;

    constructor() {
        this.initializeEcho();
    }

    private initializeEcho(): void {
        try {
            // Debug: Vérifier les variables d'environnement
            console.log('🔧 [DEBUG] Variables Reverb:', {
                key: import.meta.env.VITE_REVERB_APP_KEY,
                host: import.meta.env.VITE_REVERB_HOST,
                port: import.meta.env.VITE_REVERB_PORT,
                scheme: import.meta.env.VITE_REVERB_SCHEME
            });

            this.echo = new Echo({
                broadcaster: 'reverb',
                key: import.meta.env.VITE_REVERB_APP_KEY || 'local-key',
                wsHost: import.meta.env.VITE_REVERB_HOST || 'localhost',
                wsPort: import.meta.env.VITE_REVERB_PORT || 8080,
                wssPort: import.meta.env.VITE_REVERB_PORT || 8080,
                forceTLS: import.meta.env.VITE_REVERB_SCHEME === 'https',
                enabledTransports: ['ws', 'wss'],
                auth: {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'Authorization': `Bearer ${this.getAuthToken()}`,
                    },
                },
                authEndpoint: '/broadcasting/auth',
            });

            this.setupConnectionEvents();
        } catch (error) {
            console.error('Failed to initialize Echo:', error);
            this.scheduleReconnect();
        }
    }

    private setupConnectionEvents(): void {
        if (!this.echo) return;

        this.echo.connector.pusher.connection.bind('connected', () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            console.log('✅ WebSocket connected to Reverb');
            this.onConnectionChange?.(true);
        });

        this.echo.connector.pusher.connection.bind('disconnected', () => {
            this.isConnected = false;
            console.log('❌ WebSocket disconnected from Reverb');
            this.onConnectionChange?.(false);
            this.scheduleReconnect();
        });

        this.echo.connector.pusher.connection.bind('error', (error: any) => {
            console.error('WebSocket error:', error);
            this.scheduleReconnect();
        });
    }

    private scheduleReconnect(): void {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

        setTimeout(() => {
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            this.initializeEcho();
        }, delay);
    }

    private getAuthToken(): string {
        return localStorage.getItem('auth_token') || '';
    }

    /**
     * S'abonner à un canal de litige
     */
    subscribeToDispute(disputeId: string, callbacks: {
        onMessage?: (data: any) => void;
        onStatusChange?: (data: any) => void;
        onTyping?: (data: any) => void;
    }): void {
        if (!this.echo) {
            console.error('Echo not initialized');
            return;
        }

        const channelName = `dispute.${disputeId}`;

        if (this.channels.has(channelName)) {
            console.log(`Already subscribed to ${channelName}`);
            return;
        }

        try {
            const channel = this.echo.private(channelName);

            if (callbacks.onMessage) {
                channel.listen('.dispute.message.sent', (data: any) => {
                    console.log('📨 New dispute message received:', data);
                    callbacks.onMessage?.(data);
                    this.playNotificationSound();
                });
            }

            if (callbacks.onStatusChange) {
                channel.listen('.dispute.status.changed', (data: any) => {
                    console.log('🔄 Dispute status changed:', data);
                    callbacks.onStatusChange?.(data);
                });
            }

            if (callbacks.onTyping) {
                channel.listen('.user.typing', (data: any) => {
                    console.log('✍️ User typing in dispute:', data);
                    callbacks.onTyping?.(data);
                });
            }

            this.channels.set(channelName, channel);
            console.log(`✅ Subscribed to dispute: ${disputeId}`);
        } catch (error) {
            console.error(`Failed to subscribe to dispute ${disputeId}:`, error);
        }
    }

    /**
     * S'abonner aux notifications admin
     */
    subscribeToAdminNotifications(callbacks: {
        onNotification?: (data: any) => void;
    }): void {
        if (!this.echo) {
            console.error('Echo not initialized');
            return;
        }

        const channelName = 'admin.notifications';

        if (this.channels.has(channelName)) {
            return;
        }

        try {
            const channel = this.echo.private(channelName);

            if (callbacks.onNotification) {
                channel.listen('.notification', (data: any) => {
                    console.log('🔔 New admin notification:', data);
                    callbacks.onNotification?.(data);
                    this.showBrowserNotification(data);
                });
            }

            this.channels.set(channelName, channel);
            console.log(`✅ Subscribed to admin notifications`);
        } catch (error) {
            console.error(`Failed to subscribe to admin notifications:`, error);
        }
    }

    /**
     * Envoyer l'état "en train d'écrire"
     */
    async sendTyping(disputeId: string, isTyping: boolean = true): Promise<void> {
        // TODO: Activer quand le backend aura la route /api/disputes/typing
        if (process.env.NODE_ENV === 'development') {
            console.log(`[WebSocket] Typing ${isTyping ? 'started' : 'stopped'} for dispute ${disputeId}`);
            return;
        }

        try {
            const response = await fetch('/api/disputes/typing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    dispute_id: disputeId,
                    is_typing: isTyping,
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            // Silencieux en développement pour éviter le spam de logs
            if (process.env.NODE_ENV !== 'development') {
                console.error('Failed to send typing status:', error);
            }
        }
    }

    /**
     * Se désabonner d'un canal
     */
    unsubscribe(channelName: string): void {
        const channel = this.channels.get(channelName);
        if (channel && this.echo) {
            this.echo.leave(channelName);
            this.channels.delete(channelName);
            console.log(`❌ Unsubscribed from: ${channelName}`);
        }
    }

    /**
     * Se désabonner de tous les canaux
     */
    unsubscribeAll(): void {
        this.channels.forEach((channel, channelName) => {
            this.unsubscribe(channelName);
        });
    }

    /**
     * Se déconnecter complètement
     */
    disconnect(): void {
        this.unsubscribeAll();
        if (this.echo) {
            this.echo.disconnect();
            this.echo = null;
        }
        this.isConnected = false;
        console.log('🔌 WebSocket disconnected');
    }

    /**
     * Jouer un son de notification
     */
    private playNotificationSound(): void {
        try {
            const audio = new Audio('/sounds/notification.mp3');
            audio.volume = 0.5;
            audio.play().catch(() => {
                // Ignorer les erreurs de lecture audio (autoplay policy)
            });
        } catch (error) {
            // Ignorer les erreurs audio
        }
    }

    /**
     * Afficher une notification navigateur
     */
    private showBrowserNotification(data: any): void {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(data.title || 'Nouveau message', {
                body: data.message || 'Vous avez reçu un nouveau message',
                icon: '/favicon.ico',
                tag: data.id || 'notification',
            });
        }
    }

    /**
     * Demander la permission pour les notifications
     */
    async requestNotificationPermission(): Promise<boolean> {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }
        return false;
    }

    /**
     * Callback pour les changements de connexion
     */
    public onConnectionChange?: (connected: boolean) => void;

    /**
     * Getters
     */
    get connected(): boolean {
        return this.isConnected;
    }

    get activeChannels(): string[] {
        return Array.from(this.channels.keys());
    }
}

export default new ReverbWebSocketService();
