/**
 * Service pour gérer les notifications temps réel
 */

interface RealtimeMessage {
    type: 'new_message' | 'new_dispute' | 'new_dispute_message' | 'dispute_status_change';
    data: any;
    timestamp: string;
}

interface RealtimeCallbacks {
    onNewMessage?: (data: any) => void;
    onNewDispute?: (data: any) => void;
    onNewDisputeMessage?: (data: any) => void;
    onDisputeStatusChange?: (data: any) => void;
    onError?: (error: any) => void;
}

class RealtimeService {
    private callbacks: RealtimeCallbacks = {};
    private isConnected = false;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000; // 1 seconde
    private eventSource: EventSource | null = null;

    /**
     * Initialiser le service temps réel
     */
    public init(callbacks: RealtimeCallbacks): void {
        this.callbacks = callbacks;
        this.connect();
    }

    /**
     * Se connecter au flux temps réel
     */
    private connect(): void {
        try {
            // Utiliser Server-Sent Events pour une solution simple
            this.eventSource = new EventSource('/api/realtime/stream');

            this.eventSource.onopen = () => {
                console.log('✅ Connexion temps réel établie');
                this.isConnected = true;
                this.reconnectAttempts = 0;
            };

            this.eventSource.onmessage = (event) => {
                try {
                    const message: RealtimeMessage = JSON.parse(event.data);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('❌ Erreur parsing message temps réel:', error);
                }
            };

            this.eventSource.onerror = (error) => {
                console.error('❌ Erreur connexion temps réel:', error);
                this.isConnected = false;
                
                if (this.callbacks.onError) {
                    this.callbacks.onError(error);
                }

                // Tentative de reconnexion
                this.attemptReconnect();
            };

        } catch (error) {
            console.error('❌ Erreur initialisation temps réel:', error);
            if (this.callbacks.onError) {
                this.callbacks.onError(error);
            }
        }
    }

    /**
     * Gérer les messages reçus
     */
    private handleMessage(message: RealtimeMessage): void {
        console.log('📨 Message temps réel reçu:', message.type, message.data);

        switch (message.type) {
            case 'new_message':
                if (this.callbacks.onNewMessage) {
                    this.callbacks.onNewMessage(message.data);
                }
                break;

            case 'new_dispute':
                if (this.callbacks.onNewDispute) {
                    this.callbacks.onNewDispute(message.data);
                }
                break;

            case 'new_dispute_message':
                if (this.callbacks.onNewDisputeMessage) {
                    this.callbacks.onNewDisputeMessage(message.data);
                }
                break;

            case 'dispute_status_change':
                if (this.callbacks.onDisputeStatusChange) {
                    this.callbacks.onDisputeStatusChange(message.data);
                }
                break;

            default:
                console.warn('⚠️ Type de message non reconnu:', message.type);
        }
    }

    /**
     * Tentative de reconnexion
     */
    private attemptReconnect(): void {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ Nombre maximum de tentatives de reconnexion atteint');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Backoff exponentiel

        console.log(`🔄 Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts} dans ${delay}ms`);

        setTimeout(() => {
            this.disconnect();
            this.connect();
        }, delay);
    }

    /**
     * Se déconnecter
     */
    public disconnect(): void {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
        console.log('🔌 Connexion temps réel fermée');
    }

    /**
     * Vérifier l'état de la connexion
     */
    public isConnectionActive(): boolean {
        return this.isConnected;
    }

    /**
     * Envoyer une notification de test
     */
    public async sendTestNotification(): Promise<boolean> {
        try {
            const response = await fetch('/api/realtime/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    message: 'Test de notification temps réel'
                })
            });

            return response.ok;
        } catch (error) {
            console.error('❌ Erreur envoi notification test:', error);
            return false;
        }
    }

    /**
     * Obtenir les statistiques de connexion
     */
    public getStats(): object {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            maxReconnectAttempts: this.maxReconnectAttempts,
            hasEventSource: !!this.eventSource,
        };
    }
}

// Instance singleton
export const realtimeService = new RealtimeService();

// Types pour l'export
export type { RealtimeMessage, RealtimeCallbacks };
export default RealtimeService;
