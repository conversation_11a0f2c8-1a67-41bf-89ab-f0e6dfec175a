import { useState, useEffect, useCallback } from 'react';
import { StockService, CartItem, StockCheckResult, ProductStock } from '@/services/StockService';

/**
 * Hook pour la gestion des stocks
 */
export const useStock = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    /**
     * Vérifie la disponibilité des stocks pour un panier
     */
    const checkCartAvailability = useCallback(async (cart: CartItem[]): Promise<StockCheckResult | null> => {
        setLoading(true);
        setError(null);

        try {
            const result = await StockService.checkCartAvailability(cart);
            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la vérification des stocks';
            setError(errorMessage);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Valide un panier avant le checkout
     */
    const validateCart = useCallback(async (cart: CartItem[]) => {
        setLoading(true);
        setError(null);

        try {
            const validation = await StockService.validateCartBeforeCheckout(cart);
            
            if (!validation.valid) {
                setError(validation.errors.join(', '));
            }
            
            return validation;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la validation du panier';
            setError(errorMessage);
            return { valid: false, errors: [errorMessage], warnings: [] };
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Récupère les informations de stock pour un produit
     */
    const getProductStock = useCallback(async (productId: number): Promise<ProductStock | null> => {
        setLoading(true);
        setError(null);

        try {
            const result = await StockService.getProductStock(productId);
            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la récupération du stock';
            setError(errorMessage);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Vérifie si un produit est en stock
     */
    const isProductInStock = useCallback(async (productId: number, quantity: number = 1): Promise<boolean> => {
        try {
            return await StockService.isProductInStock(productId, quantity);
        } catch (err) {
            console.error('Erreur vérification stock produit:', err);
            return false;
        }
    }, []);

    return {
        loading,
        error,
        checkCartAvailability,
        validateCart,
        getProductStock,
        isProductInStock,
        clearError: () => setError(null)
    };
};

/**
 * Hook pour surveiller le stock d'un produit spécifique
 */
export const useProductStock = (productId: number | null) => {
    const [stock, setStock] = useState<ProductStock | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const refreshStock = useCallback(async () => {
        if (!productId) return;

        setLoading(true);
        setError(null);

        try {
            const result = await StockService.getProductStock(productId);
            setStock(result);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la récupération du stock';
            setError(errorMessage);
            setStock(null);
        } finally {
            setLoading(false);
        }
    }, [productId]);

    useEffect(() => {
        refreshStock();
    }, [refreshStock]);

    return {
        stock,
        loading,
        error,
        refreshStock,
        isInStock: stock?.available ?? false,
        availableQuantity: stock?.stock ?? 0
    };
};

/**
 * Hook pour la validation en temps réel du panier
 */
export const useCartValidation = (cart: CartItem[]) => {
    const [validation, setValidation] = useState<{
        valid: boolean;
        errors: string[];
        warnings: string[];
    } | null>(null);
    const [loading, setLoading] = useState(false);

    const validateCart = useCallback(async () => {
        if (!cart || cart.length === 0) {
            setValidation({ valid: true, errors: [], warnings: [] });
            return;
        }

        setLoading(true);

        try {
            const result = await StockService.validateCartBeforeCheckout(cart);
            setValidation(result);
        } catch (err) {
            console.error('Erreur validation panier:', err);
            setValidation({
                valid: false,
                errors: ['Erreur lors de la validation du panier'],
                warnings: []
            });
        } finally {
            setLoading(false);
        }
    }, [cart]);

    useEffect(() => {
        validateCart();
    }, [validateCart]);

    return {
        validation,
        loading,
        revalidate: validateCart,
        isValid: validation?.valid ?? false,
        hasErrors: validation ? validation.errors.length > 0 : false,
        hasWarnings: validation ? validation.warnings.length > 0 : false,
        errors: validation?.errors ?? [],
        warnings: validation?.warnings ?? []
    };
};

export default useStock;
