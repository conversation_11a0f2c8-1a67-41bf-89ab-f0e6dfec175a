import { useEffect, useState } from 'react';

type Appearance = 'light' | 'dark' | 'system';

export function useAppearance() {
  const [appearance, setAppearance] = useState<Appearance>(() => {
    if (typeof window !== 'undefined') {
      return (localStorage.getItem('appearance') as Appearance) || 'system';
    }
    return 'system';
  });

  const updateAppearance = (newAppearance: Appearance) => {
    setAppearance(newAppearance);
    localStorage.setItem('appearance', newAppearance);

    // Update the document class
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');

    if (newAppearance === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(newAppearance);
    }

    // Note: Pas besoin d'envoyer au serveur pour le moment
    // Le thème est géré côté client uniquement
  };

  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');

    if (appearance === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.add(systemTheme);

      // Listen for system theme changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        if (appearance === 'system') {
          root.classList.remove('light', 'dark');
          root.classList.add(mediaQuery.matches ? 'dark' : 'light');
        }
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      root.classList.add(appearance);
    }
  }, [appearance]);

  return {
    appearance,
    updateAppearance,
  };
}

export function initializeTheme() {
  if (typeof window === 'undefined') return;

  const appearance = (localStorage.getItem('appearance') as Appearance) || 'system';
  const root = window.document.documentElement;

  root.classList.remove('light', 'dark');

  if (appearance === 'system') {
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    root.classList.add(systemTheme);
  } else {
    root.classList.add(appearance);
  }
}
