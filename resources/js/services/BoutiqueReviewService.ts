import axios from 'axios';

export interface BoutiqueReview {
  id: number;
  marchand_id: number;
  user_id?: number;
  name: string;
  email?: string;
  rating: number;
  title?: string;
  comment: string;
  images?: Array<{name: string, folder: string}>;
  image_urls: string[];
  thumbnail_urls: Array<{small: string, medium: string, large: string}>;
  is_approved: boolean;
  is_verified: boolean;
  is_reported: boolean;
  likes: number;
  dislikes: number;
  marchand_response?: string;
  marchand_response_at?: string;
  report_reason?: string;
  created_at: string;
  updated_at: string;
  formatted_date: string;
}

export interface BoutiqueReviewStats {
  average_rating: number;
  total_reviews: number;
  rating_stats: {
    average: number;
    total: number;
    distribution: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
  };
}

export interface BoutiqueReviewsResponse {
  data: BoutiqueReview[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
}

export interface BoutiqueReviewFilters {
  page?: number;
  per_page?: number;
  sort_by?: 'created_at' | 'rating' | 'likes';
  sort_order?: 'asc' | 'desc';
  rating?: string;
  verified?: string;
  with_images?: string;
}

class BoutiqueReviewService {
  private baseUrl = '/api/boutiques';

  /**
   * Récupère les avis d'une boutique avec pagination et filtres
   */
  async getBoutiqueReviews(marchandId: number, filters: BoutiqueReviewFilters = {}): Promise<BoutiqueReviewsResponse> {
    try {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await axios.get(`${this.baseUrl}/${marchandId}/reviews?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des avis boutique:', error);
      throw error;
    }
  }

  /**
   * Crée un nouvel avis pour une boutique
   */
  async createBoutiqueReview(marchandId: number, reviewData: FormData): Promise<BoutiqueReview> {
    try {
      const response = await axios.post(`${this.baseUrl}/${marchandId}/reviews`, reviewData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création de l\'avis boutique:', error);
      throw error;
    }
  }

  /**
   * Vote pour un avis (like/dislike)
   */
  async voteReview(reviewId: number, voteType: 'like' | 'dislike'): Promise<void> {
    try {
      await axios.post(`/api/boutique-reviews/${reviewId}/vote`, {
        vote_type: voteType,
      });
    } catch (error) {
      console.error('Erreur lors du vote:', error);
      throw error;
    }
  }

  /**
   * Signale un avis
   */
  async reportReview(reviewId: number, reason: string): Promise<void> {
    try {
      await axios.post(`/api/boutique-reviews/${reviewId}/report`, {
        reason,
      });
    } catch (error) {
      console.error('Erreur lors du signalement:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques des avis d'une boutique
   */
  async getBoutiqueStats(marchandId: number): Promise<BoutiqueReviewStats> {
    try {
      const response = await axios.get(`${this.baseUrl}/${marchandId}/stats`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  /**
   * Met à jour un avis (pour les marchands - réponse)
   */
  async updateReview(reviewId: number, data: { marchand_response: string }): Promise<BoutiqueReview> {
    try {
      const response = await axios.put(`/api/boutique-reviews/${reviewId}`, data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'avis:', error);
      throw error;
    }
  }
}

// Instance singleton du service
export const boutiqueReviewService = new BoutiqueReviewService();

export default boutiqueReviewService;
