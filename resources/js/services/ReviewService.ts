import { Review } from '../models/Review';

/**
 * Interface pour les options de pagination et de filtrage des avis
 */
export interface ReviewOptions {
  page?: number;
  perPage?: number;
  sortBy?: 'created_at' | 'rating' | 'likes' | 'dislikes';
  sortOrder?: 'asc' | 'desc';
  rating?: number;
  withImages?: boolean;
}

/**
 * Interface pour les données paginées
 */
export interface PaginatedData<T> {
  current_page: number;
  data: T[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

/**
 * Service pour gérer les opérations liées aux avis
 *
 * Cette classe fournit des méthodes pour récupérer, ajouter et voter pour des avis
 */
export class ReviewService {
  /**
   * Récupère les avis d'un produit
   *
   * @param productId - L'identifiant du produit
   * @returns Une promesse qui résout avec un tableau d'avis
   */
  async getProductReviews(
    productId: string,
    options: ReviewOptions = {}
  ): Promise<{ reviews: Review[], pagination: Omit<PaginatedData<any>, 'data'> }> {
    try {
      // Construire l'URL avec les paramètres de requête
      const params = new URLSearchParams();

      if (options.page) params.append('page', options.page.toString());
      if (options.perPage) params.append('per_page', options.perPage.toString());
      if (options.sortBy) params.append('sort_by', options.sortBy);
      if (options.sortOrder) params.append('sort_order', options.sortOrder);
      if (options.rating !== undefined) params.append('rating', options.rating.toString());
      if (options.withImages !== undefined) params.append('with_images', options.withImages ? 'true' : 'false');

      const url = `/api/produits/${productId}/reviews?${params.toString()}`;

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération des avis du produit ${productId}`);
      }

      const data: PaginatedData<any> = await response.json();

      // Extraire les données paginées
      const { data: reviewsData, ...pagination } = data;

      // Convertir les données en modèles
      const reviews = this.mapApiReviewsToModel(reviewsData);

      return { reviews, pagination };
    } catch (error) {
      console.error(`Erreur dans getProductReviews(${productId}):`, error);
      return { reviews: [], pagination: { current_page: 1, from: 0, last_page: 1, per_page: 5, to: 0, total: 0 } };
    }
  }

  /**
   * Ajoute un avis pour un produit
   *
   * @param productId - L'identifiant du produit
   * @param reviewData - Les données de l'avis
   * @returns Une promesse qui résout avec l'avis créé
   */
  async addReview(
    productId: string,
    reviewData: {
      name: string;
      email?: string;
      rating: number;
      comment: string;
      images?: File[];
    }
  ): Promise<Review | null> {
    try {
      // Créer un FormData pour envoyer les images
      const formData = new FormData();
      formData.append('name', reviewData.name);
      if (reviewData.email) {
        formData.append('email', reviewData.email);
      }
      formData.append('rating', reviewData.rating.toString());
      formData.append('comment', reviewData.comment);

      // Ajouter les images si elles existent
      if (reviewData.images && reviewData.images.length > 0) {
        reviewData.images.forEach((image, index) => {
          formData.append(`images[${index}]`, image);
        });
      }
      console.log("formData", formData);
      const response = await fetch(`/api/produits/${productId}/reviews`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.errors
            ? Object.values(errorData.errors).flat().join(', ')
            : `Erreur lors de l'ajout de l'avis pour le produit ${productId}`
        );
      }

      const data = await response.json();
      return this.mapApiReviewToModel(data);
    } catch (error) {
      console.error(`Erreur dans addReview(${productId}):`, error);
      throw error;
    }
  }

  /**
   * Vote pour un avis (like/dislike)
   *
   * @param reviewId - L'identifiant de l'avis
   * @param voteType - Le type de vote (like/dislike)
   * @returns Une promesse qui résout avec le résultat du vote
   */
  async voteReview(
    reviewId: string,
    voteType: 'like' | 'dislike'
  ): Promise<{ likes: number; dislikes: number; message: string }> {
    try {
      const response = await fetch(`/api/reviews/${reviewId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify({ vote_type: voteType }),
      });

      if (!response.ok) {
        throw new Error(`Erreur lors du vote pour l'avis ${reviewId}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Erreur dans voteReview(${reviewId}, ${voteType}):`, error);
      throw error;
    }
  }

  /**
   * Convertit les données API en modèles d'avis
   *
   * @param apiReviews - Les données d'avis de l'API
   * @returns Un tableau de modèles d'avis
   * @private
   */
  private mapApiReviewsToModel(apiReviews: any[]): Review[] {
    return apiReviews.map(review => this.mapApiReviewToModel(review));
  }

  /**
   * Convertit les données API d'un avis en modèle d'avis
   *
   * @param apiReview - Les données d'un avis de l'API
   * @returns Un modèle d'avis
   * @private
   */
  private mapApiReviewToModel(apiReview: any): Review {
    return new Review(
      apiReview.id.toString(),
      apiReview.produit_id.toString(),
      apiReview.name,
      apiReview.rating,
      apiReview.comment,
      apiReview.created_at,
      apiReview.likes || 0,
      apiReview.dislikes || 0,
      apiReview.image_urls || [],
      apiReview.user ? apiReview.user.name : null
    );
  }
}
