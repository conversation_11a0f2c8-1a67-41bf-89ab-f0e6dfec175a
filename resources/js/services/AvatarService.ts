import axios from 'axios';

export interface AvatarUploadResponse {
  success: boolean;
  message: string;
  avatar_url?: string;
  logo_url?: string;
}

export interface AvatarDeleteResponse {
  success: boolean;
  message: string;
}

export interface AvatarGetResponse {
  success: boolean;
  avatar_url?: string;
  logo_url?: string;
  initials: string;
}

class AvatarService {
  private baseUrl: string;
  private api = axios.create({
    baseURL: import.meta.env.VITE_ADMIN_API_URL || 'http://localhost:8001/api',
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    }
  });

  constructor() {
    // URL de base pour l'API admin
    this.baseUrl = import.meta.env.VITE_ADMIN_API_URL || 'http://localhost:8001/api';
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  /**
   * Génère un token d'authentification simple pour les requêtes cross-domain
   */
  private generateAuthToken(userId: number): string {
    // Token simple basé sur l'ID utilisateur et une clé fixe
    // En production, utilisez une clé plus sécurisée
    const secretKey = 'lorrelei-avatar-key-2024';
    return btoa(userId.toString() + ':' + secretKey);
  }

  /**
   * Récupère l'ID utilisateur actuel depuis le contexte Inertia
   */
  private getCurrentUserId(): number | null {
    // @ts-ignore - Inertia page props
    const user = window.page?.props?.auth?.user;
    return user?.id || null;
  }

  /**
   * Upload d'un avatar utilisateur
   */
  async uploadUserAvatar(file: File, user:any): Promise<AvatarUploadResponse> {
    try {
      const userId = user.id;
      console.log("user = ", user)
      if (!userId) {
        return {
          success: false,
          message: 'Utilisateur non connecté',
        };
      }

      const authToken = this.generateAuthToken(userId);
      const formData = new FormData();
      formData.append('avatar', file);
      formData.append('user_id', userId.toString());
      formData.append('auth_token', authToken);

      const response = await axios.post(`${this.baseUrl}/avatar/user`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: `Erreur de connexion au serveur ${error}`,
      };
    }
  }

  /**
   * Upload d'un logo marchand
   */
  async uploadMarchandLogo(file: File): Promise<AvatarUploadResponse> {
    try {
      const userId = this.getCurrentUserId();
      if (!userId) {
        return {
          success: false,
          message: 'Utilisateur non connecté',
        };
      }

      const authToken = this.generateAuthToken(userId);
      const formData = new FormData();
      formData.append('avatar', file); // Le backend attend 'avatar' même pour les logos
      formData.append('user_id', userId.toString());
      formData.append('auth_token', authToken);

      const response = await axios.post(`${this.baseUrl}/avatar/marchand`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: 'Erreur de connexion au serveur',
      };
    }
  }

  /**
   * Suppression d'un avatar utilisateur
   */
  async deleteUserAvatar(): Promise<AvatarDeleteResponse> {
    try {
      const userId = this.getCurrentUserId();
      if (!userId) {
        return {
          success: false,
          message: 'Utilisateur non connecté',
        };
      }

      const authToken = this.generateAuthToken(userId);
      const response = await axios.delete(`${this.baseUrl}/avatar/user`, {
        data: {
          user_id: userId,
          auth_token: authToken,
        },
      });

      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: 'Erreur de connexion au serveur',
      };
    }
  }

  /**
   * Suppression d'un logo marchand
   */
  async deleteMarchandLogo(): Promise<AvatarDeleteResponse> {
    try {
      const response = await axios.delete(`${this.baseUrl}/avatar/marchand`, {
        withCredentials: true,
      });

      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        message: 'Erreur de connexion au serveur',
      };
    }
  }

  /**
   * Récupération de l'avatar d'un utilisateur
   */
  async getUserAvatar(userId: number): Promise<AvatarGetResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/avatar/user/${userId}`, {
        withCredentials: true,
      });

      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        initials: 'U',
      };
    }
  }

  /**
   * Récupération du logo d'un marchand
   */
  async getMarchandLogo(marchandId: number): Promise<AvatarGetResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/avatar/marchand/${marchandId}`, {
        withCredentials: true,
      });

      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        initials: 'B',
      };
    }
  }

  /**
   * Génère les initiales à partir d'un nom
   */
  generateInitials(name: string): string {
    if (!name) return 'U';

    const words = name.trim().split(' ');
    let initials = '';

    for (const word of words.slice(0, 2)) {
      if (word.length > 0) {
        initials += word.charAt(0).toUpperCase();
      }
    }

    return initials || 'U';
  }

  /**
   * Valide un fichier avant upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Vérification du type
    if (!file.type.startsWith('image/')) {
      return { valid: false, error: 'Le fichier doit être une image' };
    }

    // Vérification des formats acceptés
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Format non supporté. Utilisez JPEG, PNG ou WebP' };
    }

    // Vérification de la taille (2MB max)
    if (file.size > 2 * 1024 * 1024) {
      return { valid: false, error: 'Le fichier ne doit pas dépasser 2MB' };
    }

    return { valid: true };
  }

  /**
   * Redimensionne une image côté client (optionnel)
   */
  async resizeImage(file: File, maxWidth: number = 300, maxHeight: number = 300, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculer les nouvelles dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Dessiner l'image redimensionnée
        ctx?.drawImage(img, 0, 0, width, height);

        // Convertir en blob puis en file
        canvas.toBlob((blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(resizedFile);
          } else {
            resolve(file);
          }
        }, file.type, quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }
}

export const avatarService = new AvatarService();
