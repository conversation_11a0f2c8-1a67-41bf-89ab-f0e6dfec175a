/**
 * Service de gestion des stocks côté frontend
 * Interface avec l'API de gestion des stocks
 */

export interface CartItem {
    id: number;
    quantity: number;
    price?: number;
}

export interface StockCheckResult {
    available: boolean;
    unavailable_items: Array<{
        product_id: number;
        product_name?: string;
        reason: string;
        requested_quantity: number;
        available_quantity: number;
    }>;
    warnings: Array<{
        product_id: number;
        product_name?: string;
        reason: string;
        requested_quantity: number;
        available_quantity: number;
    }>;
    total_items_checked: number;
}

export interface ProductStock {
    product_id: number;
    product_name: string;
    stock: number;
    available: boolean;
    status: string;
}

export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
    errors?: any;
}

/**
 * Service de gestion des stocks
 */
export class StockService {
    private static readonly BASE_URL = '/api/stock';

    /**
     * Vérifie la disponibilité des stocks pour un panier
     */
    static async checkCartAvailability(cart: CartItem[]): Promise<StockCheckResult | null> {
        try {
            const response = await fetch(`${this.BASE_URL}/check-availability`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({ cart })
            });

            const result: ApiResponse<StockCheckResult> = await response.json();

            if (!response.ok) {
                console.error('Erreur API stock check:', result);
                return null;
            }

            if (result.success && result.data) {
                return result.data;
            }

            console.error('Réponse API invalide:', result);
            return null;

        } catch (error) {
            console.error('Erreur lors de la vérification des stocks:', error);
            return null;
        }
    }

    /**
     * Récupère les informations de stock pour un produit
     */
    static async getProductStock(productId: number): Promise<ProductStock | null> {
        try {
            const response = await fetch(`${this.BASE_URL}/product/${productId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                }
            });

            const result: ApiResponse<ProductStock> = await response.json();

            if (!response.ok) {
                console.error('Erreur API product stock:', result);
                return null;
            }

            if (result.success && result.data) {
                return result.data;
            }

            return null;

        } catch (error) {
            console.error('Erreur lors de la récupération du stock produit:', error);
            return null;
        }
    }

    /**
     * Récupère les informations de stock pour plusieurs produits
     */
    static async getMultipleProductsStock(productIds: number[]): Promise<ProductStock[] | null> {
        try {
            const response = await fetch(`${this.BASE_URL}/multiple-products`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({ product_ids: productIds })
            });

            const result: ApiResponse<ProductStock[]> = await response.json();

            if (!response.ok) {
                console.error('Erreur API multiple products stock:', result);
                return null;
            }

            if (result.success && result.data) {
                return result.data;
            }

            return null;

        } catch (error) {
            console.error('Erreur lors de la récupération des stocks multiples:', error);
            return null;
        }
    }

    /**
     * Valide un panier avant le checkout
     */
    static async validateCartBeforeCheckout(cart: CartItem[]): Promise<{
        valid: boolean;
        errors: string[];
        warnings: string[];
    }> {
        const errors: string[] = [];
        const warnings: string[] = [];

        if (!cart || cart.length === 0) {
            errors.push('Le panier est vide');
            return { valid: false, errors, warnings };
        }

        // Vérifier les stocks
        const stockCheck = await this.checkCartAvailability(cart);
        
        if (!stockCheck) {
            errors.push('Impossible de vérifier les stocks. Veuillez réessayer.');
            return { valid: false, errors, warnings };
        }

        // Traiter les articles non disponibles
        if (stockCheck.unavailable_items.length > 0) {
            stockCheck.unavailable_items.forEach(item => {
                if (item.reason === 'Rupture de stock') {
                    errors.push(`${item.product_name || 'Produit'} est en rupture de stock`);
                } else if (item.reason === 'Produit non disponible') {
                    errors.push(`${item.product_name || 'Produit'} n'est plus disponible`);
                } else {
                    errors.push(`${item.product_name || 'Produit'}: ${item.reason}`);
                }
            });
        }

        // Traiter les avertissements
        if (stockCheck.warnings.length > 0) {
            stockCheck.warnings.forEach(item => {
                warnings.push(
                    `Stock limité pour ${item.product_name || 'ce produit'}: ` +
                    `${item.available_quantity} disponible(s), ${item.requested_quantity} demandé(s)`
                );
            });
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * Formate les messages d'erreur pour l'affichage
     */
    static formatStockErrors(stockCheck: StockCheckResult): {
        errors: string[];
        warnings: string[];
    } {
        const errors: string[] = [];
        const warnings: string[] = [];

        stockCheck.unavailable_items.forEach(item => {
            const productName = item.product_name || `Produit #${item.product_id}`;
            
            switch (item.reason) {
                case 'Rupture de stock':
                    errors.push(`${productName} est en rupture de stock`);
                    break;
                case 'Stock insuffisant':
                    errors.push(
                        `Stock insuffisant pour ${productName}. ` +
                        `Disponible: ${item.available_quantity}, Demandé: ${item.requested_quantity}`
                    );
                    break;
                case 'Produit non disponible':
                    errors.push(`${productName} n'est plus disponible`);
                    break;
                default:
                    errors.push(`${productName}: ${item.reason}`);
            }
        });

        stockCheck.warnings.forEach(item => {
            const productName = item.product_name || `Produit #${item.product_id}`;
            warnings.push(
                `Attention: Stock limité pour ${productName}. ` +
                `Disponible: ${item.available_quantity}, Demandé: ${item.requested_quantity}`
            );
        });

        return { errors, warnings };
    }

    /**
     * Vérifie si un produit est en stock
     */
    static async isProductInStock(productId: number, quantity: number = 1): Promise<boolean> {
        const stock = await this.getProductStock(productId);
        return stock ? stock.available && stock.stock >= quantity : false;
    }

    /**
     * Récupère le stock disponible pour un produit
     */
    static async getAvailableQuantity(productId: number): Promise<number> {
        const stock = await this.getProductStock(productId);
        return stock ? stock.stock : 0;
    }
}

export default StockService;
