import { Button } from '@/components/ui/button';

import { cn } from '@/lib/utils';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { type PropsWithChildren, useState } from 'react';
import {
    User,
    ShoppingBag,
    MapPin,
    Shield,
    Palette,
    LayoutDashboard,
    ArrowLeft,
    Menu,
    X,
    MessageSquare,
    MessageCircle,
    AlertTriangle
} from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { useAuthenticatedNotifications } from '@/hooks/useAuthenticatedNotifications';

const sidebarNavItems: NavItem[] = [
    {
        title: 'pages.dashboard.dashboard',
        href: '/dashboard',
        icon: LayoutDashboard,
    },
    {
        title: 'pages.dashboard.my_orders',
        href: '/dashboard/orders',
        icon: ShoppingBag,
    },
    {
        title: 'pages.dashboard.my_conversations',
        href: '/my-account/conversations',
        icon: MessageCircle,
    },
    {
        title: 'pages.dashboard.my_disputes',
        href: '/my-account/disputes',
        icon: AlertTriangle,
    },
    {
        title: 'pages.dashboard.my_profile',
        href: '/dashboard/profile',
        icon: User,
    },
    {
        title: 'pages.dashboard.my_addresses',
        href: '/my-account',
        icon: MapPin,
    },
    {
        title: 'pages.password.change_password',
        href: '/settings/password',
        icon: Shield,
    },
    {
        title: 'pages.appearance.preferences',
        href: '/settings/appearance',
        icon: Palette,
    },
];

interface ClientDashboardLayoutProps extends PropsWithChildren {
    title?: string;
    description?: string;
}

export default function ClientDashboardLayout({
    children,
    title,
    description
}: ClientDashboardLayoutProps) {
    const { translate } = useTranslation();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const { notifications } = useAuthenticatedNotifications();

    // When server-side rendering, we only render the layout on the client...
    if (typeof window === 'undefined') {
        return null;
    }

    const currentPath = window.location.pathname;

    // Fonction pour obtenir le badge de notification pour un élément de navigation
    const getNotificationBadge = (href: string) => {
        if (href === '/my-account/conversations' && notifications.conversations_non_lues > 0) {
            return (
                <span className="ml-auto bg-blue-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                    {notifications.conversations_non_lues > 99 ? '99+' : notifications.conversations_non_lues}
                </span>
            );
        }
        if (href === '/my-account/disputes' && notifications.disputes_non_lues > 0) {
            return (
                <span className="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                    {notifications.disputes_non_lues > 99 ? '99+' : notifications.disputes_non_lues}
                </span>
            );
        }
        return null;
    };

    return (
        <EcommerceLayout>
            <div className="container mx-auto px-4 py-8">
                {/* En-tête avec bouton retour */}
                <div className="mb-8">
                    <div className="flex items-center gap-4 mb-4">
                        <Link href={route('home')}>
                            <Button variant="ghost" size="sm" className="cursor-pointer">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                {translate('pages.dashboard.back_to_shop')}
                            </Button>
                        </Link>
                    </div>

                    {title && (
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                                {title}
                            </h1>
                            {description && (
                                <p className="text-gray-600 dark:text-gray-400 mt-2">
                                    {description}
                                </p>
                            )}
                        </div>
                    )}
                </div>

                {/* Navigation Mobile - Bouton Menu */}
                <div className="lg:hidden mb-6">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                        className="w-full justify-center"
                    >
                        {isMobileMenuOpen ? (
                            <>
                                <X className="h-4 w-4 mr-2" />
                                {translate('common.close_menu')}
                            </>
                        ) : (
                            <>
                                <Menu className="h-4 w-4 mr-2" />
                                {translate('common.navigation_menu')}
                            </>
                        )}
                    </Button>
                </div>

                {/* Navigation Mobile - Menu Déroulant */}
                {isMobileMenuOpen && (
                    <div className="lg:hidden mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <nav className="grid grid-cols-2 gap-2">
                            {sidebarNavItems.map((item) => {
                                const IconComponent = item.icon;
                                const isActive = currentPath === item.href ||
                                    (item.href === '/dashboard' && currentPath.startsWith('/dashboard') && currentPath !== '/dashboard/orders' && currentPath !== '/dashboard/profile');

                                return (
                                    <Button
                                        key={item.href}
                                        size="sm"
                                        variant={isActive ? "default" : "ghost"}
                                        asChild
                                        className="justify-start h-12 text-xs relative"
                                        onClick={() => setIsMobileMenuOpen(false)}
                                    >
                                        <Link href={item.href} prefetch className="flex items-center w-full">
                                            {IconComponent && (
                                                <IconComponent className="h-4 w-4 mr-2" />
                                            )}
                                            <span className="truncate flex-1">{translate(item.title)}</span>
                                            {getNotificationBadge(item.href)}
                                        </Link>
                                    </Button>
                                );
                            })}
                        </nav>
                    </div>
                )}

                <div className="flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-12">
                    {/* Sidebar Navigation - Desktop Only */}
                    <aside className="hidden lg:block lg:w-64">
                        <nav className="flex flex-col space-y-1">
                            {sidebarNavItems.map((item) => {
                                const IconComponent = item.icon;
                                const isActive = currentPath === item.href ||
                                    (item.href === '/dashboard' && currentPath.startsWith('/dashboard') && currentPath !== '/dashboard/orders' && currentPath !== '/dashboard/profile');

                                return (
                                    <Button
                                        key={item.href}
                                        size="sm"
                                        variant="ghost"
                                        asChild
                                        className={cn(
                                            'w-full justify-start h-10 px-4 py-2',
                                            {
                                                'bg-primary/10 text-primary border-r-2 border-primary': isActive,
                                                'hover:bg-muted': !isActive,
                                            }
                                        )}
                                    >
                                        <Link href={item.href} prefetch className="flex items-center w-full">
                                            {IconComponent && (
                                                <IconComponent className="h-4 w-4 mr-3" />
                                            )}
                                            <span className="flex-1">{translate(item.title)}</span>
                                            {getNotificationBadge(item.href)}
                                        </Link>
                                    </Button>
                                );
                            })}
                        </nav>
                    </aside>

                    {/* Contenu principal */}
                    <div className="flex-1">
                        <section className="space-y-6">
                            {children}
                        </section>
                    </div>
                </div>
            </div>
        </EcommerceLayout>
    );
}
