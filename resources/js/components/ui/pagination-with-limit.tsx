import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PaginationLimitSelector } from './pagination-limit-selector';
import { useTranslation } from '@/hooks/use-translation';

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  onItemsPerPageChange: (limit: number) => void;
  allowedLimits?: number[];
  totalItems?: number;
  className?: string;
}

/**
 * Composant de pagination avec sélecteur de limite d'éléments par page
 */
export function PaginationWithLimit({
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
  onItemsPerPageChange,
  allowedLimits = [10, 20, 50, 100],
  totalItems,
  className = '',
}: PaginationProps) {
  const { tDefault } = useTranslation();

  // Générer les numéros de page à afficher
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // Si le nombre total de pages est inférieur ou égal au nombre maximum de pages à afficher,
      // afficher toutes les pages
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Toujours afficher la première page
      pageNumbers.push(1);

      // Calculer les pages à afficher autour de la page courante
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Ajuster si nous sommes près du début ou de la fin
      if (currentPage <= 3) {
        endPage = 4;
      } else if (currentPage >= totalPages - 2) {
        startPage = totalPages - 3;
      }

      // Ajouter des ellipses si nécessaire
      if (startPage > 2) {
        pageNumbers.push('ellipsis-start');
      }

      // Ajouter les pages du milieu
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      // Ajouter des ellipses si nécessaire
      if (endPage < totalPages - 1) {
        pageNumbers.push('ellipsis-end');
      }

      // Toujours afficher la dernière page
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className={`flex flex-col sm:flex-row items-center justify-between gap-4 ${className}`}>
      <div className="flex items-center gap-2">
        {totalItems !== undefined && (
          <span className="text-sm text-muted-foreground">
            {tDefault('pagination.showing', 'Showing')} {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)} - {Math.min(currentPage * itemsPerPage, totalItems)} {tDefault('pagination.of', 'of')} {totalItems}
          </span>
        )}
        <PaginationLimitSelector
          value={itemsPerPage}
          onChange={onItemsPerPageChange}
          allowedLimits={allowedLimits}
        />
      </div>

      <nav className="flex items-center space-x-1">
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          aria-label={tDefault('pagination.previous_page', 'Previous page')}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {pageNumbers.map((page, index) => {
          if (page === 'ellipsis-start' || page === 'ellipsis-end') {
            return (
              <Button
                key={`ellipsis-${index}`}
                variant="ghost"
                size="icon"
                disabled
                className="cursor-default"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            );
          }

          return (
            <Button
              key={page}
              variant={currentPage === page ? 'default' : 'outline'}
              size="icon"
              onClick={() => onPageChange(page as number)}
              aria-label={`${tDefault('pagination.page', 'Page')} ${page}`}
              aria-current={currentPage === page ? 'page' : undefined}
            >
              {page}
            </Button>
          );
        })}

        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          aria-label={tDefault('pagination.next_page', 'Next page')}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </nav>
    </div>
  );
}
