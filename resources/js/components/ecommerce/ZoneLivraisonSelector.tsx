import React, { useState, useEffect } from 'react';
import { useDelivery } from '@/contexts/DeliveryContext';
import { ZoneLivraisonData } from '@/services/zoneLivraisonService';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { MapPin, ChevronRight, Loader2 } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface ZoneLivraisonSelectorProps {
  onZoneSelected?: (zone: ZoneLivraisonData | null) => void;
  className?: string;
  showLabel?: boolean;
  compact?: boolean;
}

/**
 * Composant de sélection de zone de livraison
 */
export default function ZoneLivraisonSelector({
  onZoneSelected,
  className = '',
  showLabel = true,
  compact = false
}: ZoneLivraisonSelectorProps) {
  const { zonesTree, selectedZone, setSelectedZone, isLoading } = useDelivery();
  const { translate } = useTranslation();

  // États pour la sélection hiérarchique
  const [selectedCountry, setSelectedCountry] = useState<ZoneLivraisonData | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<ZoneLivraisonData | null>(null);
  const [selectedCity, setSelectedCity] = useState<ZoneLivraisonData | null>(null);
  const [selectedNeighborhood, setSelectedNeighborhood] = useState<ZoneLivraisonData | null>(null);

  // Listes filtrées pour chaque niveau
  const [regions, setRegions] = useState<ZoneLivraisonData[]>([]);
  const [cities, setCities] = useState<ZoneLivraisonData[]>([]);
  const [neighborhoods, setNeighborhoods] = useState<ZoneLivraisonData[]>([]);

  // Initialiser les sélections à partir de la zone sélectionnée
  useEffect(() => {
    if (selectedZone && zonesTree.length > 0) {
      // Trouver la hiérarchie complète de la zone sélectionnée
      if (selectedZone.type === 'Quartier') {
        setSelectedNeighborhood(selectedZone);

        // Trouver la ville parente
        const city = findParentZone(selectedZone, 'Ville');
        if (city) {
          setSelectedCity(city);

          // Trouver la région parente
          const region = findParentZone(city, 'Region');
          if (region) {
            setSelectedRegion(region);

            // Trouver le pays parent
            const country = findParentZone(region, 'Pays');
            if (country) {
              setSelectedCountry(country);
            }
          }
        }
      } else if (selectedZone.type === 'Ville') {
        setSelectedCity(selectedZone);
        setSelectedNeighborhood(null);

        // Trouver la région parente
        const region = findParentZone(selectedZone, 'Region');
        if (region) {
          setSelectedRegion(region);

          // Trouver le pays parent
          const country = findParentZone(region, 'Pays');
          if (country) {
            setSelectedCountry(country);
          }
        }
      } else if (selectedZone.type === 'Region') {
        setSelectedRegion(selectedZone);
        setSelectedCity(null);
        setSelectedNeighborhood(null);

        // Trouver le pays parent
        const country = findParentZone(selectedZone, 'Pays');
        if (country) {
          setSelectedCountry(country);
        }
      } else if (selectedZone.type === 'Pays') {
        setSelectedCountry(selectedZone);
        setSelectedRegion(null);
        setSelectedCity(null);
        setSelectedNeighborhood(null);
      }
    }
  }, [selectedZone, zonesTree]);

  // Fonction pour trouver une zone parente d'un type spécifique
  const findParentZone = (zone: ZoneLivraisonData, type: string): ZoneLivraisonData | null => {
    // Recherche récursive dans l'arborescence
    const findInTree = (tree: ZoneLivraisonData[], zoneId: number, targetType: string): ZoneLivraisonData | null => {
      for (const node of tree) {
        if (node.id === zoneId && node.type === targetType) {
          return node;
        }

        if (node.enfants && node.enfants.length > 0) {
          const found = findInTree(node.enfants, zoneId, targetType);
          if (found) return found;
        }
      }

      return null;
    };

    // Si la zone a un parent_id, chercher le parent dans l'arborescence
    if (zone.parent_id) {
      for (const country of zonesTree) {
        if (country.id === zone.parent_id && country.type === type) {
          return country;
        }

        if (country.enfants) {
          for (const region of country.enfants) {
            if (region.id === zone.parent_id && region.type === type) {
              return region;
            }

            if (region.enfants) {
              for (const city of region.enfants) {
                if (city.id === zone.parent_id && city.type === type) {
                  return city;
                }
              }
            }
          }
        }
      }
    }

    return null;
  };

  // Mettre à jour les régions lorsqu'un pays est sélectionné
  useEffect(() => {
    if (selectedCountry && selectedCountry.enfants) {
      setRegions(selectedCountry.enfants);
    } else {
      setRegions([]);
    }

    // Réinitialiser les sélections de niveau inférieur
    if (!selectedCountry) {
      setSelectedRegion(null);
      setSelectedCity(null);
      setSelectedNeighborhood(null);
    }
  }, [selectedCountry]);

  // Mettre à jour les villes lorsqu'une région est sélectionnée
  useEffect(() => {
    if (selectedRegion && selectedRegion.enfants) {
      setCities(selectedRegion.enfants);
    } else {
      setCities([]);
    }

    // Réinitialiser les sélections de niveau inférieur
    if (!selectedRegion) {
      setSelectedCity(null);
      setSelectedNeighborhood(null);
    }
  }, [selectedRegion]);

  // Mettre à jour les quartiers lorsqu'une ville est sélectionnée
  useEffect(() => {
    if (selectedCity && selectedCity.enfants) {
      setNeighborhoods(selectedCity.enfants);
    } else {
      setNeighborhoods([]);
    }

    // Réinitialiser les sélections de niveau inférieur
    if (!selectedCity) {
      setSelectedNeighborhood(null);
    }
  }, [selectedCity]);

  // Mettre à jour la zone sélectionnée lorsque la sélection change
  useEffect(() => {
    let newSelectedZone: ZoneLivraisonData | null = null;

    if (selectedNeighborhood) {
      newSelectedZone = selectedNeighborhood;
    } else if (selectedCity) {
      newSelectedZone = selectedCity;
    } else if (selectedRegion) {
      newSelectedZone = selectedRegion;
    } else if (selectedCountry) {
      newSelectedZone = selectedCountry;
    }

    if (newSelectedZone !== selectedZone) {
      setSelectedZone(newSelectedZone);
      if (onZoneSelected) {
        onZoneSelected(newSelectedZone);
      }
    }
  }, [selectedCountry, selectedRegion, selectedCity, selectedNeighborhood]);

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>{translate('Chargement des zones de livraison...')}</span>
      </div>
    );
  }

  if (compact && selectedZone) {
    return (
      <div className={`flex items-center ${className}`}>
        <MapPin className="h-4 w-4 mr-1" />
        <span className="text-sm font-medium">{selectedZone.nom}</span>
        <Button variant="ghost" size="sm" className="ml-1 p-0 h-auto" onClick={() => setSelectedZone(null)}>
          <span className="text-xs text-muted-foreground">{translate('Changer')}</span>
        </Button>
      </div>
    );
  }
  console.log("zone tree = ", zonesTree)
  return (
    <div className={`space-y-4 ${className}`}>
      {showLabel && (
        <div className="flex items-center">
          <MapPin className="h-4 w-4 mr-2" />
          <Label>{translate('Zone de livraison')}</Label>
        </div>
      )}

      <div className="flex flex-col space-y-2">
        {/* Sélection du pays */}
        <Select
          value={selectedCountry?.id.toString() || ''}
          onValueChange={(value) => {
            const country = zonesTree.find(c => c.id.toString() === value) || null;
            setSelectedCountry(country);
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder={translate('Sélectionnez un pays')} />
          </SelectTrigger>
          <SelectContent>
            {zonesTree.map((country) => (
              <SelectItem key={country.id} value={country.id.toString()}>
                {country.nom}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Sélection de la région */}
        {selectedCountry && regions.length > 0 && (
          <div className="flex items-center">
            <ChevronRight className="h-4 w-4 mr-2" />
            <Select
              value={selectedRegion?.id.toString() || ''}
              onValueChange={(value) => {
                const region = regions.find(r => r.id.toString() === value) || null;
                setSelectedRegion(region);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={translate('Sélectionnez une région')} />
              </SelectTrigger>
              <SelectContent>
                {regions.map((region) => (
                  <SelectItem key={region.id} value={region.id.toString()}>
                    {region.nom}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Sélection de la ville */}
        {selectedRegion && cities.length > 0 && (
          <div className="flex items-center">
            <ChevronRight className="h-4 w-4 mr-2" />
            <Select
              value={selectedCity?.id.toString() || ''}
              onValueChange={(value) => {
                const city = cities.find(c => c.id.toString() === value) || null;
                setSelectedCity(city);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={translate('Sélectionnez une ville')} />
              </SelectTrigger>
              <SelectContent>
                {cities.map((city) => (
                  <SelectItem key={city.id} value={city.id.toString()}>
                    {city.nom}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Sélection du quartier */}
        {selectedCity && neighborhoods.length > 0 && (
          <div className="flex items-center">
            <ChevronRight className="h-4 w-4 mr-2" />
            <Select
              value={selectedNeighborhood?.id.toString() || ''}
              onValueChange={(value) => {
                const neighborhood = neighborhoods.find(n => n.id.toString() === value) || null;
                setSelectedNeighborhood(neighborhood);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={translate('Sélectionnez un quartier')} />
              </SelectTrigger>
              <SelectContent>
                {neighborhoods.map((neighborhood) => (
                  <SelectItem key={neighborhood.id} value={neighborhood.id.toString()}>
                    {neighborhood.nom}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
    </div>
  );
}
