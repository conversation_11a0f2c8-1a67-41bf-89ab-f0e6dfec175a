import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Link } from '@inertiajs/react';
import { LogIn, UserPlus } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

export default function UserMenuGuest() {
  const { t } = useTranslation();

  return (
    <>
      <DropdownMenuLabel className="p-0 font-normal">
        <div className="flex flex-col px-3 py-2">
          <span className="font-medium">{t('header.guest')}</span>
          <span className="text-xs text-muted-foreground">{t('header.not_logged_in')}</span>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem asChild>
          <Link href={route('login')} className="flex w-full cursor-pointer items-center">
            <LogIn className="mr-2 h-4 w-4" />
            {t('header.login')}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={route('register')} className="flex w-full cursor-pointer items-center">
            <UserPlus className="mr-2 h-4 w-4" />
            {t('header.register')}
          </Link>
        </DropdownMenuItem>
      </DropdownMenuGroup>
    </>
  );
}
