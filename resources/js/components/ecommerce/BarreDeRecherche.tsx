import { useState, useEffect, useRef } from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ProductService } from '@/services/ProductService';
import { Product } from '@/models/Product';
import { Link, router } from '@inertiajs/react';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Props pour le composant BarreDeRecherche
 */
interface BarreDeRechercheProps {
  className?: string;
  placeholder?: string;
  maxSuggestions?: number;
}

// Removed the top-level call to useTranslation

/**
 * Composant de barre de recherche avec suggestions
 *
 * @param className - Classes CSS additionnelles
 * @param placeholder - Texte d'indication dans le champ de recherche
 * @param maxSuggestions - Nombre maximum de suggestions à afficher
 */
export default function BarreDeRecherche({
  className = '',
  placeholder = '',
  maxSuggestions = 5,
}: BarreDeRechercheProps) {
  const { tDefault } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const placeholderText = placeholder || tDefault('search.placeholder', 'Rechercher des produits...');
  const [suggestions, setSuggestions] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const productService = new ProductService();

  /**
   * Gère la soumission du formulaire de recherche
   *
   * @param e - L'événement de soumission
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      router.visit(route('search', { q: searchTerm.trim() }));
      setShowSuggestions(false);
    }
  };

  /**
   * Gère le changement de texte dans le champ de recherche
   *
   * @param e - L'événement de changement
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.trim().length > 1) {
      setIsLoading(true);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  /**
   * Efface le champ de recherche
   */
  const clearSearch = () => {
    setSearchTerm('');
    setSuggestions([]);
    setShowSuggestions(false);
  };

  // Recherche de suggestions lorsque le terme de recherche change
  useEffect(() => {
    const delaySearch = setTimeout(async () => {
      if (searchTerm.trim().length > 1) {
        try {
          const results = await productService.searchProducts(searchTerm);
          setSuggestions(results.slice(0, maxSuggestions));
        } catch (error) {
          console.error('Erreur lors de la recherche:', error);
        } finally {
          setIsLoading(false);
        }
      }
    }, 300);

    return () => clearTimeout(delaySearch);
  }, [searchTerm, maxSuggestions]);

  // Ferme les suggestions lorsqu'on clique en dehors
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative flex items-center">
          <Search className="absolute left-3 h-5 w-5 text-muted-foreground" />
          <Input
            type="text"
            placeholder={placeholderText}
            onChange={handleChange}
            className="pl-10 pr-10"
            aria-label="Rechercher"
          />
          {searchTerm && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-1 h-8 w-8"
              onClick={clearSearch}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Effacer</span>
            </Button>
          )}
        </div>
      </form>

      {/* Suggestions */}
      {showSuggestions && (
        <div className="absolute z-50 mt-1 w-full rounded-md border bg-background p-2 shadow-lg">
          {isLoading ? (
            <div className="p-2 text-center text-sm text-muted-foreground">
              Recherche en cours...
            </div>
          ) : suggestions.length > 0 ? (
            <ul>
              {suggestions.map((product) => (
                <li key={product.id}>
                  <Link
                    href={route('product', { productSlug: product.slug })}
                    className="flex items-center gap-3 rounded-md p-2 hover:bg-muted"
                    onClick={() => setShowSuggestions(false)}
                  >
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="h-10 w-10 rounded-md object-cover"
                    />
                    <div className="flex-1 overflow-hidden">
                      <p className="truncate font-medium">{product.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {product.formattedPrice()}
                      </p>
                    </div>
                  </Link>
                </li>
              ))}
              <li className="mt-1 border-t pt-1">
                <Link
                  href={route('search', { q: searchTerm })}
                  className="block rounded-md p-2 text-center text-sm font-medium text-primary hover:bg-muted"
                  onClick={() => setShowSuggestions(false)}
                >
                  Voir tous les résultats
                </Link>
              </li>
            </ul>
          ) : searchTerm.trim().length > 1 ? (
            <div className="p-2 text-center text-sm text-muted-foreground">
              Aucun résultat trouvé
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
