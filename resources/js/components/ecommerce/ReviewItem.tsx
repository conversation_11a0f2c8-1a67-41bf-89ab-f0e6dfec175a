import { useState } from 'react';
import { useTranslation } from '@/hooks/use-translation';
import { Review } from '@/models/Review';
import { Star, ThumbsUp, ThumbsDown, Image } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ReviewService } from '@/services/ReviewService';
import { toast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

/**
 * Formate une date en date relative (ex: "À l'instant", "Il y a 2 jours")
 *
 * @param dateString - La date à formater
 * @param t - Fonction de traduction
 * @returns La date formatée
 */
const formatRelativeDate = (dateString: string, t: any): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();

  // Convertir en minutes, heures, jours
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  // Formater la date relative
  if (diffMinutes < 1) {
    return t('reviews.just_now');
  } else if (diffMinutes < 60) {
    return t('reviews.minutes_ago', { count: diffMinutes });
  } else if (diffHours < 24) {
    return t('reviews.hours_ago', { count: diffHours });
  } else {
    return t('reviews.days_ago', { count: diffDays });
  }
};

/**
 * Formate une date en date exacte (ex: "12 avril 2025 à 14:30")
 *
 * @param dateString - La date à formater
 * @returns La date formatée
 */
const formatExactDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Formate un nombre pour l'affichage (ex: 1000 -> 1k, 1500 -> 1.5k)
 *
 * @param num - Le nombre à formater
 * @returns Le nombre formaté
 */
const formatNumber = (num: number): string => {
  if (num < 1000) {
    return num.toString();
  }

  const formatted = num / 1000;
  if (formatted === Math.floor(formatted)) {
    return `${formatted}k`;
  }

  return `${formatted.toFixed(1)}k`;
};

interface ReviewItemProps {
  review: Review;
  onVoteChange?: () => void;
}

export default function ReviewItem({ review, onVoteChange }: ReviewItemProps) {
  const [userVote, setUserVote] = useState<'like' | 'dislike' | null>(null);
  const [likes, setLikes] = useState(review.likes);
  const [dislikes, setDislikes] = useState(review.dislikes);
  const [isVoting, setIsVoting] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const reviewService = new ReviewService();
  const { t } = useTranslation();

  const handleVote = async (voteType: 'like' | 'dislike') => {
    if (isVoting) return;

    setIsVoting(true);

    try {
      const result = await reviewService.voteReview(review.id, voteType);
      setLikes(result.likes);
      setDislikes(result.dislikes);

      // Mettre à jour le vote de l'utilisateur
      if (userVote === voteType) {
        // Si l'utilisateur clique sur le même bouton, annuler son vote
        setUserVote(null);
      } else {
        // Sinon, enregistrer son nouveau vote
        setUserVote(voteType);
      }

      if (onVoteChange) {
        onVoteChange();
      }
    } catch (error) {
      console.error('Erreur lors du vote:', error);
      toast({
        title: t('toast.error'),
        description: t('reviews.review_error'),
        variant: "destructive",
      });
    } finally {
      setIsVoting(false);
    }
  };
  console.log("review", review);
  return (
    <div className="rounded-lg border p-4">
      <div className="mb-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="flex items-center text-amber-500">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${i < review.rating ? 'fill-current' : ''}`}
              />
            ))}
          </div>
          <span className="font-medium">{review.getDisplayName()}</span>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="text-xs text-muted-foreground">
                {formatRelativeDate(review.createdAt, t)}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{formatExactDate(review.createdAt)}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <p className="text-sm">{review.comment}</p>

      {/* Affichage des images */}
      {review.hasImages() && (
        <div className="mt-3 flex flex-wrap gap-2">
          {review.imageUrls.map((url, index) => (
            <Dialog key={index}>
              <DialogTrigger asChild>
                <button
                  className="h-16 w-16 overflow-hidden rounded-md border"
                  onClick={() => setSelectedImageIndex(index)}
                >
                  <img
                    src={url}
                    alt={`Image ${index + 1} de l'avis`}
                    className="h-full w-full object-cover"
                  />
                </button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <div className="flex h-full w-full items-center justify-center">
                  <img
                    src={url}
                    alt={`Image ${index + 1} de l'avis`}
                    className="max-h-[70vh] max-w-full object-contain"
                  />
                </div>
              </DialogContent>
            </Dialog>
          ))}
        </div>
      )}



      <div className="mt-3 flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          className={`flex items-center gap-1 text-xs ${userVote === 'like' ? 'text-blue-600' : ''}`}
          onClick={() => handleVote('like')}
          disabled={isVoting}
        >
          <ThumbsUp className={`h-3.5 w-3.5 ${userVote === 'like' ? 'fill-blue-600' : ''}`} />
          <span>{formatNumber(likes)}</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className={`flex items-center gap-1 text-xs ${userVote === 'dislike' ? 'text-red-600' : ''}`}
          onClick={() => handleVote('dislike')}
          disabled={isVoting}
        >
          <ThumbsDown className={`h-3.5 w-3.5 ${userVote === 'dislike' ? 'fill-red-600' : ''}`} />
          <span>{formatNumber(dislikes)}</span>
        </Button>
      </div>
    </div>
  );
}
