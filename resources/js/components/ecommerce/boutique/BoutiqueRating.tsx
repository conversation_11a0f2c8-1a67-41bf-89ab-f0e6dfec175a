import React from 'react';
import { Star } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface BoutiqueRatingProps {
  rating: number;
  totalReviews: number;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

export default function BoutiqueRating({ 
  rating, 
  totalReviews, 
  size = 'md', 
  showText = true,
  className = '' 
}: BoutiqueRatingProps) {
  const { translate } = useTranslation();

  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const starSize = sizeClasses[size];
  const textSize = textSizeClasses[size];

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Étoiles */}
      <div className="flex items-center">
        {Array.from({ length: 5 }).map((_, index) => {
          const isFilled = index < Math.floor(rating);
          const isHalfFilled = index === Math.floor(rating) && rating % 1 >= 0.5;
          
          return (
            <div key={index} className="relative">
              <Star 
                className={`${starSize} text-gray-300 dark:text-gray-600`}
              />
              {(isFilled || isHalfFilled) && (
                <Star 
                  className={`${starSize} absolute top-0 left-0 text-amber-400 fill-current`}
                  style={{
                    clipPath: isHalfFilled ? 'inset(0 50% 0 0)' : 'none'
                  }}
                />
              )}
            </div>
          );
        })}
      </div>

      {/* Texte de notation */}
      {showText && (
        <div className={`flex items-center gap-1 ${textSize} text-gray-600 dark:text-gray-400`}>
          <span className="font-medium text-gray-900 dark:text-gray-100">
            {rating.toFixed(1)}
          </span>
          <span>
            ({totalReviews} {translate(totalReviews === 1 ? 'reviews.review' : 'reviews.reviews')})
          </span>
        </div>
      )}
    </div>
  );
}
