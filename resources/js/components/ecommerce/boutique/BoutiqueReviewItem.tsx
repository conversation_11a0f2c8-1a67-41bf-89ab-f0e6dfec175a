import React, { useState } from 'react';
import { Star, ThumbsUp, ThumbsDown, Flag, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTranslation } from '@/hooks/use-translation';
import { BoutiqueReview } from '@/services/BoutiqueReviewService';
import { formatRelativeDate } from '@/utils/format';

interface BoutiqueReviewItemProps {
  review: BoutiqueReview;
  onVote?: (reviewId: number, voteType: 'like' | 'dislike') => void;
  onReport?: (reviewId: number, reason: string) => void;
  className?: string;
}

export default function BoutiqueReviewItem({
  review,
  onVote,
  onReport,
  className = ''
}: BoutiqueReviewItemProps) {
  const { translate } = useTranslation();
  const [isReporting, setIsReporting] = useState(false);

  const handleVote = (voteType: 'like' | 'dislike') => {
    if (onVote) {
      onVote(review.id, voteType);
    }
  };

  const handleReport = (reason: string) => {
    if (onReport) {
      onReport(review.id, reason);
      setIsReporting(false);
    }
  };

  return (
    <Card className={`${className}`}>
      <CardContent className="p-4 sm:p-6">
        {/* En-tête de l'avis */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-3">
          <div className="flex items-center gap-3">
            {/* Avatar ou initiales */}
            <div className="flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full bg-primary/10 text-primary font-medium text-sm">
              {review.name.charAt(0).toUpperCase()}
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 flex-wrap">
                <span className="font-medium text-gray-900 dark:text-gray-100 truncate">
                  {review.name}
                </span>
                {review.is_verified && (
                  <Badge variant="secondary" className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    {translate('reviews.verified_purchase')}
                  </Badge>
                )}
              </div>

              {/* Étoiles et date */}
              <div className="flex items-center gap-2 mt-1">
                <div className="flex items-center">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 sm:h-4 sm:w-4 ${
                        i < review.rating
                          ? 'text-amber-400 fill-current'
                          : 'text-gray-300 dark:text-gray-600'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                  {review.formatted_date || formatRelativeDate(review.created_at)}
                </span>
              </div>
            </div>
          </div>

          {/* Menu actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleReport('inappropriate')}>
                <Flag className="mr-2 h-4 w-4" />
                {translate('reviews.report')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Titre de l'avis */}
        {review.title && (
          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2 text-sm sm:text-base">
            {review.title}
          </h4>
        )}

        {/* Commentaire */}
        <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm sm:text-base leading-relaxed">
          {review.comment}
        </p>

        {/* Images de l'avis */}
        {review.image_urls && review.image_urls.length > 0 && (
          <div className="flex gap-2 mb-4 overflow-x-auto pb-2">
            {review.image_urls.map((imageUrl, index) => (
              <img
                key={index}
                src={imageUrl}
                alt={`Avis ${index + 1}`}
                className="h-16 w-16 sm:h-20 sm:w-20 rounded-lg object-cover flex-shrink-0 border border-gray-200 dark:border-gray-700"
              />
            ))}
          </div>
        )}

        {/* Réponse du marchand */}
        {review.marchand_response && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 sm:p-4 mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="text-xs">
                {translate('reviews.merchant_response')}
              </Badge>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formatRelativeDate(review.marchand_response_at || '')}
              </span>
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              {review.marchand_response}
            </p>
          </div>
        )}

        {/* Actions de vote */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleVote('like')}
            className="flex items-center gap-1 text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400"
          >
            <ThumbsUp className="h-4 w-4" />
            <span className="text-xs sm:text-sm">{review.likes}</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleVote('dislike')}
            className="flex items-center gap-1 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400"
          >
            <ThumbsDown className="h-4 w-4" />
            <span className="text-xs sm:text-sm">{review.dislikes}</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
