import { Marchand } from '@/models/Marchand';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, Globe, Phone, Mail, MapPin, Tag } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import BoutiqueReviews from './BoutiqueReviews';

interface BoutiqueInfoProps {
  marchand: Marchand;
}

export default function BoutiqueInfo({ marchand }: BoutiqueInfoProps) {
  const { translate } = useTranslation();

  return (
    <div className="space-y-6">
      {/* Description de la boutique */}
      {marchand.hasDescription() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="h-5 w-5" />
              <span>{translate('pages.boutique.about_title', { merchant: marchand.nomEntreprise })}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              {marchand.description}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Informations de contact et détails */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Informations de contact */}
        <Card>
          <CardHeader>
            <CardTitle>{translate('pages.boutique.contact')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {marchand.telephone && (
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.phone')}</p>
                  <p className="font-medium dark:text-gray-200">{marchand.telephone}</p>
                </div>
              </div>
            )}

            {marchand.email && (
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.email')}</p>
                  <p className="font-medium dark:text-gray-200">{marchand.email}</p>
                </div>
              </div>
            )}

            {marchand.getLocation() && (
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.location')}</p>
                  <p className="font-medium dark:text-gray-200">{marchand.getLocation()}</p>
                </div>
              </div>
            )}

            {marchand.hasSiteWeb() && (
              <div className="flex items-center space-x-3">
                <Globe className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.website')}</p>
                  <a
                    href={marchand.getFormattedSiteWeb()}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                  >
                    {marchand.getSiteWebDisplayName()}
                  </a>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Détails de l'entreprise */}
        <Card>
          <CardHeader>
            <CardTitle>{translate('pages.boutique.company_details')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {marchand.typeBusiness && (
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.business_type')}</p>
                <Badge variant="outline" className="mt-1">
                  {marchand.typeBusiness}
                </Badge>
              </div>
            )}

            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.member_since')}</p>
              <p className="font-medium dark:text-gray-200">{marchand.getFormattedJoinedDate()}</p>
            </div>

            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.status')}</p>
              <Badge variant="default" className="mt-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                {translate('common.verified_merchant')}
              </Badge>
            </div>

            {/* Badge de qualité */}
            {marchand.getQualityBadge() && (
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">{translate('pages.boutique.certification')}</p>
                <Badge variant="secondary" className="mt-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  {translate(`common.${marchand.getQualityBadge()?.toLowerCase().replace(' ', '_')}`)}
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Catégories de produits */}
      {marchand.hasCategories() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Tag className="h-5 w-5" />
              <span>{translate('pages.boutique.categories')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {marchand.categories.map((category, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="px-3 py-1"
                >
                  {category}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Politiques et informations importantes */}
      <Card>
        <CardHeader>
          <CardTitle>{translate('pages.boutique.policies')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{translate('pages.boutique.delivery_policy')}</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {translate('pages.boutique.delivery_policy_text')}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{translate('pages.boutique.return_policy')}</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {translate('pages.boutique.return_policy_text')}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{translate('pages.boutique.payment_policy')}</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {translate('pages.boutique.payment_policy_text')}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Section des avis clients */}
      <BoutiqueReviews marchandId={marchand.id} />
    </div>
  );
}
