import React, { useState } from 'react';
import { Star, Upload, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useTranslation } from '@/hooks/use-translation';
import { boutiqueReviewService } from '@/services/BoutiqueReviewService';

interface BoutiqueReviewFormProps {
  marchandId: number;
  onSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

export default function BoutiqueReviewForm({ 
  marchandId, 
  onSuccess, 
  onCancel,
  className = '' 
}: BoutiqueReviewFormProps) {
  const { translate } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    title: '',
    comment: ''
  });
  const [images, setImages] = useState<File[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'].includes(file.type);
      const isValidSize = file.size <= 2 * 1024 * 1024; // 2MB
      return isValidType && isValidSize;
    });

    setImages(prev => [...prev, ...validFiles].slice(0, 5)); // Max 5 images
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = translate('reviews.name_required');
    }
    if (!rating) {
      newErrors.rating = translate('reviews.rating_required');
    }
    if (!formData.comment.trim() || formData.comment.length < 10) {
      newErrors.comment = translate('reviews.comment_min_length');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      const submitData = new FormData();
      submitData.append('name', formData.name);
      submitData.append('email', formData.email);
      submitData.append('rating', rating.toString());
      submitData.append('title', formData.title);
      submitData.append('comment', formData.comment);
      
      images.forEach((image, index) => {
        submitData.append(`images[${index}]`, image);
      });

      await boutiqueReviewService.createBoutiqueReview(marchandId, submitData);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg sm:text-xl">
          {translate('reviews.write_review')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          {/* Notation par étoiles */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {translate('reviews.your_rating')} *
            </Label>
            <div className="flex items-center gap-1">
              {Array.from({ length: 5 }).map((_, index) => {
                const starValue = index + 1;
                return (
                  <button
                    key={index}
                    type="button"
                    className="p-1 rounded focus:outline-none focus:ring-2 focus:ring-primary"
                    onMouseEnter={() => setHoveredRating(starValue)}
                    onMouseLeave={() => setHoveredRating(0)}
                    onClick={() => setRating(starValue)}
                  >
                    <Star
                      className={`h-6 w-6 sm:h-8 sm:w-8 transition-colors ${
                        starValue <= (hoveredRating || rating)
                          ? 'text-amber-400 fill-current'
                          : 'text-gray-300 dark:text-gray-600'
                      }`}
                    />
                  </button>
                );
              })}
            </div>
            {errors.rating && (
              <p className="text-sm text-red-600 dark:text-red-400">{errors.rating}</p>
            )}
          </div>

          {/* Nom */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">
              {translate('reviews.your_name')} *
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder={translate('reviews.name_placeholder')}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-600 dark:text-red-400">{errors.name}</p>
            )}
          </div>

          {/* Email (optionnel) */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              {translate('reviews.your_email')}
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder={translate('reviews.email_placeholder')}
            />
          </div>

          {/* Titre (optionnel) */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium">
              {translate('reviews.review_title')}
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder={translate('reviews.title_placeholder')}
            />
          </div>

          {/* Commentaire */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-sm font-medium">
              {translate('reviews.your_review')} *
            </Label>
            <Textarea
              id="comment"
              value={formData.comment}
              onChange={(e) => handleInputChange('comment', e.target.value)}
              placeholder={translate('reviews.comment_placeholder')}
              rows={4}
              className={errors.comment ? 'border-red-500' : ''}
            />
            {errors.comment && (
              <p className="text-sm text-red-600 dark:text-red-400">{errors.comment}</p>
            )}
          </div>

          {/* Upload d'images */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              {translate('reviews.add_photos')}
            </Label>
            <div className="space-y-3">
              {/* Bouton d'upload */}
              <div className="flex items-center gap-2">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <Label
                  htmlFor="image-upload"
                  className="flex items-center gap-2 px-3 py-2 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  <span className="text-sm">{translate('reviews.upload_photos')}</span>
                </Label>
              </div>

              {/* Aperçu des images */}
              {images.length > 0 && (
                <div className="flex gap-2 flex-wrap">
                  {images.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(image)}
                        alt={`Preview ${index + 1}`}
                        className="h-16 w-16 sm:h-20 sm:w-20 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 h-6 w-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Boutons d'action */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 sm:flex-none"
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {translate('reviews.submit_review')}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="flex-1 sm:flex-none"
              >
                {translate('common.cancel')}
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
