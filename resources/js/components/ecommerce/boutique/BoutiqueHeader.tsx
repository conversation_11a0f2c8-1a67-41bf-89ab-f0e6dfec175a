import { Star, MapPin, Calendar, Award } from 'lucide-react';
import { Marchand } from '@/models/Marchand';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/use-translation';
import {  usePage } from '@inertiajs/react';
import BoutiqueRating from './BoutiqueRating';

interface BoutiqueHeaderProps {
  marchand: Marchand;
  stats?: {
    totalProducts: number;
    averageRating: number;
    totalReviews: number;
    totalSales: number;
    joinedDate: string;
  } | null;
}

export default function BoutiqueHeader({ marchand, stats }: BoutiqueHeaderProps) {
  const { translate } = useTranslation();
  const { url } = usePage();
  console.log("url = ", url, url.includes('/produits'))
  // Déterminer quelle page est active
  const isOverviewActive = url === marchand.getBoutiqueUrl() || url === `${marchand.getBoutiqueUrl()}/`;
  const isProductsActive = url.includes('/produits');
  const isAboutActive = url.includes('/a-propos');
  const renderStars = () => {
    const rating = stats?.averageRating || marchand.averageRating;
    const fullStars = Math.floor(rating);
    const hasHalfStar = (rating % 1) >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return (
      <div className="flex items-center space-x-1">
        {/* Étoiles pleines */}
        {Array.from({ length: fullStars }).map((_, index) => (
          <Star key={`full-${index}`} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
        ))}

        {/* Demi-étoile */}
        {hasHalfStar && (
          <div className="relative">
            <Star className="h-4 w-4 text-gray-300" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            </div>
          </div>
        )}

        {/* Étoiles vides */}
        {Array.from({ length: emptyStars }).map((_, index) => (
          <Star key={`empty-${index}`} className="h-4 w-4 text-gray-300" />
        ))}
      </div>
    );
  };

  return (
    <div className="relative">
      {/* Bannière de fond */}
      <div
        className="h-64 bg-gradient-to-r from-blue-600 to-purple-600 bg-cover bg-center relative"
        style={{
          backgroundImage: marchand.bannerUrl ? `url(${marchand.getBannerUrl()})` : undefined
        }}
      >
        {/* Overlay pour améliorer la lisibilité */}
        <div className="absolute inset-0 bg-black/40 dark:bg-black/60"></div>

        {/* Contenu de l'en-tête */}
        <div className="relative container mx-auto px-4 h-full flex items-end pb-4 md:pb-8">
          <div className="flex flex-col md:flex-row md:items-end space-y-4 md:space-y-0 md:space-x-6 w-full">
            {/* Logo du marchand */}
            <div className="flex-shrink-0 self-center md:self-auto">
              <div className="w-16 h-16 md:w-24 md:h-24 bg-white rounded-full p-2 shadow-lg">
                <img
                  src={marchand.getLogoUrl()}
                  alt={`Logo ${marchand.nomEntreprise}`}
                  className="w-full h-full object-cover rounded-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/images/default-merchant-logo.png';
                  }}
                />
              </div>
            </div>

            {/* Informations principales */}
            <div className="flex-1 text-white text-center md:text-left">
              <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-3 mb-2">
                <h1 className="text-xl md:text-3xl font-bold">{marchand.nomEntreprise}</h1>

                {/* Badge de qualité */}
                {marchand.getQualityBadge() && (
                  <Badge variant="secondary" className="bg-white/20 dark:bg-white/10 text-white border-white/30 dark:border-white/20">
                    <Award className="h-3 w-3 mr-1" />
                    {translate(`common.${marchand.getQualityBadge()?.toLowerCase().replace(' ', '_')}`)}
                  </Badge>
                )}
              </div>

              {/* Évaluation et localisation */}
              <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-6 text-sm">
                {/* Note et avis */}
                <div className="flex items-center">
                  <BoutiqueRating
                    rating={stats?.averageRating || marchand.averageRating}
                    totalReviews={stats?.totalReviews || marchand.reviewsCount}
                    size="md"
                    className="text-white [&_*]:text-white [&_.text-gray-600]:text-white/80 [&_.text-gray-400]:text-white/70 [&_.text-gray-900]:text-white [&_.text-gray-100]:text-white"
                  />
                </div>

                {/* Localisation */}
                {marchand.getLocation() && (
                  <div className="flex items-center space-x-1 text-white/80">
                    <MapPin className="h-4 w-4" />
                    <span>{marchand.getLocation()}</span>
                  </div>
                )}

                {/* Date d'inscription */}
                <div className="flex items-center space-x-1 text-white/80 dark:text-white/70">
                  <Calendar className="h-4 w-4" />
                  <span>{translate('pages.boutique.member_since')} {marchand.getFormattedJoinedDate()}</span>
                </div>
              </div>

              {/* Description courte */}
              {marchand.hasDescription() && (
                <p className="mt-3 text-white/90 max-w-2xl">
                  {marchand.getTruncatedDescription(120)}
                </p>
              )}
            </div>

            {/* Statistiques rapides */}
            {stats && (
              <div className="flex flex-wrap justify-center md:justify-start lg:justify-center gap-4 lg:gap-8 text-white text-center mt-4 md:mt-0">
                <div className="min-w-0">
                  <div className="text-lg md:text-2xl font-bold">{stats.totalProducts}</div>
                  <div className="text-xs md:text-sm text-white/80 dark:text-white/70">{translate('pages.boutique.products_count')}</div>
                </div>
                <div className="min-w-0">
                  <div className="text-lg md:text-2xl font-bold">{stats.totalSales}</div>
                  <div className="text-xs md:text-sm text-white/80 dark:text-white/70">{translate('pages.boutique.sales_count')}</div>
                </div>
                <div className="min-w-0">
                  <div className="text-lg md:text-2xl font-bold">{stats.averageRating.toFixed(1)}</div>
                  <div className="text-xs md:text-sm text-white/80 dark:text-white/70">{translate('pages.boutique.average_rating')}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Navigation de la boutique */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="container mx-auto px-4">
          <nav className="flex flex-wrap justify-center md:justify-start space-x-4 md:space-x-8">
            <a
              href={marchand.getBoutiqueUrl()}
              className={`py-4 px-2 border-b-2 ${isOverviewActive ?
                  'border-blue-600 text-blue-600 dark:text-blue-400 font-medium' :
                  'border-transparent text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-300 dark:hover:border-gray-600 transition-colors'
              }`
              }
            >
              {translate('common.shop_overview')}
            </a>
            <a
              href={`${marchand.getBoutiqueUrl()}/produits`}
              className={`py-4 px-2 border-b-2 ${isProductsActive ?
                  'border-blue-600 text-blue-600 dark:text-blue-400 font-medium' :
                  'border-transparent text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-300 dark:hover:border-gray-600 transition-colors'
              }`
              }
            >
              {translate('common.shop_products')}
            </a>
            <a
              href={`${marchand.getBoutiqueUrl()}/a-propos`}
              className={`py-4 px-2 border-b-2 ${isAboutActive ?
                  'border-blue-600 text-blue-600 dark:text-blue-400 font-medium' :
                  'border-transparent text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-300 dark:hover:border-gray-600 transition-colors'
              }`
              }
            >
              {translate('common.shop_about')}
            </a>
          </nav>
        </div>
      </div>
    </div>
  );
}
