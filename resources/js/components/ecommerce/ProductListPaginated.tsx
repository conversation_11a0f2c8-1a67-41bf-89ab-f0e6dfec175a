import { useState, useEffect } from 'react';
import { Product } from '@/models/Product';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import { ProductService } from '@/services/ProductService';
import CardProduit from './CardProduit';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ProductListPaginatedProps {
    initialProducts: Product[];
    totalItems: number;
    itemsPerPage?: number;
    className?: string;
    emptyMessage?: string;
    onPageChange?: (page: number) => void;
    onItemsPerPageChange?: (itemsPerPage: number) => void;
    allowedItemsPerPage?: number[];
}

/**
 * Composant pour afficher une liste de produits avec pagination
 */
export default function ProductListPaginated({
    initialProducts = [],
    totalItems = 0,
    itemsPerPage = 12,
    className = '',
    emptyMessage = 'Aucun produit trouvé',
    onPageChange,
    onItemsPerPageChange,
    allowedItemsPerPage = [12, 24, 36, 48]
}: ProductListPaginatedProps) {
    // États
    const [products, setProducts] = useState<Product[]>(initialProducts);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [perPage, setPerPage] = useState<number>(itemsPerPage);

    // Services et hooks
    const productService = new ProductService();
    const { tDefault } = useTranslation();

    // Calcul du nombre total de pages
    const totalPages = Math.max(1, Math.ceil(totalItems / perPage));

    // Calcul des indices pour l'affichage "Affichage de X-Y sur Z produits"
    const startIndex = (currentPage - 1) * perPage + 1;
    const endIndex = Math.min(currentPage * perPage, totalItems);

    // Mettre à jour les produits lorsque initialProducts change
    useEffect(() => {
        setProducts(initialProducts);
    }, [initialProducts]);

    // Fonction pour charger une page spécifique
    const loadPage = async (page: number) => {
        if (page < 1 || page > totalPages || page === currentPage) return;

        setIsLoading(true);

        try {
            // Si un gestionnaire de changement de page est fourni, l'utiliser
            if (onPageChange) {
                onPageChange(page);
                setCurrentPage(page);
            } else {
                // Sinon, charger les produits directement
                const result = await productService.getProductsPaginated(page, perPage);
                setProducts(result.products);
                setCurrentPage(page);
            }
        } catch (error) {
            console.error('Erreur lors du chargement de la page:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Fonction pour gérer le changement du nombre d'éléments par page
    const handleItemsPerPageChange = (value: string) => {
        const newPerPage = parseInt(value, 10);
        setPerPage(newPerPage);

        // Calculer la nouvelle page pour conserver approximativement la même position
        const firstItemIndex = (currentPage - 1) * perPage;
        const newPage = Math.floor(firstItemIndex / newPerPage) + 1;

        // Si un gestionnaire de changement d'éléments par page est fourni, l'utiliser
        if (onItemsPerPageChange) {
            onItemsPerPageChange(newPerPage);
        }

        // Charger la nouvelle page
        loadPage(newPage);
    };

    // Afficher un message si aucun produit n'est trouvé
    if (products.length === 0 && !isLoading) {
        return <div className="py-8 text-center text-muted-foreground">{emptyMessage}</div>;
    }

    return (
        <div className={className}>
            {/* Grille de produits */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {isLoading ? (
                    // Afficher des placeholders pendant le chargement
                    Array.from({ length: itemsPerPage }).map((_, index) => (
                        <div key={index} className="animate-pulse rounded-lg border">
                            <div className="aspect-square bg-muted"></div>
                            <div className="p-4 space-y-3">
                                <div className="h-4 w-1/4 rounded bg-muted"></div>
                                <div className="h-4 w-3/4 rounded bg-muted"></div>
                                <div className="h-4 w-1/2 rounded bg-muted"></div>
                                <div className="h-8 rounded bg-muted"></div>
                            </div>
                        </div>
                    ))
                ) : (
                    // Afficher les produits en utilisant CardProduit
                    products.map(product => (
                        <CardProduit key={product.id} product={product} />
                    ))
                )}
            </div>

            {/* Informations et contrôles de pagination */}
            <div className="mt-8 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                {/* Informations sur les éléments affichés */}
                <div className="text-sm text-muted-foreground text-center md:text-left">
                    {totalItems > 0 ? (
                        <>
                            <span className="hidden sm:inline">
                                {tDefault(
                                    'pagination.showing_items',
                                    `Affichage de ${startIndex}-${endIndex} sur ${totalItems} produits`
                                )}
                            </span>
                            <span className="sm:hidden">
                                {tDefault(
                                    'pagination.showing_items_short',
                                    `${startIndex}-${endIndex} / ${totalItems}`
                                )}
                            </span>
                        </>
                    ) : (
                        tDefault('pagination.no_items', 'Aucun produit trouvé')
                    )}
                </div>

                {/* Sélecteur d'éléments par page */}
                <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                        <span className="hidden sm:inline">{tDefault('pagination.items_per_page', 'Produits par page')}</span>
                        <span className="sm:hidden">{tDefault('pagination.items_per_page_short', 'Par page')}</span>:
                    </span>
                    <Select
                        value={perPage.toString()}
                        onValueChange={handleItemsPerPageChange}
                        disabled={isLoading}
                    >
                        <SelectTrigger className="w-[70px] sm:w-[80px]">
                            <SelectValue placeholder={perPage.toString()} />
                        </SelectTrigger>
                        <SelectContent>
                            {allowedItemsPerPage.map(value => (
                                <SelectItem key={value} value={value.toString()}>
                                    {value}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                {/* Contrôles de pagination */}
                {totalPages > 1 && (
                    <div className="flex items-center justify-center space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => loadPage(currentPage - 1)}
                            disabled={currentPage === 1 || isLoading}
                        >
                            <ChevronLeft className="h-4 w-4" />
                            <span className="ml-1 hidden sm:inline">{tDefault('pagination.previous', 'Précédent')}</span>
                        </Button>

                        <div className="flex items-center space-x-1">
                            {/* Afficher les numéros de page - version mobile simplifiée */}
                            <div className="hidden sm:flex items-center space-x-1">
                                {Array.from({ length: Math.min(5, totalPages) }).map((_, index) => {
                                    // Calculer le numéro de page à afficher
                                    let pageNumber: number;

                                    if (totalPages <= 5) {
                                        // Si moins de 5 pages, afficher toutes les pages
                                        pageNumber = index + 1;
                                    } else if (currentPage <= 3) {
                                        // Si on est au début, afficher les 5 premières pages
                                        pageNumber = index + 1;
                                    } else if (currentPage >= totalPages - 2) {
                                        // Si on est à la fin, afficher les 5 dernières pages
                                        pageNumber = totalPages - 4 + index;
                                    } else {
                                        // Sinon, afficher 2 pages avant et 2 pages après la page courante
                                        pageNumber = currentPage - 2 + index;
                                    }

                                    return (
                                        <Button
                                            key={pageNumber}
                                            variant={pageNumber === currentPage ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => loadPage(pageNumber)}
                                            disabled={isLoading}
                                        >
                                            {pageNumber}
                                        </Button>
                                    );
                                })}
                            </div>

                            {/* Version mobile - juste la page courante */}
                            <div className="sm:hidden flex items-center">
                                <Button
                                    variant="default"
                                    size="sm"
                                    disabled={true}
                                    className="px-3"
                                >
                                    {currentPage} / {totalPages}
                                </Button>
                            </div>
                        </div>

                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => loadPage(currentPage + 1)}
                            disabled={currentPage === totalPages || isLoading}
                        >
                            <span className="mr-1 hidden sm:inline">{tDefault('pagination.next', 'Suivant')}</span>
                            <ChevronRight className="h-4 w-4" />
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
}
