import React, { useState, useEffect } from 'react';
import { 
    SunIcon, 
    MoonIcon, 
    ComputerDesktopIcon,
    ChevronDownIcon 
} from '@heroicons/react/24/outline';

type Theme = 'light' | 'dark' | 'system';

interface ThemeSelectorProps {
    className?: string;
}

export default function ThemeSelector({ className = '' }: ThemeSelectorProps) {
    const [theme, setTheme] = useState<Theme>('system');
    const [isOpen, setIsOpen] = useState(false);

    // Charger le thème depuis localStorage au montage
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme') as Theme;
        if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
            setTheme(savedTheme);
        }
    }, []);

    // Appliquer le thème
    useEffect(() => {
        const root = window.document.documentElement;
        
        if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            root.classList.remove('light', 'dark');
            root.classList.add(systemTheme);
        } else {
            root.classList.remove('light', 'dark');
            root.classList.add(theme);
        }
        
        localStorage.setItem('theme', theme);
    }, [theme]);

    // Écouter les changements de préférence système
    useEffect(() => {
        if (theme === 'system') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const handleChange = () => {
                const root = window.document.documentElement;
                const systemTheme = mediaQuery.matches ? 'dark' : 'light';
                root.classList.remove('light', 'dark');
                root.classList.add(systemTheme);
            };

            mediaQuery.addEventListener('change', handleChange);
            return () => mediaQuery.removeEventListener('change', handleChange);
        }
    }, [theme]);

    const themes = [
        {
            value: 'light' as Theme,
            label: 'Clair',
            icon: SunIcon,
        },
        {
            value: 'dark' as Theme,
            label: 'Sombre',
            icon: MoonIcon,
        },
        {
            value: 'system' as Theme,
            label: 'Système',
            icon: ComputerDesktopIcon,
        },
    ];

    const currentTheme = themes.find(t => t.value === theme);

    return (
        <div className={`relative ${className}`}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 
                         bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg 
                         hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 
                         transition-colors"
            >
                {currentTheme && (
                    <>
                        <currentTheme.icon className="w-4 h-4" />
                        <span>{currentTheme.label}</span>
                        <ChevronDownIcon className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
                    </>
                )}
            </button>

            {isOpen && (
                <>
                    {/* Overlay pour fermer le menu */}
                    <div 
                        className="fixed inset-0 z-10" 
                        onClick={() => setIsOpen(false)}
                    />
                    
                    {/* Menu déroulant */}
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 
                                  rounded-lg shadow-lg z-20 py-1">
                        {themes.map((themeOption) => (
                            <button
                                key={themeOption.value}
                                onClick={() => {
                                    setTheme(themeOption.value);
                                    setIsOpen(false);
                                }}
                                className={`w-full flex items-center space-x-3 px-4 py-2 text-sm text-left
                                          hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors
                                          ${theme === themeOption.value 
                                              ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                                              : 'text-gray-700 dark:text-gray-200'
                                          }`}
                            >
                                <themeOption.icon className="w-4 h-4" />
                                <span>{themeOption.label}</span>
                                {theme === themeOption.value && (
                                    <div className="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full" />
                                )}
                            </button>
                        ))}
                    </div>
                </>
            )}
        </div>
    );
}
