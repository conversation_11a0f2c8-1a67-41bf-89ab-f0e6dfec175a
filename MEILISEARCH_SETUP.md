# Configuration et utilisation de Meilisearch dans Lorrelei

## Installation et configuration

### 1. Installation de Meilisearch

Vous avez déjà installé les dépendances PHP nécessaires :
```bash
composer require meilisearch/meilisearch-php guzzlehttp/guzzle http-interop/http-factory-guzzle
```

### 2. Installation du serveur Meilisearch

#### Option A: Docker (Recommandé)
```bash
docker run -it --rm \
  -p 7700:7700 \
  -v $(pwd)/meili_data:/meili_data \
  getmeili/meilisearch:v1.5 \
  meilisearch --master-key="your-master-key-here"
```

#### Option B: Installation directe
Téléchargez depuis https://github.com/meilisearch/meilisearch/releases

### 3. Configuration des variables d'environnement

Ajoutez ces variables à votre fichier `.env` :
```env
MEILISEARCH_HOST=http://127.0.0.1:7700
MEILISEARCH_KEY=your-master-key-here
MEILISEARCH_PRODUITS_INDEX=produits
MEILISEARCH_CATEGORIES_INDEX=categories
MEILISEARCH_SEARCH_LIMIT=20
MEILISEARCH_FALLBACK_ENABLED=true
MEILISEARCH_TIMEOUT=5
MEILISEARCH_RETRY_ATTEMPTS=3
```

## Initialisation et indexation

### 1. Initialiser Meilisearch
```bash
php artisan meilisearch:init
```

### 2. Indexer les données existantes
```bash
# Indexer tous les produits et catégories
php artisan meilisearch:init --index-all

# Ou indexer séparément
php artisan meilisearch:index-products
php artisan meilisearch:index-categories
```

### 3. Réinitialiser les index (si nécessaire)
```bash
php artisan meilisearch:index-products --fresh
php artisan meilisearch:index-categories --fresh
```

## Fonctionnalités implémentées

### Contrôleurs modifiés

1. **ProduitController** - Toutes les méthodes utilisent maintenant Meilisearch :
   - `index()` - Liste des produits avec pagination
   - `search()` - Recherche de produits
   - `getByCategorie()` - Produits par catégorie
   - `getFeatured()` - Produits en vedette
   - `getDiscounted()` - Produits en promotion

2. **CategorieController** - Méthodes modifiées :
   - `index()` - Liste des catégories

### Synchronisation automatique

Les observers suivants maintiennent la synchronisation automatique :
- `ProduitObserver` - Synchronise les produits lors des opérations CRUD
- `CategorieObserver` - Synchronise les catégories lors des opérations CRUD

### Système de fallback

En cas d'erreur Meilisearch, le système bascule automatiquement vers la base de données traditionnelle.

## Utilisation des API

### Recherche de produits
```
GET /api/produits/search?q=terme_recherche&limit=20&page=1
```

### Filtres disponibles
- `currency` - Filtrer par devise
- `min_price` / `max_price` - Filtrer par prix
- `in_stock` - Produits en stock uniquement
- `min_rating` - Note minimale
- `on_sale` - Produits en promotion
- `sort_by` - Tri (price_asc, price_desc, name_asc, name_desc, rating, newest)

### Exemple de requête complète
```
GET /api/produits/search?q=smartphone&min_price=100&max_price=500&in_stock=true&sort_by=price_asc&limit=20&page=1
```

## Monitoring et maintenance

### Vérifier le statut de Meilisearch
```bash
curl http://127.0.0.1:7700/health
```

### Statistiques des index
```bash
curl http://127.0.0.1:7700/indexes/produits/stats
curl http://127.0.0.1:7700/indexes/categories/stats
```

### Logs
Les logs Meilisearch sont disponibles dans `storage/logs/laravel.log`

## Dépannage

### Problèmes courants

1. **Erreur de connexion** : Vérifiez que Meilisearch est démarré et accessible
2. **Index vide** : Exécutez les commandes d'indexation
3. **Résultats incohérents** : Réindexez les données avec `--fresh`

### Commandes utiles

```bash
# Vérifier la configuration
php artisan config:cache

# Réindexer complètement
php artisan meilisearch:index-products --fresh
php artisan meilisearch:index-categories --fresh

# Vider le cache
php artisan cache:clear
```

## Performance

### Optimisations recommandées

1. **Index séparés** : Produits et catégories ont leurs propres index
2. **Attributs optimisés** : Seuls les champs nécessaires sont indexés
3. **Pagination** : Limite par défaut de 20 résultats
4. **Cache** : Utilisez le cache Laravel pour les requêtes fréquentes

### Monitoring des performances

- Temps de traitement inclus dans les réponses API
- Logs détaillés des opérations Meilisearch
- Métriques de fallback pour surveiller la disponibilité

## Sécurité

1. **Clé maître** : Utilisez une clé forte en production
2. **Réseau** : Limitez l'accès au port 7700
3. **HTTPS** : Utilisez HTTPS en production
4. **Validation** : Toutes les entrées sont validées avant indexation
