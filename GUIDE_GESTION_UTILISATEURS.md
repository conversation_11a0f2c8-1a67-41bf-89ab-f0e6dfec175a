# 👥 Guide de Gestion des Utilisateurs - Système de Rôles Granulaires

## 🎯 Vue d'ensemble

Le système de gestion des utilisateurs de Lorelei permet une gestion granulaire des permissions avec deux contextes distincts :
- **Dashboard Admin** : Gestion de la plateforme
- **Dashboard Marchand** : Gestion des boutiques

## 📋 Architecture du Système

### Tables Principales
- `admin_roles` : R<PERSON>les pour le dashboard admin
- `admin_users` : Liaison utilisateurs-rôles admin
- `marchand_roles` : Rôles pour le dashboard marchand  
- `marchand_users` : Liaison utilisateurs-équipes marchand

### Enums de Permissions
- `AdminPermission` : 30+ permissions granulaires pour les admins
- `MarchandPermission` : 25+ permissions granulaires pour les marchands

## 🔧 Installation et Configuration

### 1. Exécuter les Migrations
```bash
php artisan migrate
```

### 2. Créer les Rôles par Défaut
```bash
php artisan db:seed --class=RoleSeeder
```

### 3. Assigner les Rôles aux Utilisateurs Existants
```bash
php artisan roles:assign-default
```

### 4. Nettoyer les Invitations Expirées (Optionnel)
```bash
php artisan invitations:cleanup
```

## 👨‍💼 Gestion des Utilisateurs Admin

### Rôles Admin Disponibles

#### Super Administrateur
- **Slug** : `super_admin`
- **Permissions** : Toutes les permissions
- **Usage** : Propriétaires de la plateforme

#### Administrateur
- **Slug** : `admin`
- **Permissions** : Gestion étendue (utilisateurs, marchands, finances, support)
- **Usage** : Administrateurs principaux

#### Gestionnaire Finance
- **Slug** : `finance_manager`
- **Permissions** : Finances, paiements, abonnements, rapports financiers
- **Usage** : Équipe finance

#### Support Client
- **Slug** : `support_client`
- **Permissions** : Support, litiges, avis clients, données clients
- **Usage** : Équipe support

#### Gestionnaire Marchands
- **Slug** : `merchant_manager`
- **Permissions** : Validation marchands, gestion documents, support marchands
- **Usage** : Équipe de validation des marchands

### Créer un Utilisateur Admin

1. **Via le Dashboard Admin** :
   - Aller dans `Gestion des Utilisateurs > Utilisateurs Admin`
   - Cliquer sur "Créer"
   - Remplir les informations utilisateur
   - Sélectionner le rôle et département
   - Choisir le niveau d'accès
   - Cocher "Envoyer une invitation par email" (recommandé)

2. **Via Code** :
```php
use App\Models\User;
use App\Models\AdminRole;
use App\Services\UserInvitationService;

// Créer l'utilisateur
$user = User::create([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'is_admin' => true,
]);

// Assigner le rôle admin
$adminRole = AdminRole::where('slug', 'admin')->first();
$adminUser = $user->assignAdminRole($adminRole, [
    'department' => 'support',
    'access_level' => 'full',
]);

// Envoyer l'invitation
app(UserInvitationService::class)->sendAdminInvitation($user, $adminUser);
```

## 🏪 Gestion des Équipes Marchands

### Rôles Marchand Disponibles

#### Propriétaire
- **Slug** : `owner`
- **Permissions** : Toutes les permissions de la boutique
- **Usage** : Propriétaire de la boutique

#### Gestionnaire
- **Slug** : `manager`
- **Permissions** : Gestion complète sauf suppression du compte
- **Usage** : Gestionnaire principal

#### Gestionnaire Produits
- **Slug** : `product_manager`
- **Permissions** : Produits, catalogue, promotions, analytics produits
- **Usage** : Responsable catalogue

#### Gestionnaire Commandes
- **Slug** : `order_manager`
- **Permissions** : Commandes, expéditions, support client, litiges
- **Usage** : Responsable logistique

#### Comptable
- **Slug** : `accountant`
- **Permissions** : Finances, paiements, rapports, facturation
- **Usage** : Responsable financier

#### Employé
- **Slug** : `employee`
- **Permissions** : Accès de base (consultation, traitement commandes)
- **Usage** : Employé standard

### Ajouter un Membre à l'Équipe

1. **Via le Dashboard Marchand** :
   - Aller dans `Équipe & Collaboration > Équipe`
   - Cliquer sur "Créer"
   - Remplir les informations utilisateur
   - Sélectionner le rôle et niveau d'accès
   - Cocher "Envoyer une invitation par email" (recommandé)

2. **Via Code** :
```php
use App\Models\User;
use App\Models\MarchandRole;
use App\Services\UserInvitationService;

// Créer l'utilisateur
$user = User::create([
    'name' => 'Jane Doe',
    'email' => '<EMAIL>',
]);

// Assigner le rôle marchand
$marchandRole = MarchandRole::where('slug', 'employee')->first();
$marchandUser = $user->assignMarchandRole($marchand, $marchandRole, [
    'access_level' => 'employee',
]);

// Envoyer l'invitation
app(UserInvitationService::class)->sendMarchandTeamInvitation(
    $user, 
    $marchandUser, 
    $marchand,
    auth()->user()
);
```

## 📧 Système d'Invitations

### Processus d'Invitation

1. **Création de l'utilisateur** avec token temporaire
2. **Envoi d'email d'invitation** avec lien sécurisé
3. **Acceptation par l'utilisateur** via page dédiée
4. **Configuration du mot de passe** et vérification email
5. **Redirection vers le dashboard** approprié

### Types d'Invitations

#### Invitation Admin
- **Durée** : 24 heures
- **URL** : `/admin/invitation/{user}/{token}`
- **Redirection** : Dashboard admin

#### Invitation Marchand
- **Durée** : 7 jours
- **URL** : `/marchand/invitation/{user}/{marchand}/{token}`
- **Redirection** : Dashboard marchand

### Gestion des Invitations

```php
use App\Services\UserInvitationService;

$service = app(UserInvitationService::class);

// Renvoyer une invitation
$service->resendAdminInvitation($adminUser);
$service->resendMarchandInvitation($marchandUser);

// Annuler une invitation
$service->cancelInvitation($user, 'admin');
$service->cancelInvitation($user, 'marchand');

// Nettoyer les invitations expirées
$cleaned = $service->cleanupExpiredInvitations();
```

## 🔒 Vérification des Permissions

### Dans les Contrôleurs
```php
// Vérifier permission admin
if (auth()->user()->hasAdminPermission(AdminPermission::MANAGE_USERS)) {
    // Action autorisée
}

// Vérifier permission marchand
if (auth()->user()->hasMarchandPermission($marchand, MarchandPermission::MANAGE_TEAM)) {
    // Action autorisée
}
```

### Dans les Routes
```php
// Middleware de permissions
Route::middleware(['permissions:manage_users,admin'])->group(function () {
    // Routes admin
});

Route::middleware(['permissions:manage_team,marchand'])->group(function () {
    // Routes marchand
});
```

### Dans les Resources Filament
```php
public static function canAccess(): bool
{
    return auth()->user()?->hasAdminPermission(AdminPermission::MANAGE_USERS) ?? false;
}
```

## 📊 Widgets et Statistiques

### Dashboard Admin
- **UserManagementStatsWidget** : Statistiques des utilisateurs admin, équipes marchands, invitations

### Dashboard Marchand
- **TeamManagementWidget** : Statistiques de l'équipe, invitations, connexions récentes

## 🛠️ Commandes Utiles

```bash
# Créer les rôles par défaut
php artisan db:seed --class=RoleSeeder

# Assigner les rôles aux utilisateurs existants
php artisan roles:assign-default

# Forcer la réassignation des rôles
php artisan roles:assign-default --force

# Nettoyer les invitations expirées
php artisan invitations:cleanup

# Programmer le nettoyage automatique (dans le scheduler)
# $schedule->command('invitations:cleanup')->daily();
```

## 🔧 Personnalisation

### Créer un Nouveau Rôle Admin
```php
AdminRole::create([
    'name' => 'Marketing Manager',
    'slug' => 'marketing_manager',
    'description' => 'Gestion du marketing et des promotions',
    'permissions' => [
        AdminPermission::MANAGE_PROMOTIONS->value,
        AdminPermission::MANAGE_CAMPAIGNS->value,
        AdminPermission::VIEW_MARKETING_ANALYTICS->value,
    ],
    'priority' => 40,
    'is_active' => true,
]);
```

### Créer un Nouveau Rôle Marchand
```php
MarchandRole::create([
    'name' => 'Community Manager',
    'slug' => 'community_manager',
    'description' => 'Gestion de la communauté et des réseaux sociaux',
    'permissions' => [
        MarchandPermission::MANAGE_REVIEWS->value,
        MarchandPermission::VIEW_MARKETING->value,
        MarchandPermission::CONTACT_SUPPORT->value,
    ],
    'priority' => 35,
    'is_active' => true,
]);
```

## 🚨 Bonnes Pratiques

1. **Principe du moindre privilège** : Accordez uniquement les permissions nécessaires
2. **Rôles système protégés** : Ne modifiez pas les rôles système sans précaution
3. **Invitations temporaires** : Surveillez et nettoyez régulièrement les invitations expirées
4. **Audit des permissions** : Vérifiez régulièrement les permissions accordées
5. **Tests de permissions** : Testez toujours les nouvelles permissions avant déploiement

## 📞 Support

Pour toute question sur le système de gestion des utilisateurs :
- Documentation technique : Ce fichier
- Code source : `app/Models/`, `app/Enums/`, `app/Services/UserInvitationService.php`
- Tests : Créez des tests unitaires pour vos permissions personnalisées
