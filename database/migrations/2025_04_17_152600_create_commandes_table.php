<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('commandes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('clients');
            $table->foreignId('marchand_id')->constrained('marchands');
            $table->decimal('montantTotal', 10, 2);
            $table->enum('statut', ['EnAttente', 'EnCoursDeTraitement', 'Expédié', 'Livré', 'Annulé', 'Remboursé']);
            $table->foreignId('adresse_livraison_id')->constrained('adresses');
            $table->timestamp('creeLe');
            $table->date('dateExpeditionPrevue')->nullable();
            $table->date('dateLivraisonPrevue')->nullable();
            $table->string('codeSuivi', 100)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('commandes');
    }
};
