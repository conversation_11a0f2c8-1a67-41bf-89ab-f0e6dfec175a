<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier si la table existe déjà
        if (!Schema::hasTable('conversation_messages')) {
            // Créer la table si elle n'existe pas
            Schema::create('conversation_messages', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->uuid('conversation_id');
                $table->string('auteur_type'); // 'client', 'marchand'
                $table->uuid('auteur_client_id')->nullable();
                $table->uuid('auteur_marchand_id')->nullable();
                $table->string('auteur_nom');
                $table->text('message');
                $table->string('type_message')->default('message');
                $table->json('pieces_jointes')->nullable();
                $table->boolean('lu_par_client')->default(false);
                $table->boolean('lu_par_marchand')->default(false);
                $table->timestamp('date_lecture_client')->nullable();
                $table->timestamp('date_lecture_marchand')->nullable();
                $table->uuid('reponse_a_message_id')->nullable();
                $table->boolean('important')->default(false);
                $table->boolean('notification_envoyee')->default(false);
                $table->boolean('archive')->default(false);
                $table->json('metadata')->nullable();
                $table->boolean('modere')->default(false);
                $table->string('raison_moderation')->nullable();
                $table->timestamp('date_moderation')->nullable();
                $table->timestamps();

                // Ajouter les clés étrangères avec gestion d'erreur
                try {
                    $table->foreign('conversation_id')->references('id')->on('client_marchand_conversations')->onDelete('cascade');
                } catch (\Exception $e) {
                    // Clé étrangère existe déjà ou table référencée n'existe pas
                }

                try {
                    $table->foreign('auteur_client_id')->references('id')->on('clients')->onDelete('set null');
                } catch (\Exception $e) {
                    // Clé étrangère existe déjà ou table référencée n'existe pas
                }

                try {
                    $table->foreign('auteur_marchand_id')->references('id')->on('marchands')->onDelete('set null');
                } catch (\Exception $e) {
                    // Clé étrangère existe déjà ou table référencée n'existe pas
                }
            });
        } else {
            // La table existe, vérifier si elle a les bonnes colonnes
            Schema::table('conversation_messages', function (Blueprint $table) {
                // Ajouter les colonnes manquantes si nécessaire
                if (!Schema::hasColumn('conversation_messages', 'auteur_type')) {
                    $table->string('auteur_type')->default('client');
                }
                if (!Schema::hasColumn('conversation_messages', 'auteur_client_id')) {
                    $table->uuid('auteur_client_id')->nullable();
                }
                if (!Schema::hasColumn('conversation_messages', 'auteur_marchand_id')) {
                    $table->uuid('auteur_marchand_id')->nullable();
                }
                if (!Schema::hasColumn('conversation_messages', 'auteur_nom')) {
                    $table->string('auteur_nom')->nullable();
                }
                if (!Schema::hasColumn('conversation_messages', 'type_message')) {
                    $table->string('type_message')->default('message');
                }
                if (!Schema::hasColumn('conversation_messages', 'pieces_jointes')) {
                    $table->json('pieces_jointes')->nullable();
                }
                if (!Schema::hasColumn('conversation_messages', 'lu_par_client')) {
                    $table->boolean('lu_par_client')->default(false);
                }
                if (!Schema::hasColumn('conversation_messages', 'lu_par_marchand')) {
                    $table->boolean('lu_par_marchand')->default(false);
                }
                if (!Schema::hasColumn('conversation_messages', 'date_lecture_client')) {
                    $table->timestamp('date_lecture_client')->nullable();
                }
                if (!Schema::hasColumn('conversation_messages', 'date_lecture_marchand')) {
                    $table->timestamp('date_lecture_marchand')->nullable();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversation_messages');
    }
};
