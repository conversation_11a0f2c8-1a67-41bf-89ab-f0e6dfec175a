<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter la colonne avatar à la table users
        Schema::table('users', function (Blueprint $table) {
            $table->string('avatar')->nullable()->after('email');
        });

        // Ajouter la colonne logo à la table marchands
        Schema::table('marchands', function (Blueprint $table) {
            $table->string('logo')->nullable()->after('nomEntreprise');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('avatar');
        });

        Schema::table('marchands', function (Blueprint $table) {
            $table->dropColumn('logo');
        });
    }
};
