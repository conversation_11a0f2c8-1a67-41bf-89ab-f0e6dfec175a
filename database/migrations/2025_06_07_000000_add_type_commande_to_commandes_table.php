<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier si la colonne existe déjà
        if (!Schema::hasColumn('commandes', 'type_commande')) {
            Schema::table('commandes', function (Blueprint $table) {
                $table->string('type_commande')->default('legacy')->after('statut');
            });
        }

        // Ajouter les index avec gestion d'erreur
        try {
            Schema::table('commandes', function (Blueprint $table) {
                $table->index(['type_commande', 'statut'], 'idx_commandes_type_statut');
            });
        } catch (\Exception $e) {
            // Index existe déjà, on continue
        }

        try {
            Schema::table('commandes', function (Blueprint $table) {
                $table->index(['marchand_id', 'type_commande'], 'idx_commandes_marchand_type');
            });
        } catch (\Exception $e) {
            // Index existe déjà, on continue
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('commandes', function (Blueprint $table) {
            $table->dropIndex(['type_commande', 'statut']);
            $table->dropIndex(['marchand_id', 'type_commande']);
            $table->dropColumn('type_commande');
        });
    }
};
