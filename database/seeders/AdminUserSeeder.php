<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur administrateur par défaut
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrateur Lorelei',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]
        );

        // Créer un utilisateur marchand de test
        $marchandUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Marchand Test',
                'email' => '<EMAIL>',
                'password' => Hash::make('marchand123'),
                'is_admin' => false,
                'email_verified_at' => now(),
            ]
        );

        // <PERSON><PERSON>er le profil marchand associé
        if ($marchandUser) {
            \App\Models\Marchand::updateOrCreate(
                ['user_id' => $marchandUser->id],
                [
                    'user_id' => $marchandUser->id,
                    'nomEntreprise' => 'Boutique Test',
                    'pays_business' => 'CM',
                    'ville_business' => 'Douala',
                    'type_business' => 'entreprise',
                    'statut_validation' => 'valide',
                    'etape_inscription' => 'complete',
                    'telephone_principal' => '+237 6XX XXX XXX',
                    'email_business' => '<EMAIL>',
                    'description_business' => 'Boutique de test pour démonstration',
                    'accepte_conditions' => true,
                    'accepte_newsletter' => true,
                    'langue_preferee' => 'fr',
                    'source_inscription' => 'seeder',
                ]
            );
        }

        $this->command->info('Utilisateurs créés :');
        $this->command->info('Admin: <EMAIL> / admin123');
        $this->command->info('Marchand: <EMAIL> / marchand123');
    }
}
