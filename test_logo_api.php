<?php

require_once 'vendor/autoload.php';

use App\Http\Controllers\Api\MarchandController;
use App\Models\Marchand;

// Test direct de l'API marchand pour vérifier les logos
echo "=== Test API Marchand - Logos ===\n\n";

try {
    // Récupérer un marchand avec slug
    $marchand = Marchand::where('statut_validation', 'valide')->first();
    
    if (!$marchand) {
        echo "❌ Aucun marchand validé trouvé\n";
        exit;
    }
    
    echo "✅ Marchand trouvé: {$marchand->nomEntreprise}\n";
    echo "   - ID: {$marchand->id}\n";
    echo "   - Slug: {$marchand->slug}\n";
    echo "   - Logo: " . ($marchand->logo ?? 'NULL') . "\n";
    
    // Test de l'API
    $controller = new MarchandController();
    
    if ($marchand->slug) {
        echo "\n--- Test API getBySlug ---\n";
        $response = $controller->getBySlug($marchand->slug);
        $data = json_decode($response->getContent(), true);
        
        echo "Statut: " . $response->getStatusCode() . "\n";
        
        if (isset($data['data'])) {
            echo "Logo dans réponse: " . ($data['data']['logo'] ?? 'NULL') . "\n";
            echo "Logo URL dans réponse: " . ($data['data']['logo_url'] ?? 'NULL') . "\n";
        }
        
        echo "\nRéponse complète:\n";
        echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    // Vérifier si le répertoire avatars existe
    echo "\n--- Vérification stockage ---\n";
    $storagePath = storage_path('app/public/avatars/marchand');
    echo "Répertoire avatars/marchand existe: " . (is_dir($storagePath) ? 'OUI' : 'NON') . "\n";
    
    if (is_dir($storagePath)) {
        $files = scandir($storagePath);
        $imageFiles = array_filter($files, function($file) {
            return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|webp)$/i', $file);
        });
        echo "Fichiers images trouvés: " . count($imageFiles) . "\n";
        if (count($imageFiles) > 0) {
            echo "Exemples: " . implode(', ', array_slice($imageFiles, 0, 3)) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
