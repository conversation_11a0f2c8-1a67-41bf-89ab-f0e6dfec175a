<?php
use App\Http\Controllers\Api\ReviewImageController;
use Illuminate\Support\Facades\Route;
// Routes pour les images de reviews (accessibles depuis lorrelei)
Route::post('/reviews/upload-images', [App\Http\Controllers\Api\ReviewImageController::class, 'uploadImages'])
->name('api.reviews.upload-images');
Route::delete('/reviews/delete-image', [App\Http\Controllers\Api\ReviewImageController::class, 'deleteImage'])
->name('api.reviews.delete-image');

// Routes pour les images de reviews boutiques (accessibles depuis lorrelei)
Route::post('/boutique-reviews/upload-images', [App\Http\Controllers\Api\BoutiqueReviewImageController::class, 'uploadImages'])
    ->name('api.boutique-reviews.upload-images');
Route::delete('/boutique-reviews/delete-image', [App\Http\Controllers\Api\BoutiqueReviewImageController::class, 'deleteImage'])
    ->name('api.boutique-reviews.delete-image');

// Routes pour les avatars (sans CSRF)
Route::middleware(['auth:web'])->group(function () {
    // Routes pour les avatars

});
Route::post('/avatar/user', [App\Http\Controllers\AvatarController::class, 'uploadUserAvatar'])
    ->name('api.avatar.user.upload');
Route::delete('/avatar/user', [App\Http\Controllers\AvatarController::class, 'deleteUserAvatar'])
    ->name('api.avatar.user.delete');
Route::post('/avatar/marchand', [App\Http\Controllers\AvatarController::class, 'uploadMarchandAvatar'])
    ->name('api.avatar.marchand.upload');
Route::delete('/avatar/marchand', [App\Http\Controllers\AvatarController::class, 'deleteMarchandAvatar'])
    ->name('api.avatar.marchand.delete');
Route::get('/avatar/user/{userId}', [App\Http\Controllers\AvatarController::class, 'getUserAvatar'])
    ->name('api.avatar.user.get');
Route::get('/avatar/marchand/{marchandId}', [App\Http\Controllers\AvatarController::class, 'getMarchandAvatar'])
    ->name('api.avatar.marchand.get');
// Webhook pour les notifications temps réel
Route::post('/webhook/realtime-notification', [App\Http\Controllers\Api\WebhookController::class, 'realtimeNotification']);
