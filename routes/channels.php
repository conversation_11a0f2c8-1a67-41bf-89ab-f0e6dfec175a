<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\User;
use App\Models\ClientMarchandConversation;
use App\Models\Dispute;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Canal privé pour chaque utilisateur
Broadcast::channel('user.{id}', function (User $user, int $id) {
    return (int) $user->id === $id;
});

// Canal privé pour les admins
Broadcast::channel('admin.notifications', function (User $user) {
    return $user->is_admin === true;
});

// Canal privé pour chaque marchand
Broadcast::channel('marchand.{marchandId}', function (User $user, int $marchandId) {
    return $user->marchand && $user->marchand->id === $marchandId;
});

// Canal de conversation client-marchand
Broadcast::channel('conversation.{conversationId}', function (User $user, string $conversationId) {
    $conversation = ClientMarchandConversation::find($conversationId);
    
    if (!$conversation) {
        return false;
    }
    
    // Autoriser le client propriétaire
    if ($user->client && $user->client->id === $conversation->client_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'client'];
    }
    
    // Autoriser le marchand concerné
    if ($user->marchand && $user->marchand->id === $conversation->marchand_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'marchand'];
    }
    
    // Autoriser les admins
    if ($user->is_admin) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'admin'];
    }
    
    return false;
});

// Canal de litige
Broadcast::channel('dispute.{disputeId}', function (User $user, string $disputeId) {
    $dispute = Dispute::find($disputeId);
    
    if (!$dispute) {
        return false;
    }
    
    // Autoriser le client propriétaire
    if ($user->client && $user->client->id === $dispute->client_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'client'];
    }
    
    // Autoriser le marchand concerné
    if ($user->marchand && $user->marchand->id === $dispute->marchand_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'marchand'];
    }
    
    // Autoriser les admins
    if ($user->is_admin) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'admin'];
    }
    
    return false;
});

// Canal de présence pour conversation
Broadcast::channel('presence-conversation.{conversationId}', function (User $user, string $conversationId) {
    $conversation = ClientMarchandConversation::find($conversationId);
    
    if (!$conversation) {
        return false;
    }
    
    // Même logique d'autorisation que conversation normale
    if ($user->client && $user->client->id === $conversation->client_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'client'];
    }
    
    if ($user->marchand && $user->marchand->id === $conversation->marchand_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'marchand'];
    }
    
    if ($user->is_admin) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'admin'];
    }
    
    return false;
});

// Canal de présence pour litige
Broadcast::channel('presence-dispute.{disputeId}', function (User $user, string $disputeId) {
    $dispute = Dispute::find($disputeId);
    
    if (!$dispute) {
        return false;
    }
    
    // Même logique d'autorisation que dispute normale
    if ($user->client && $user->client->id === $dispute->client_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'client'];
    }
    
    if ($user->marchand && $user->marchand->id === $dispute->marchand_id) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'marchand'];
    }
    
    if ($user->is_admin) {
        return ['id' => $user->id, 'name' => $user->name, 'type' => 'admin'];
    }
    
    return false;
});
